"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '400px',\n            width: '100%',\n            background: colors.primary || '#B31B1E',\n            borderRadius: '0px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif'),\n            aspectRatio: '3/4'\n        },\n        innerContainer: {\n            background: colors.background || '#FFFFFF',\n            margin: '20px',\n            borderRadius: '10px',\n            padding: '20px',\n            height: 'calc(100% - 40px)',\n            position: 'relative'\n        },\n        marigoldBorder: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '10px 0',\n            borderTop: \"3px solid \".concat(colors.secondary || '#FFD700'),\n            borderBottom: \"3px solid \".concat(colors.secondary || '#FFD700')\n        },\n        marigoldDot: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: colors.secondary || '#FFD700'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '20px 0',\n            background: 'transparent'\n        },\n        ganeshSymbol: {\n            width: '60px',\n            height: '60px',\n            backgroundColor: '#8A2BE2',\n            borderRadius: '8px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            margin: '0 auto 15px',\n            fontSize: '32px',\n            color: 'white'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '18px',\n            color: colors.primary || '#B31B1E',\n            fontWeight: '700',\n            marginBottom: '30px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '36px',\n            fontWeight: '700',\n            color: colors.primary || '#B31B1E',\n            textAlign: 'center',\n            margin: '20px 0',\n            lineHeight: '1.2'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '28px',\n            color: '#333',\n            margin: '0 10px',\n            fontWeight: '400'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});