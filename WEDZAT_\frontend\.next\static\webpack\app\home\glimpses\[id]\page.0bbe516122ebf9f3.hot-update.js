"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/[id]/page",{

/***/ "(app-pages-browser)/./components/HomeDashboard/Glimpses.tsx":
/*!***********************************************!*\
  !*** ./components/HomeDashboard/Glimpses.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _UserAvatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserAvatar */ \"(app-pages-browser)/./components/HomeDashboard/UserAvatar.tsx\");\n/* harmony import */ var _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/axiosConfig */ \"(app-pages-browser)/./services/axiosConfig.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../contexts/LocationContext */ \"(app-pages-browser)/./contexts/LocationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst GlimpsesSection = (param)=>{\n    let { shouldLoad = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { selectedLocation } = (0,_contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation)();\n    const [glimpses, setGlimpses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [initialLoadComplete, setInitialLoadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const loadMoreTriggerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Function to navigate to a user's profile\n    const navigateToUserProfile = (userId, username, event)=>{\n        // Stop event propagation to prevent triggering parent click events\n        if (event) {\n            event.stopPropagation();\n        }\n        // If we have a userId, navigate to that specific user's profile\n        if (userId) {\n            router.push(\"/profile/\".concat(userId));\n        } else if (username) {\n            // For now, just use the username as a parameter\n            // In a real app, you might want to fetch the user ID first\n            router.push(\"/profile/\".concat(username));\n        } else {\n            console.warn('Cannot navigate to profile: missing both userId and username');\n        }\n    };\n    // Function to fetch glimpses data\n    const fetchGlimpses = async (pageNumber, isInitialLoad)=>{\n        try {\n            // Set appropriate loading state\n            if (isInitialLoad) {\n                setLoading(true);\n            } else {\n                setLoadingMore(true);\n            }\n            console.log(\"Fetching glimpses for page \".concat(pageNumber, \"...\"));\n            // Get token from localStorage\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                console.warn(\"No authentication token found\");\n                setError(\"Authentication required\");\n                return;\n            }\n            // Build API URL with location parameter if selected\n            let apiUrl = \"/glimpses?page=\".concat(pageNumber, \"&limit=10\");\n            if (selectedLocation) {\n                apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n            }\n            // Make API request\n            const response = await _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(apiUrl, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            // Process the data\n            if (response.data && response.data.glimpses) {\n                console.log(\"Loaded \".concat(response.data.glimpses.length, \" glimpses for page \").concat(pageNumber));\n                // Log the first item for debugging\n                if (response.data.glimpses.length > 0) {\n                    console.log(\"Sample glimpse data:\", response.data.glimpses[0]);\n                }\n                // Process the response\n                const processedGlimpses = response.data.glimpses.map((glimpse)=>{\n                    if (!glimpse.thumbnail_url && glimpse.media_url && glimpse.media_url.includes('youtube')) {\n                        const videoId = getYoutubeId(glimpse.media_url);\n                        if (videoId) glimpse.thumbnail_url = \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n                    }\n                    return glimpse;\n                });\n                if (pageNumber === 1) {\n                    setGlimpses(processedGlimpses);\n                } else {\n                    setGlimpses((prev)=>[\n                            ...prev,\n                            ...processedGlimpses\n                        ]);\n                }\n                setHasMore(response.data.next_page);\n                setPage(pageNumber); // Update the current page\n                setError(null); // Clear any previous errors\n            } else {\n                console.warn(\"Unexpected response format:\", response.data);\n                setError(\"Failed to load glimpses - unexpected response format\");\n                // Don't clear existing glimpses on error for subsequent pages\n                if (pageNumber === 1) {\n                    setGlimpses([]);\n                }\n            }\n        } catch (err) {\n            console.error(\"API request failed:\", err);\n            setError(\"Failed to load glimpses\");\n            // Don't clear existing glimpses on error for subsequent pages\n            if (pageNumber === 1) {\n                setGlimpses([]);\n            }\n        } finally{\n            // Always clear loading states\n            if (isInitialLoad) {\n                setLoading(false);\n                console.log(\"Initial loading complete\");\n            } else {\n                setLoadingMore(false);\n                console.log(\"Loading more complete\");\n            }\n        }\n    };\n    // Fetch first page of glimpses as soon as the component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GlimpsesSection.useEffect\": ()=>{\n            // Only trigger when shouldLoad changes to true\n            if (shouldLoad) {\n                console.log(\"Glimpses component is now visible and ready to load data\");\n                // Set a flag to track initial load\n                setInitialLoadComplete(true);\n                // IMMEDIATE API request with no conditions or delays\n                console.log(\"Triggering initial glimpses load IMMEDIATELY...\");\n                setLoading(true);\n                // Skip the fetchGlimpses function and make the API call directly here\n                // to eliminate any potential delay or condition that might block it\n                const token = localStorage.getItem(\"token\");\n                if (token) {\n                    // Build API URL with location parameter if selected\n                    let apiUrl = \"/glimpses?page=1&limit=10\";\n                    if (selectedLocation) {\n                        apiUrl += \"&location=\".concat(encodeURIComponent(selectedLocation));\n                    }\n                    // Make direct API request\n                    console.log(\"Making direct API request for glimpses page 1...\");\n                    _services_axiosConfig__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(apiUrl, {\n                        headers: {\n                            Authorization: \"Bearer \".concat(token)\n                        }\n                    }).then({\n                        \"GlimpsesSection.useEffect\": (response)=>{\n                            console.log(\"API response received for glimpses page 1\");\n                            if (response.data && response.data.glimpses) {\n                                setGlimpses(response.data.glimpses);\n                                setHasMore(response.data.next_page);\n                                setPage(1);\n                                setError(null);\n                            } else {\n                                setGlimpses([]);\n                                setError(\"No glimpses found\");\n                            }\n                        }\n                    }[\"GlimpsesSection.useEffect\"]).catch({\n                        \"GlimpsesSection.useEffect\": (err)=>{\n                            console.error(\"Direct API request failed:\", err);\n                            setError(\"Failed to load glimpses\");\n                            setGlimpses([]);\n                        }\n                    }[\"GlimpsesSection.useEffect\"]).finally({\n                        \"GlimpsesSection.useEffect\": ()=>{\n                            setLoading(false);\n                            console.log(\"Initial loading complete\");\n                        }\n                    }[\"GlimpsesSection.useEffect\"]);\n                } else {\n                    setLoading(false);\n                    setError(\"Authentication required\");\n                }\n            }\n        }\n    }[\"GlimpsesSection.useEffect\"], [\n        shouldLoad\n    ]); // Only depend on shouldLoad\n    // Failsafe to ensure content is loaded - only show error after timeout\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GlimpsesSection.useEffect\": ()=>{\n            // If we're stuck in loading state for more than 5 seconds, show error\n            let timeoutId = null;\n            if (loading && initialLoadComplete) {\n                timeoutId = setTimeout({\n                    \"GlimpsesSection.useEffect\": ()=>{\n                        console.log(\"Loading timeout reached - API request may have failed\");\n                        setLoading(false);\n                        if (glimpses.length === 0) {\n                            setError(\"Unable to load glimpses. Please check your network connection.\");\n                        }\n                    }\n                }[\"GlimpsesSection.useEffect\"], 10000); // Increased timeout to 10 seconds for slow networks\n            }\n            return ({\n                \"GlimpsesSection.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"GlimpsesSection.useEffect\"];\n        }\n    }[\"GlimpsesSection.useEffect\"], [\n        loading,\n        initialLoadComplete,\n        glimpses.length\n    ]);\n    // Setup Intersection Observer for horizontal lazy loading\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"GlimpsesSection.useEffect\": ()=>{\n            // Only set up observer after initial load and if there's more content to fetch\n            if (!initialLoadComplete || !hasMore) return;\n            // Disconnect previous observer if it exists\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n                console.log(\"Disconnected previous intersection observer\");\n            }\n            // Create new observer - similar to the pattern used in Flashes and WeddingVideos\n            observerRef.current = new IntersectionObserver({\n                \"GlimpsesSection.useEffect\": (entries)=>{\n                    var _entries_;\n                    // If the trigger element is visible and we're not already loading\n                    if (((_entries_ = entries[0]) === null || _entries_ === void 0 ? void 0 : _entries_.isIntersecting) && !loading && !loadingMore && hasMore) {\n                        console.log(\"Load more trigger is visible, loading next page...\");\n                        console.log(\"Intersection ratio:\", entries[0].intersectionRatio);\n                        const nextPage = page + 1;\n                        fetchGlimpses(nextPage, false);\n                    }\n                }\n            }[\"GlimpsesSection.useEffect\"], // Use the scroll container as the root for the intersection observer\n            {\n                root: scrollContainerRef.current,\n                threshold: 0.1,\n                rootMargin: \"0px 150px 0px 0px\"\n            });\n            // Start observing the trigger element\n            if (loadMoreTriggerRef.current) {\n                observerRef.current.observe(loadMoreTriggerRef.current);\n                console.log(\"Now observing load more trigger element\");\n            } else {\n                console.warn(\"Load more trigger element not found\");\n            }\n            return ({\n                \"GlimpsesSection.useEffect\": ()=>{\n                    if (observerRef.current) {\n                        observerRef.current.disconnect();\n                        console.log(\"Disconnected intersection observer\");\n                    }\n                }\n            })[\"GlimpsesSection.useEffect\"];\n        }\n    }[\"GlimpsesSection.useEffect\"], [\n        hasMore,\n        loading,\n        loadingMore,\n        page,\n        initialLoadComplete\n    ]);\n    // Get appropriate image source for a glimpse\n    const getImageSource = (glimpse)=>{\n        if (glimpse.thumbnail_url) {\n            return glimpse.thumbnail_url;\n        }\n        // If it's a YouTube video, use the YouTube thumbnail\n        if (glimpse.media_url && glimpse.media_url.includes('youtube')) {\n            const videoId = getYoutubeId(glimpse.media_url);\n            if (videoId) {\n                return \"https://img.youtube.com/vi/\".concat(videoId, \"/hqdefault.jpg\");\n            }\n        }\n        return \"/pics/placeholder.svg\";\n    };\n    // Extract YouTube video ID from URL if needed\n    const getYoutubeId = (url)=>{\n        if (!url) return '';\n        // If it's already just an ID, return it\n        if (url.length < 20 && !url.includes('/')) return url;\n        // Try to extract ID from YouTube URL\n        const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n        const match = url.match(regExp);\n        return match && match[2].length === 11 ? match[2] : '';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-83037452c623c470\" + \" \" + \"mb-4 bg-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center  mb-4 px-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"font-inter text-[20px] leading-[18px] tracking-[0%] align-middle font-semibold text-[#000000] \",\n                        children: \"Glimpses\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/home/<USER>",\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1\",\n                        children: \"See all\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-600\",\n                        children: \"Loading glimpses...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined),\n            error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"text-red-500 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            console.log(\"Retrying glimpses load...\");\n                            setError(null);\n                            fetchGlimpses(1, true);\n                        },\n                        className: \"jsx-83037452c623c470\" + \" \" + \"px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, undefined),\n            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: scrollContainerRef,\n                style: {\n                    scrollBehavior: \"smooth\",\n                    WebkitOverflowScrolling: \"touch\",\n                    minHeight: glimpses.length === 0 && !error ? \"220px\" : \"auto\"\n                },\n                className: \"jsx-83037452c623c470\" + \" \" + \"overflow-x-auto scrollbar-hide relative\",\n                children: [\n                    glimpses.length === 0 && !error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex items-center justify-center h-[220px] w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-83037452c623c470\" + \" \" + \"text-gray-400\",\n                            children: \"No glimpses available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-83037452c623c470\" + \" \" + \"flex gap-3 pb-4 flex-nowrap\",\n                        children: [\n                            glimpses.map((glimpse, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: \"linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100.01%)\"\n                                    },\n                                    className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 rounded-[10px] overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer border border-[#4D0C0D] w-[80vw] sm:w-[40vw] md:w-[30vw] lg:w-[22vw] h-[160px] sm:h-[180px] md:h-[200px] lg:h-[220px] max-w-[365px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: (e)=>navigateToUserProfile(glimpse.user_id, glimpse.user_name, e),\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute top-4 left-4 flex items-center space-x-2 z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserAvatar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    username: glimpse.user_name || \"user\",\n                                                    avatar: glimpse.user_avatar || undefined,\n                                                    size: \"sm\",\n                                                    isGradientBorder: true,\n                                                    onClick: (e)=>navigateToUserProfile(glimpse.user_id, glimpse.user_name, e)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: (e)=>navigateToUserProfile(glimpse.user_id, glimpse.user_name, e),\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-white text-sm font-medium drop-shadow-md cursor-pointer hover:underline\",\n                                                    children: glimpse.user_name || \"user\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"h-full relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        top: 0,\n                                                        left: 0,\n                                                        right: 0,\n                                                        bottom: 0,\n                                                        zIndex: 1\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"relative w-full h-full rounded-[4px] overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: getImageSource(glimpse),\n                                                            alt: glimpse.caption || \"Glimpse Video\",\n                                                            fill: true,\n                                                            sizes: \"(max-width: 640px) 85vw, (max-width: 768px) 45vw, (max-width: 1024px) 30vw, 25vw\",\n                                                            className: \"object-cover\",\n                                                            ...index < 2 ? {\n                                                                priority: true\n                                                            } : {\n                                                                loading: \"lazy\"\n                                                            },\n                                                            unoptimized: true,\n                                                            placeholder: \"blur\",\n                                                            blurDataURL: \"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg==\",\n                                                            onError: (e)=>{\n                                                                const imgElement = e.target;\n                                                                if (imgElement) {\n                                                                    imgElement.src = \"/pics/placeholder.svg\";\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        top: 0,\n                                                        left: 0,\n                                                        right: 0,\n                                                        bottom: 0,\n                                                        zIndex: 2,\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        cursor: \"pointer\",\n                                                        backgroundColor: \"rgba(0, 0, 0, 0.2)\"\n                                                    },\n                                                    onClick: ()=>{\n                                                        if (glimpse.media_id) {\n                                                            window.location.href = \"/home/<USER>/\".concat(glimpse.media_id);\n                                                        }\n                                                    },\n                                                    className: \"jsx-83037452c623c470\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-83037452c623c470\" + \" \" + \"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"28\",\n                                                            height: \"28\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"white\",\n                                                            className: \"jsx-83037452c623c470\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5v14l11-7z\",\n                                                                className: \"jsx-83037452c623c470\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-83037452c623c470\" + \" \" + \"absolute bottom-0 left-0 right-0 p-2 sm:p-3 md:p-4 bg-gradient-to-t from-black/80 to-transparent text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"font-medium text-xs sm:text-sm md:text-base line-clamp-1\",\n                                                    children: glimpse.caption\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-83037452c623c470\" + \" \" + \"text-[10px] sm:text-xs\",\n                                                    children: [\n                                                        glimpse.views_count ? \"\".concat((glimpse.views_count / 1000).toFixed(1), \"K views\") : \"\",\n                                                        glimpse.likes_count ? \" • \".concat((glimpse.likes_count / 1000).toFixed(1), \"K likes\") : \"\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(glimpse.media_id, \"-\").concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, undefined)),\n                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: loadMoreTriggerRef,\n                                style: {\n                                    position: \"relative\",\n                                    // Add debug outline in development\n                                    outline:  true ? \"1px dashed rgba(255, 0, 0, 0.3)\" : 0\n                                },\n                                \"aria-hidden\": \"true\",\n                                \"data-testid\": \"glimpses-load-more-trigger\",\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 w-10 h-full opacity-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, undefined),\n                            loadingMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-83037452c623c470\" + \" \" + \"flex-shrink-0 flex items-center justify-center min-w-[100px] h-[160px] sm:h-[180px] md:h-[200px] lg:h-[220px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-83037452c623c470\" + \" \" + \"ml-2 text-sm text-gray-600\",\n                                        children: \"Loading more...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, undefined),\n            !loading && glimpses.length === 0 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-83037452c623c470\" + \" \" + \"py-10 text-center text-gray-500\",\n                children: \"No glimpses available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n                lineNumber: 542,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"83037452c623c470\",\n                children: \".scrollbar-hide.jsx-83037452c623c470::-webkit-scrollbar{display:none}.scrollbar-hide.jsx-83037452c623c470{-ms-overflow-style:none;scrollbar-width:none}\"\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\HomeDashboard\\\\Glimpses.tsx\",\n        lineNumber: 327,\n        columnNumber: 5\n    }, undefined);\n};\n_s(GlimpsesSection, \"QTu5wlXS3mG6DUvj9dS3nzTVsqk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_7__.useLocation\n    ];\n});\n_c = GlimpsesSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GlimpsesSection);\nvar _c;\n$RefreshReg$(_c, \"GlimpsesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/HomeDashboard/Glimpses.tsx\n"));

/***/ })

});