"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import VideoVendorDetails from "../../../components/VideoVendorDetails";

// Updated interface for glimpse items from new API
interface Glimpse {
  media_id: string;
  media_url: string;
  thumbnail_url?: string;
  caption?: string;
  location?: string;
  event_type?: string;
  video_type?: string;
  partner?: string;
  wedding_style?: string;
  budget?: string;
  created_at: string;
  user_id: string;
  user_name?: string;
  user_avatar?: string | null;
  is_own_content?: boolean;
  followers_count?: number;
  following_count?: number;
  is_following?: boolean;
  views_count?: number;
  likes_count?: number;
  is_liked?: boolean;
}

interface ApiResponse {
  glimpses: Glimpse[];
  next_page: boolean;
  total_count: number;
  current_page: number;
  filtered_by_location?: string;
  location_matches_in_page?: number;
}

export default function GlimpsesPage() {
  const [glimpses, setGlimpses] = useState<Glimpse[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [newItemsCount, setNewItemsCount] = useState<number>(0);

  useEffect(() => setIsClient(true), []);

  // Fetch glimpses from the API
  useEffect(() => {
    const fetchGlimpses = async () => {
      try {
        setLoading(true);
        // Get token from localStorage - try multiple possible keys
        const token = typeof window !== 'undefined' ?
          (localStorage.getItem('token') ||
            localStorage.getItem('jwt_token') ||
            localStorage.getItem('auth_token')) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          return;
        }

        console.log(`Fetching glimpses for page ${page}`);
        const response = await axios.get<ApiResponse>(`/glimpses?page=${page}&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.glimpses) {
          console.log(`Glimpses API response for page ${page}:`, response.data);
          console.log(`Loaded ${response.data.glimpses.length} glimpses for page ${page}`);

          // Process the response to ensure all required fields are present
          const processedGlimpses = response.data.glimpses.map(glimpse => {
            // Fallback for missing thumbnail
            if (!glimpse.thumbnail_url && glimpse.media_url && glimpse.media_url.includes('youtube')) {
              const videoId = getYoutubeId(glimpse.media_url);
              if (videoId) {
                glimpse.thumbnail_url = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
              }
            }
            return glimpse;
          });

          if (page === 1) {
            // For first page, just set the glimpses directly
            setGlimpses(processedGlimpses);
            setNewItemsCount(0); // Reset new items count for first page
            console.log(`Set initial ${processedGlimpses.length} glimpses`);
          } else {
            // For subsequent pages, append to existing glimpses
            setGlimpses(prev => {
              const newGlimpses = [...prev, ...processedGlimpses];
              console.log(`Added ${processedGlimpses.length} new glimpses, total now: ${newGlimpses.length}`);
              return newGlimpses;
            });
            // Set the count of newly loaded items
            setNewItemsCount(processedGlimpses.length);
          }

          setHasMore(response.data.next_page);
          console.log(`Has more pages: ${response.data.next_page}`);
          console.log(`Current page: ${response.data.current_page}, Total items: ${response.data.total_count}`);
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load glimpses');
        }
      } catch (err) {
        console.error('Error fetching glimpses:', err);
        setError('Failed to load glimpses');
      } finally {
        setLoading(false);
      }
    };

    fetchGlimpses();
  }, [page]);

  // Reference for the last item in the list
  const lastGlimpseRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Reset new items notification after a few seconds
  useEffect(() => {
    if (newItemsCount > 0) {
      const timer = setTimeout(() => {
        setNewItemsCount(0);
      }, 5000); // Reset after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [newItemsCount]);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last glimpse is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.1, rootMargin: '0px 0px 500px 0px' } // Load when item is just 10% visible or 500px before it comes into view
    );

    // Get the last item element
    const lastElement = lastGlimpseRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, glimpses.length]);

  // Get image source for a glimpse
  const getImageSource = (glimpse: Glimpse): string => {
    // If we have a thumbnail from API, use it
    if (glimpse.thumbnail_url) {
      return glimpse.thumbnail_url;
    }

    // If it's a YouTube video, use the YouTube thumbnail
    if (glimpse.media_url && glimpse.media_url.includes('youtube')) {
      const videoId = getYoutubeId(glimpse.media_url);
      if (videoId) {
        return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
      }
    }

    // Default fallback - use a local placeholder image
    return '/pics/placeholder.svg';
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Glimpses</h1>
              <div className="text-sm text-gray-500">
                Showing {glimpses.length} items | Page {page}
              </div>
            </div>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading glimpses</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {glimpses.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {glimpses.map((glimpse, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === glimpses.length - 1;

                  // Determine if this is a newly loaded item
                  const isNewlyLoaded = page > 1 && index >= glimpses.length - newItemsCount;

                  return (
                  <div
                    key={`${glimpse.media_id}-${index}`}
                    // Apply ref to the last item for intersection observer
                    ref={isLastItem ? lastGlimpseRef : null}
                    className={`rounded-[10px] overflow-hidden shadow-md relative hover:scale-105 transition-transform cursor-pointer border ${isNewlyLoaded ? 'border-green-500 animate-pulse' : 'border-[#4D0C0D]'}`}
                    style={{
                      height: "220px",
                      background:
                        "linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100.01%)",
                    }}
                  >
                    <div className="absolute top-4 left-4 flex items-center space-x-2 z-10">
                      <UserAvatar
                        username={glimpse.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                        imageUrl={glimpse.user_avatar || undefined}
                      />
                      <span className="text-white text-sm font-medium drop-shadow-md">
                        {glimpse.user_name || "user"}
                      </span>
                    </div>

                    {/* Video Thumbnail */}
                    <div className="h-full relative">
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          zIndex: 1,
                        }}
                      >
                        <div className="relative w-full h-full rounded-[4px] overflow-hidden">
                          <Image
                            src={getImageSource(glimpse)}
                            alt={glimpse.caption || "Glimpse Video"}
                            fill
                            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                            className="object-cover"
                            priority={index < 6} // Only prioritize the first six images
                            unoptimized={true} // Skip optimization for external images
                            onError={(e) => {
                              console.error(`Failed to load thumbnail for glimpse: ${glimpse.caption}`);
                              // Use placeholder as fallback
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement) {
                                imgElement.src = '/pics/placeholder.svg';
                              }
                            }}
                          />
                        </div>
                      </div>

                      {/* Play Button Overlay */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          zIndex: 2,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          cursor: "pointer",
                          backgroundColor: "rgba(0, 0, 0, 0.2)",
                        }}
                        onClick={() => {
                          // Navigate to the glimpse detail page
                          if (glimpse.media_id) {
                            console.log(`Navigating to glimpse detail: ${glimpse.media_id}`);
                            window.location.href = `/home/<USER>/${glimpse.media_id}`;
                          }
                        }}
                      >
                        <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28"
                            height="28"
                            viewBox="0 0 24 24"
                            fill="white"
                          >
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 md:p-4 bg-gradient-to-t from-black/80 to-transparent text-white">
                      <div className="font-medium text-xs sm:text-sm md:text-base line-clamp-1">{glimpse.caption}</div>
                      <div className="text-[10px] sm:text-xs">
                        {glimpse.views_count ? `${(glimpse.views_count / 1000).toFixed(1)}K views` : ''}
                        {glimpse.likes_count ? ` • ${(glimpse.likes_count / 1000).toFixed(1)}K likes` : ''}
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* New items loaded notification */}
            {!loading && newItemsCount > 0 && (
              <div className="py-3 text-center text-green-600 font-medium animate-pulse">
                {newItemsCount} new items loaded! Look for the green borders.
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && glimpses.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more glimpses to load</div>
            )}

            {/* No content state */}
            {!loading && glimpses.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No glimpses available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
