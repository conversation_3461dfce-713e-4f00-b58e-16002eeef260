// components/upload/UploadButton.tsx
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import {  PlusCircle,
  Camera,
  Video,
  Image,
  Upload,
  Film,
  Radio,
  Heart,
  Check,
  ArrowRight, } from 'lucide-react';
import { uploadService } from '../../services/api';
import { uploadToS3 } from '../../utils/directS3Upload';
import PhotoUploadModal from './PhotoUploadModal';
import VideoUploadModal from './VideoUploadModal';
import Webcam from 'react-webcam';
import { multipartUpload, UploadedPart } from '../../utils/s3MultipartUpload';

interface UploadButtonProps {
  contentType?: 'moments' | 'photos' | 'videos';
  variant?: 'default' | 'icon' | 'text';
  className?: string;
  children?: React.ReactNode;
}
// (Removed duplicate WebcamWrapper declaration)
const uploadTypes = [
  {
    key: "flashes",
    label: "Flashes",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.75 28.5171H11.25C4.4625 28.5171 1.5625 25.6171 1.5625 18.8296V11.3296C1.5625 4.54209 4.4625 1.64209 11.25 1.64209H18.75C25.5375 1.64209 28.4375 4.54209 28.4375 11.3296V18.8296C28.4375 25.6171 25.5375 28.5171 18.75 28.5171ZM11.25 3.51709C5.4875 3.51709 3.4375 5.56709 3.4375 11.3296V18.8296C3.4375 24.5921 5.4875 26.6421 11.25 26.6421H18.75C24.5125 26.6421 26.5625 24.5921 26.5625 18.8296V11.3296C26.5625 5.56709 24.5125 3.51709 18.75 3.51709H11.25Z"
          fill="#292D32"
        />
        <path
          d="M8.6123 27.867C8.0998 27.867 7.6748 27.442 7.6748 26.9295V3.22949C7.6748 2.71699 8.0998 2.29199 8.6123 2.29199C9.1248 2.29199 9.5498 2.70449 9.5498 3.22949V26.9295C9.5498 27.4545 9.1248 27.867 8.6123 27.867Z"
          fill="#292D32"
        />
        <path
          d="M21.1123 27.867C20.5998 27.867 20.1748 27.442 20.1748 26.9295V3.22949C20.1748 2.71699 20.5998 2.29199 21.1123 2.29199C21.6248 2.29199 22.0498 2.71699 22.0498 3.22949V26.9295C22.0498 27.4545 21.6248 27.867 21.1123 27.867Z"
          fill="#292D32"
        />
        <path
          d="M8.6123 9.72949H3.1748C2.6623 9.72949 2.2373 9.30449 2.2373 8.79199C2.2373 8.27949 2.6623 7.85449 3.1748 7.85449H8.6123C9.1248 7.85449 9.5498 8.27949 9.5498 8.79199C9.5498 9.30449 9.1248 9.72949 8.6123 9.72949Z"
          fill="#292D32"
        />
        <path
          d="M8.6126 16.0171H2.5376C2.0251 16.0171 1.6001 15.5921 1.6001 15.0796C1.6001 14.5671 2.0251 14.1421 2.5376 14.1421H8.6126C9.1251 14.1421 9.5501 14.5671 9.5501 15.0796C9.5501 15.5921 9.1251 16.0171 8.6126 16.0171Z"
          fill="#292D32"
        />
        <path
          d="M8.6126 22.2295H3.1001C2.5876 22.2295 2.1626 21.8045 2.1626 21.292C2.1626 20.7795 2.5876 20.3545 3.1001 20.3545H8.6126C9.1251 20.3545 9.5501 20.7795 9.5501 21.292C9.5501 21.8045 9.1251 22.2295 8.6126 22.2295Z"
          fill="#292D32"
        />
        <path
          d="M27.3623 9.72949H21.9248C21.4123 9.72949 20.9873 9.30449 20.9873 8.79199C20.9873 8.27949 21.4123 7.85449 21.9248 7.85449H27.3623C27.8748 7.85449 28.2998 8.27949 28.2998 8.79199C28.2998 9.30449 27.8748 9.72949 27.3623 9.72949Z"
          fill="#292D32"
        />
        <path
          d="M27.3626 16.0171H21.2876C20.7751 16.0171 20.3501 15.5921 20.3501 15.0796C20.3501 14.5671 20.7751 14.1421 21.2876 14.1421H27.3626C27.8751 14.1421 28.3001 14.5671 28.3001 15.0796C28.3001 15.5921 27.8751 16.0171 27.3626 16.0171Z"
          fill="#292D32"
        />
        <path
          d="M21.2124 16.0171H7.4624C6.9499 16.0171 6.5249 15.5921 6.5249 15.0796C6.5249 14.5671 6.9499 14.1421 7.4624 14.1421H21.2124C21.7249 14.1421 22.1499 14.5671 22.1499 15.0796C22.1499 15.5921 21.7374 16.0171 21.2124 16.0171Z"
          fill="#292D32"
        />
        <path
          d="M27.3626 22.2295H21.8501C21.3376 22.2295 20.9126 21.8045 20.9126 21.292C20.9126 20.7795 21.3376 20.3545 21.8501 20.3545H27.3626C27.8751 20.3545 28.3001 20.7795 28.3001 21.292C28.3001 21.8045 27.8751 22.2295 27.3626 22.2295Z"
          fill="#292D32"
        />
      </svg>
    ),
    tooltip: "Only videos less than 90 seconds",
  },
  {
    key: "glimpses",
    label: "Glimpses",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.1094 26.0257C21.0219 26.0257 18.4719 24.6882 17.6844 22.2507C17.1469 20.5632 17.7719 18.3507 19.7219 17.7257C20.5469 17.4632 21.4094 17.5757 22.0969 18.0007C22.7719 17.5757 23.6594 17.4507 24.4969 17.7257C26.4469 18.3507 27.0719 20.5632 26.5344 22.2507C25.7469 24.7382 23.0719 26.0257 22.1094 26.0257ZM19.4719 21.6757C20.0094 23.3382 21.8094 24.1132 22.1344 24.1507C22.4969 24.1132 24.2594 23.2382 24.7469 21.6757C25.0094 20.8382 24.7469 19.7757 23.9219 19.5007C23.5719 19.3882 23.1094 19.4507 22.8969 19.7757C22.7219 20.0382 22.4344 20.2007 22.1344 20.2007C21.8469 20.2007 21.5344 20.0632 21.3594 19.8132C21.0969 19.4507 20.6344 19.4007 20.3094 19.5007C19.4719 19.7757 19.2094 20.8382 19.4719 21.6757Z"
          fill="black"
        />
        <path
          d="M10.8594 18.5257C9.77193 18.5257 7.22193 17.1882 6.43443 14.7507C5.89693 13.0632 6.52193 10.8507 8.47193 10.2257C9.29693 9.96324 10.1594 10.0757 10.8469 10.5007C11.5219 10.0757 12.4094 9.95074 13.2469 10.2257C15.1969 10.8507 15.8219 13.0632 15.2844 14.7507C14.4969 17.2382 11.8219 18.5257 10.8594 18.5257ZM8.22193 14.1757C8.75943 15.8382 10.5594 16.6132 10.8844 16.6507C11.2469 16.6132 13.0094 15.7382 13.4969 14.1757C13.7594 13.3382 13.4969 12.2757 12.6719 12.0007C12.3219 11.8882 11.8594 11.9507 11.6469 12.2757C11.4719 12.5382 11.1844 12.7007 10.8844 12.7007C10.5969 12.7007 10.2844 12.5632 10.1094 12.3132C9.84693 11.9507 9.38443 11.9007 9.05943 12.0007C8.22193 12.2757 7.95943 13.3382 8.22193 14.1757Z"
          fill="black"
        />
        <path
          d="M15 27.1422C14.6125 27.1422 14.225 27.0921 13.9125 26.9796C10.7125 25.8921 1.5625 20.5921 1.5625 10.9421C1.5625 6.56712 5.1 3.01709 9.45 3.01709C11.525 3.01709 13.5375 3.85462 15 5.31712C16.4625 3.85462 18.475 3.01709 20.55 3.01709C24.9 3.01709 28.4375 6.56712 28.4375 10.9421C28.4375 13.3796 27.85 15.7171 26.6875 17.9046C26.55 18.1671 26.2875 18.3547 25.9875 18.3922C25.6875 18.4297 25.3875 18.3296 25.175 18.1046C24.2 17.0796 22.9 16.5046 21.5 16.5046C18.7125 16.5046 16.4375 18.7796 16.4375 21.5671C16.4375 22.8046 16.9 24.0046 17.7375 24.9421C17.9375 25.1671 18.0125 25.4671 17.95 25.7546C17.8875 26.0421 17.6875 26.2921 17.425 26.4171C16.925 26.6421 16.475 26.8296 16.0625 26.9671C15.7625 27.0921 15.3875 27.1422 15 27.1422ZM9.45 4.89209C6.1375 4.89209 3.4375 7.60462 3.4375 10.9421C3.4375 19.4921 11.65 24.2171 14.525 25.2046C14.7625 25.2921 15.2375 25.2796 15.4625 25.2046C15.5 25.1921 15.5375 25.1796 15.575 25.1671C14.9125 24.0921 14.5625 22.8546 14.5625 21.5796C14.5625 17.7546 17.675 14.6422 21.5 14.6422C22.95 14.6422 24.375 15.1046 25.55 15.9546C26.225 14.3546 26.5625 12.6796 26.5625 10.9421C26.5625 7.60462 23.8625 4.89209 20.55 4.89209C18.675 4.89209 16.875 5.79216 15.75 7.30466C15.4 7.77966 14.6 7.77966 14.25 7.30466C13.125 5.79216 11.325 4.89209 9.45 4.89209Z"
          fill="black"
        />
      </svg>
    ),
    tooltip: "Only videos less 7 minutes",
  },
  {
    key: "movies",
    label: "Movies",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.75 28.5171H11.25C4.4625 28.5171 1.5625 25.6171 1.5625 18.8296V11.3296C1.5625 4.54209 4.4625 1.64209 11.25 1.64209H18.75C25.5375 1.64209 28.4375 4.54209 28.4375 11.3296V18.8296C28.4375 25.6171 25.5375 28.5171 18.75 28.5171ZM11.25 3.51709C5.4875 3.51709 3.4375 5.56709 3.4375 11.3296V18.8296C3.4375 24.5921 5.4875 26.6421 11.25 26.6421H18.75C24.5125 26.6421 26.5625 24.5921 26.5625 18.8296V11.3296C26.5625 5.56709 24.5125 3.51709 18.75 3.51709H11.25Z"
          fill="#292D32"
        />
        <path
          d="M26.8499 9.90454H3.1499C2.6374 9.90454 2.2124 9.47954 2.2124 8.96704C2.2124 8.45454 2.6249 8.02954 3.1499 8.02954H26.8499C27.3624 8.02954 27.7874 8.45454 27.7874 8.96704C27.7874 9.47954 27.3749 9.90454 26.8499 9.90454Z"
          fill="#292D32"
        />
        <path
          d="M10.6499 9.72954C10.1374 9.72954 9.7124 9.30454 9.7124 8.79204V2.71704C9.7124 2.20454 10.1374 1.77954 10.6499 1.77954C11.1624 1.77954 11.5874 2.20454 11.5874 2.71704V8.79204C11.5874 9.30454 11.1624 9.72954 10.6499 9.72954Z"
          fill="#292D32"
        />
        <path
          d="M19.3501 9.16704C18.8376 9.16704 18.4126 8.74204 18.4126 8.22954V2.71704C18.4126 2.20454 18.8376 1.77954 19.3501 1.77954C19.8626 1.77954 20.2876 2.20454 20.2876 2.71704V8.22954C20.2876 8.75454 19.8751 9.16704 19.3501 9.16704Z"
          fill="#292D32"
        />
        <path
          d="M15 22.5796C13.8204 22.5796 11.0543 21.194 10.2001 18.6688C9.61701 16.9206 10.295 14.6285 12.4102 13.981C13.3051 13.709 14.2407 13.8256 14.9864 14.2659C15.7186 13.8256 16.6813 13.6961 17.5898 13.981C19.705 14.6285 20.383 16.9206 19.7999 18.6688C18.9457 21.2458 16.0441 22.5796 15 22.5796ZM12.139 18.0731C12.7221 19.7954 14.6746 20.5983 15.0271 20.6371C15.4203 20.5983 17.3322 19.6918 17.861 18.0731C18.1457 17.2055 17.861 16.1047 16.9661 15.8198C16.5864 15.7033 16.0847 15.7681 15.8542 16.1047C15.6644 16.3767 15.3525 16.545 15.0271 16.545C14.7153 16.545 14.3763 16.4026 14.1865 16.1436C13.9017 15.7681 13.4 15.7163 13.0475 15.8198C12.139 16.1047 11.8543 17.2055 12.139 18.0731Z"
          fill="#292D32"
        />
      </svg>
    ),
    tooltip: "Only videos less than 1 hour",
  },
  {
    key: "photos",
    label: "Photos",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.25 13.5171C9.35 13.5171 7.8125 11.9796 7.8125 10.0796C7.8125 8.17959 9.35 6.64209 11.25 6.64209C13.15 6.64209 14.6875 8.17959 14.6875 10.0796C14.6875 11.9796 13.15 13.5171 11.25 13.5171ZM11.25 8.51709C10.3875 8.51709 9.6875 9.21709 9.6875 10.0796C9.6875 10.9421 10.3875 11.6421 11.25 11.6421C12.1125 11.6421 12.8125 10.9421 12.8125 10.0796C12.8125 9.21709 12.1125 8.51709 11.25 8.51709Z"
          fill="#292D32"
        />
        <path
          d="M18.75 28.5171H11.25C4.4625 28.5171 1.5625 25.6171 1.5625 18.8296V11.3296C1.5625 4.54209 4.4625 1.64209 11.25 1.64209H15C15.5125 1.64209 15.9375 2.06709 15.9375 2.57959C15.9375 3.09209 15.5125 3.51709 15 3.51709H11.25C5.4875 3.51709 3.4375 5.56709 3.4375 11.3296V18.8296C3.4375 24.5921 5.4875 26.6421 11.25 26.6421H18.75C24.5125 26.6421 26.5625 24.5921 26.5625 18.8296V13.8296C26.5625 13.3171 26.9875 12.8921 27.5 12.8921C28.0125 12.8921 28.4375 13.3171 28.4375 13.8296V18.8296C28.4375 25.6171 25.5375 28.5171 18.75 28.5171Z"
          fill="#292D32"
        />
        <path
          d="M23.6749 10.5296C22.5874 10.5296 20.0374 9.19214 19.2499 6.75464C18.7124 5.06714 19.3374 2.85464 21.2874 2.22964C22.1124 1.96714 22.9749 2.07964 23.6624 2.50464C24.3374 2.07964 25.2249 1.95464 26.0624 2.22964C28.0124 2.85464 28.6374 5.06714 28.0999 6.75464C27.3124 9.24214 24.6374 10.5296 23.6749 10.5296ZM21.0374 6.17964C21.5749 7.84214 23.3749 8.61714 23.6999 8.65464C24.0624 8.61714 25.8249 7.74214 26.3124 6.17964C26.5749 5.34214 26.3124 4.27964 25.4874 4.00464C25.1374 3.89214 24.6749 3.95464 24.4624 4.27964C24.2874 4.54214 23.9999 4.70464 23.6999 4.70464C23.4124 4.70464 23.0999 4.56714 22.9249 4.31714C22.6624 3.95464 22.1999 3.90464 21.8749 4.00464C21.0374 4.27964 20.7749 5.34214 21.0374 6.17964Z"
          fill="#292D32"
        />
        <path
          d="M3.33746 24.7045C3.03746 24.7045 2.73746 24.5545 2.56246 24.292C2.27496 23.867 2.38746 23.2795 2.82496 22.992L8.98746 18.8545C10.3375 17.942 12.2 18.0545 13.425 19.092L13.8375 19.4545C14.4625 19.992 15.525 19.992 16.1375 19.4545L21.3375 14.992C22.6625 13.8545 24.75 13.8545 26.0875 14.992L28.125 16.742C28.5125 17.0795 28.5625 17.667 28.225 18.067C27.8875 18.4545 27.3 18.5045 26.9 18.167L24.8625 16.417C24.2375 15.8795 23.175 15.8795 22.55 16.417L17.35 20.8795C16.025 22.017 13.9375 22.017 12.6 20.8795L12.1875 20.517C11.6125 20.0295 10.6625 19.9795 10.025 20.417L3.86246 24.5545C3.69996 24.6545 3.51246 24.7045 3.33746 24.7045Z"
          fill="#292D32"
        />
      </svg>
    ),
  },
  {
    key: "live",
    label: "Live",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.171 19.5505C14.9934 19.5505 14.8158 19.5209 14.6579 19.4716C13.3553 19.0275 10 17.0637 10 13.3927C10 11.6163 11.4408 10.1755 13.2072 10.1755C13.9276 10.1755 14.6184 10.4223 15.1809 10.8565C15.7434 10.4223 16.4342 10.1755 17.1546 10.1755C18.9211 10.1755 20.3618 11.6163 20.3618 13.3927C20.3618 17.0637 16.9967 19.0275 15.7039 19.4716C15.5263 19.5209 15.3487 19.5505 15.171 19.5505ZM13.1974 11.6558C12.25 11.6558 11.4704 12.4354 11.4704 13.3927C11.4704 16.1854 14.1842 17.7446 15.1316 18.0703C15.1414 18.0703 15.1809 18.0703 15.2105 18.0703C16.1678 17.7347 18.8618 16.1756 18.8618 13.4025C18.8618 12.4453 18.0921 11.6657 17.1348 11.6657C16.5921 11.6657 16.0888 11.9223 15.7533 12.3565C15.477 12.7315 14.8454 12.7315 14.5691 12.3565C14.2533 11.9124 13.75 11.6558 13.1974 11.6558Z"
          fill="#292D32"
        />
        <path
          d="M26.2504 23.5172C26.0504 23.5172 25.8628 23.4547 25.6878 23.3297C25.2753 23.0172 25.1878 22.4297 25.5003 22.0172C27.0128 20.0047 27.8129 17.6047 27.8129 15.0797C27.8129 12.5547 27.0128 10.1547 25.5003 8.14221C25.1878 7.72971 25.2753 7.14219 25.6878 6.82969C26.1003 6.51719 26.6879 6.60468 27.0004 7.01718C28.7629 9.35468 29.6879 12.1422 29.6879 15.0797C29.6879 18.0172 28.7629 20.8047 27.0004 23.1422C26.8129 23.3922 26.5379 23.5172 26.2504 23.5172Z"
          fill="#292D32"
        />
        <path
          d="M3.75 23.5172C3.4625 23.5172 3.18749 23.3922 2.99999 23.1422C1.23749 20.8047 0.3125 18.0172 0.3125 15.0797C0.3125 12.1422 1.23749 9.35468 2.99999 7.01718C3.31249 6.60468 3.90002 6.51719 4.31252 6.82969C4.72502 7.14219 4.81251 7.72971 4.50001 8.14221C2.98751 10.1547 2.1875 12.5547 2.1875 15.0797C2.1875 17.6047 2.98751 20.0047 4.50001 22.0172C4.81251 22.4297 4.72502 23.0172 4.31252 23.3297C4.15002 23.4547 3.95 23.5172 3.75 23.5172Z"
          fill="#292D32"
        />
        <path
          d="M22.2503 20.517C22.0503 20.517 21.8628 20.4545 21.6878 20.3295C21.2753 20.017 21.1878 19.4294 21.5003 19.0169C22.3628 17.8794 22.8128 16.5169 22.8128 15.0794C22.8128 13.6419 22.3628 12.2795 21.5003 11.142C21.1878 10.7295 21.2753 10.1419 21.6878 9.82943C22.1003 9.51693 22.6878 9.60446 23.0003 10.017C24.1003 11.492 24.6878 13.2419 24.6878 15.0794C24.6878 16.9169 24.1003 18.6794 23.0003 20.1419C22.8128 20.3919 22.5378 20.517 22.2503 20.517Z"
          fill="#292D32"
        />
        <path
          d="M7.75002 20.517C7.46252 20.517 7.18751 20.3919 7.00001 20.1419C5.90001 18.6669 5.3125 16.9169 5.3125 15.0794C5.3125 13.2419 5.90001 11.4795 7.00001 10.017C7.31251 9.60446 7.89999 9.51693 8.31249 9.82943C8.72499 10.1419 8.81248 10.7295 8.49998 11.142C7.63748 12.2795 7.1875 13.6419 7.1875 15.0794C7.1875 16.5169 7.63748 17.8794 8.49998 19.0169C8.81248 19.4294 8.72499 20.017 8.31249 20.3295C8.14999 20.4545 7.95002 20.517 7.75002 20.517Z"
          fill="#292D32"
        />
      </svg>
    ),
  },
  {
    key: "moments",
    label: "Moments",
    icon: (
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.65 24.7671C13.2875 24.7671 12.9375 24.7171 12.65 24.6046C8.37499 23.1421 1.5625 17.9171 1.5625 10.1921C1.5625 6.22958 4.76249 3.01709 8.69999 3.01709C10.5875 3.01709 12.35 3.74207 13.6625 5.02957C14.9875 3.74207 16.7375 3.01709 18.625 3.01709C22.5625 3.01709 25.7625 6.22958 25.7625 10.1921C25.7625 10.7296 25.725 11.2796 25.65 11.8421C25.6125 12.1296 25.4375 12.3921 25.1875 12.5421C24.9375 12.6921 24.625 12.7046 24.35 12.5921C22.8125 11.9296 20.9 12.4296 19.9 13.7671C19.725 14.0046 19.3875 14.1296 19.15 14.1421C18.85 14.1421 18.575 14.0046 18.4 13.7671C17.6875 12.8171 16.6 12.2796 15.425 12.2796C13.3875 12.2796 11.725 13.9546 11.725 16.0171C11.725 19.2671 13.5875 21.5796 15.1625 22.9671C15.3875 23.1671 15.5 23.4546 15.475 23.7546C15.45 24.0546 15.2875 24.3171 15.025 24.4796C14.8875 24.5546 14.775 24.6046 14.7125 24.6171C14.3875 24.7171 14.025 24.7671 13.65 24.7671ZM8.69999 4.89209C5.79999 4.89209 3.4375 7.26708 3.4375 10.1921C3.4375 16.7296 8.87501 20.7546 12 22.3046C10.8125 20.7171 9.83749 18.6171 9.83749 16.0046C9.83749 12.9046 12.3375 10.3921 15.4125 10.3921C16.8125 10.3921 18.125 10.9046 19.1375 11.8171C20.4 10.6921 22.2 10.1796 23.875 10.4921C23.875 10.3921 23.875 10.2921 23.875 10.2046C23.875 7.27959 21.5125 4.90456 18.6125 4.90456C16.95 4.90456 15.4125 5.67957 14.4 7.01707C14.225 7.25457 13.95 7.39209 13.65 7.39209C13.35 7.39209 13.075 7.25457 12.9 7.01707C11.9125 5.66707 10.375 4.89209 8.69999 4.89209Z"
          fill="#292D32"
        />
        <path
          d="M19.1503 27.1421C18.8628 27.1421 18.5628 27.1046 18.3253 27.0171C17.2503 26.6546 15.5128 25.7671 13.9253 24.3546C12.0753 22.7171 9.86279 19.9421 9.86279 16.0046C9.86279 12.9046 12.3628 10.3921 15.4378 10.3921C16.8378 10.3921 18.1503 10.9046 19.1628 11.8171C20.7378 10.4046 23.1378 10.0046 25.1128 10.8671C27.1378 11.7671 28.4503 13.7921 28.4503 16.0171C28.4503 21.9421 23.2503 25.9171 19.9753 27.0296C19.7253 27.1046 19.4378 27.1421 19.1503 27.1421ZM15.4253 12.2671C13.3878 12.2671 11.7253 13.9421 11.7253 16.0046C11.7253 19.2546 13.5878 21.5671 15.1628 22.9546C16.5503 24.1796 18.0128 24.9421 18.9253 25.2421C19.0253 25.2796 19.2628 25.2796 19.3503 25.2421C21.2253 24.6046 26.5628 21.5421 26.5628 16.0046C26.5628 14.5171 25.6878 13.1671 24.3503 12.5796C22.8378 11.9171 20.9003 12.4171 19.9003 13.7546C19.7253 13.9921 19.3878 14.1171 19.1503 14.1296C18.8503 14.1296 18.5753 13.9921 18.4003 13.7546C17.6878 12.8046 16.6003 12.2671 15.4253 12.2671Z"
          fill="#292D32"
        />
      </svg>
    ),
    tooltip: "Photos or videos less than 60 seconds",
  },
];

// Reusable FaceVerificationModal
const FaceVerificationModal = ({ open, onVerified, onClose }: { open: boolean, onVerified: () => void, onClose: () => void }) => {
  const webcamRef = React.useRef<any>(null);
  const [capturing, setCapturing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const capture = React.useCallback(async () => {
    if (!webcamRef.current) return;
    setLoading(true);
    setError(null);
    const imageSrc = webcamRef.current.getScreenshot();
    if (!imageSrc) {
      setError('Could not capture image. Please try again.');
      setLoading(false);
      return;
    }
    try {
      const token = localStorage.getItem('token');
      const res = await fetch('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/verify-face', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ face_image: imageSrc })
      });
      if (res.status === 200) {
        onVerified();
      } else {
        const data = await res.json();
        setError(data.error || 'Face verification failed. Try again.');
      }
    } catch (e: any) {
      setError('Face verification failed. Try again.');
    } finally {
      setLoading(false);
    }
  }, [onVerified]);
    if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
      <div
        style={{
          position: "relative",
          width: "606px",
          height: "663.08px",
          background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
          borderRadius: "20px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "40px",
          gap: "60px",
          isolation: "isolate",
        }}
      >
        {/* Close button */}
        <button
          className="absolute"
          style={{
            top: "20px",
            right: "20px",
            width: "24px",
            height: "24px",
            zIndex: 1,
          }}
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
              stroke="#292D32"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Main content container */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "0px",
            gap: "40px",
            width: "526px",
            height: "583.08px",
            flex: "none",
            order: 0,
            alignSelf: "stretch",
            flexGrow: 0,
            zIndex: 0,
          }}
        >
          {/* Header section */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "20px",
              width: "526px",
              height: "87.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Title row with logo and text */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "20px",
                width: "280px",
                height: "37.08px",
                flex: "none",
                order: 0,
                flexGrow: 0,
              }}
            >
              {/* Heart logo */}
              <div
                style={{
                  width: "47px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="47"
                  height="38"
                  viewBox="0 0 47 38"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                    fill="#B31B1E"
                  />
                </svg>
              </div>

              {/* Title and icon */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "10px",
                  width: "213px",
                  height: "27px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <h2
                  style={{
                    width: "179px",
                    height: "27px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 600,
                    fontSize: "22px",
                    lineHeight: "27px",
                    textAlign: "center",
                    color: "#000000",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Face Verification
                </h2>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.1205 14.0698C12.1005 14.0698 12.0705 14.0698 12.0505 14.0698C12.0205 14.0698 11.9805 14.0698 11.9505 14.0698C9.68047 13.9998 7.98047 12.2298 7.98047 10.0498C7.98047 7.82978 9.79047 6.01978 12.0105 6.01978C14.2305 6.01978 16.0405 7.82978 16.0405 10.0498C16.0305 12.2398 14.3205 13.9998 12.1505 14.0698C12.1305 14.0698 12.1305 14.0698 12.1205 14.0698ZM12.0005 7.50978C10.6005 7.50978 9.47047 8.64978 9.47047 10.0398C9.47047 11.4098 10.5405 12.5198 11.9005 12.5698C11.9305 12.5598 12.0305 12.5598 12.1305 12.5698C13.4705 12.4998 14.5205 11.3998 14.5305 10.0398C14.5305 8.64978 13.4005 7.50978 12.0005 7.50978Z"
                      fill="#292D32"
                    />
                    <path
                      d="M11.9998 23.2899C9.30984 23.2899 6.73984 22.2899 4.74984 20.4699C4.56984 20.3099 4.48984 20.0699 4.50984 19.8399C4.63984 18.6499 5.37984 17.5399 6.60984 16.7199C9.58984 14.7399 14.4198 14.7399 17.3898 16.7199C18.6198 17.5499 19.3598 18.6499 19.4898 19.8399C19.5198 20.0799 19.4298 20.3099 19.2498 20.4699C17.2598 22.2899 14.6898 23.2899 11.9998 23.2899ZM6.07984 19.6399C7.73984 21.0299 9.82984 21.7899 11.9998 21.7899C14.1698 21.7899 16.2598 21.0299 17.9198 19.6399C17.7398 19.0299 17.2598 18.4399 16.5498 17.9599C14.0898 16.3199 9.91984 16.3199 7.43984 17.9599C6.72984 18.4399 6.25984 19.0299 6.07984 19.6399Z"
                      fill="#292D32"
                    />
                    <path
                      d="M12 23.2898C6.07 23.2898 1.25 18.4698 1.25 12.5398C1.25 6.6098 6.07 1.78979 12 1.78979C17.93 1.78979 22.75 6.6098 22.75 12.5398C22.75 18.4698 17.93 23.2898 12 23.2898ZM12 3.28979C6.9 3.28979 2.75 7.4398 2.75 12.5398C2.75 17.6398 6.9 21.7898 12 21.7898C17.1 21.7898 21.25 17.6398 21.25 12.5398C21.25 7.4398 17.1 3.28979 12 3.28979Z"
                      fill="#292D32"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Subtitle */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "8px",
                width: "526px",
                height: "30px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 12.8296C8.83 12.8296 6.25 10.2496 6.25 7.07959C6.25 3.90959 8.83 1.32959 12 1.32959C15.17 1.32959 17.75 3.90959 17.75 7.07959C17.75 10.2496 15.17 12.8296 12 12.8296ZM12 2.82959C9.66 2.82959 7.75 4.73959 7.75 7.07959C7.75 9.41959 9.66 11.3296 12 11.3296C14.34 11.3296 16.25 9.41959 16.25 7.07959C16.25 4.73959 14.34 2.82959 12 2.82959Z"
                    fill="#292D32"
                  />
                  <path
                    d="M3.41016 22.8296C3.00016 22.8296 2.66016 22.4896 2.66016 22.0796C2.66016 17.8096 6.85015 14.3296 12.0002 14.3296C13.0102 14.3296 14.0001 14.4596 14.9601 14.7296C15.3601 14.8396 15.5902 15.2496 15.4802 15.6496C15.3702 16.0496 14.9601 16.2796 14.5602 16.1696C13.7402 15.9396 12.8802 15.8296 12.0002 15.8296C7.68015 15.8296 4.16016 18.6296 4.16016 22.0796C4.16016 22.4896 3.82016 22.8296 3.41016 22.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M18 22.8296C16.34 22.8296 14.78 21.9496 13.94 20.5196C13.49 19.7996 13.25 18.9496 13.25 18.0796C13.25 16.6196 13.9 15.2696 15.03 14.3696C15.87 13.6996 16.93 13.3296 18 13.3296C20.62 13.3296 22.75 15.4596 22.75 18.0796C22.75 18.9496 22.51 19.7996 22.06 20.5296C21.81 20.9496 21.49 21.3296 21.11 21.6496C20.28 22.4096 19.17 22.8296 18 22.8296ZM18 14.8296C17.26 14.8296 16.56 15.0796 15.97 15.5496C15.2 16.1596 14.75 17.0896 14.75 18.0796C14.75 18.6696 14.91 19.2496 15.22 19.7496C15.8 20.7296 16.87 21.3296 18 21.3296C18.79 21.3296 19.55 21.0396 20.13 20.5196C20.39 20.2996 20.61 20.0396 20.77 19.7596C21.09 19.2496 21.25 18.6696 21.25 18.0796C21.25 16.2896 19.79 14.8296 18 14.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M17.4299 19.8196C17.2399 19.8196 17.0499 19.7497 16.8999 19.5997L15.9099 18.6097C15.6199 18.3197 15.6199 17.8396 15.9099 17.5496C16.1999 17.2596 16.6799 17.2596 16.9699 17.5496L17.4499 18.0297L19.0499 16.5496C19.3499 16.2696 19.8299 16.2897 20.1099 16.5897C20.3899 16.8897 20.3699 17.3697 20.0699 17.6497L17.9399 19.6196C17.7899 19.7496 17.6099 19.8196 17.4299 19.8196Z"
                    fill="#292D32"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "157px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Verify your face ID
              </span>
            </div>
          </div>

          {/* Camera section */}
          <div
            style={{
              boxSizing: "border-box",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              padding: "20px 40px",
              gap: "8px",
              width: "526px",
              height: "354px",
              border: "1px solid #D9D9D9",
              borderRadius: "20px",
              flex: "none",
              order: 1,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Camera circle */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                padding: "90px 40px",
                gap: "8px",
                width: "204px",
                height: "204px",
                border: "1px solid #D9D9D9",
                borderRadius: "130px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                position: "relative",
                overflow: "hidden",
              }}
            >
              <WebcamWrapper
                audio={false}
                ref={webcamRef}
                screenshotFormat="image/jpeg"
                width={204}
                height={204}
                videoConstraints={{ facingMode: "user" }}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  borderRadius: "130px",
                }}
              />
            </div>

            {/* Open Camera text */}
            <span
              style={{
                width: "92px",
                height: "17px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "17px",
                color: "#262626",
                flex: "none",
                order: 1,
                flexGrow: 0,
              }}
            >
              Open Camera
            </span>

            {/* Description text */}
            <p
              style={{
                width: "372px",
                height: "56px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 300,
                fontSize: "18px",
                lineHeight: "28px",
                textAlign: "center",
                color: "#000000",
                flex: "none",
                order: 1,
                flexGrow: 0,
                marginTop: "16px",
              }}
            >
              This is a one-time face verification process. It won't appear
              again next time.
            </p>

            {error && (
              <div
                style={{
                  color: "#B31B1E",
                  fontSize: "14px",
                  textAlign: "center",
                  marginTop: "8px",
                }}
              >
                {error}
              </div>
            )}
          </div>

          {/* Bottom buttons */}
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "526px",
              height: "62px",
              flex: "none",
              order: 2,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Back button */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "143px",
                height: "62px",
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                cursor: "pointer",
              }}
              onClick={onClose}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  transform: "rotate(-180deg)",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ transform: "rotate(-180deg)" }}
                >
                  <path
                    d="M11.47 21.58C11.01 21.58 10.57 21.39 10.25 21.07L3.18 14C2.53 13.35 2.53 12.31 3.18 11.66L10.25 4.59C10.9 3.94 11.94 3.94 12.59 4.59C13.24 5.24 13.24 6.28 12.59 6.93L7.66 11.86L20.5 11.86C21.33 11.86 22 12.53 22 13.36C22 14.19 21.33 14.86 20.5 14.86L7.66 14.86L12.59 19.79C13.24 20.44 13.24 21.48 12.59 22.13C12.27 22.45 11.83 22.64 11.47 21.58Z"
                    fill="#262626"
                  />
                  <path
                    d="M11.46 12.17L20.5 12.17C20.91 12.17 21.25 12.53 21.25 12.94C21.25 13.35 20.91 13.71 20.5 13.71L11.46 13.71C11.05 13.71 10.71 13.35 10.71 12.94C10.71 12.53 11.05 12.17 11.46 12.17Z"
                    fill="#262626"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "49px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#292D32",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Back
              </span>
            </div>

            {/* Upload button */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "164px",
                height: "62px",
                background: loading ? "#999999" : "#B31B1E",
                borderRadius: "10px",
                flex: "none",
                order: 1,
                flexGrow: 0,
                cursor: loading ? "not-allowed" : "pointer",
              }}
              onClick={loading ? undefined : capture}
            >
              <span
                style={{
                  width: "70px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#FDFCFF",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {loading ? "Verifying..." : "Upload"}
              </span>
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.43 18.9C14.24 18.9 14.05 18.83 13.9 18.68C13.61 18.39 13.61 17.91 13.9 17.62L19.44 12.08L13.9 6.54C13.61 6.25 13.61 5.77 13.9 5.48C14.19 5.19 14.67 5.19 14.96 5.48L21.03 11.55C21.32 11.84 21.32 12.32 21.03 12.61L14.96 18.68C14.81 18.83 14.62 18.9 14.43 18.9Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M20.33 12.83H3.5C3.09 12.83 2.75 12.49 2.75 12.08C2.75 11.67 3.09 11.33 3.5 11.33H20.33C20.74 11.33 21.08 11.67 21.08 12.08C21.08 12.49 20.74 12.83 20.33 12.83Z"
                    fill="#FFFFFF"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Wrapper to fix JSX/TS compatibility
const WebcamWrapper = React.forwardRef<any, any>((props, ref) => React.createElement(Webcam as any, { ref, ...props }));

const UploadButton: React.FC<UploadButtonProps> = ({ 
  contentType, 
  variant = 'default',
  className = '',
  children 
}) => {
  const [showModal, setShowModal] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('');
  const [step, setStep] = useState<'type' | 'file' | 'thumbnail' | 'face' | 'photo-confirm'>('type');
  const [file, setFile] = useState<File | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isVideo, setIsVideo] = useState(false);
  const [thumbnail, setThumbnail] = useState<string | null>(null);
  const [faceVerified, setFaceVerified] = useState<boolean | null>(null);
  const [checkingFace, setCheckingFace] = useState(false);
  const [customThumbnail, setCustomThumbnail] = useState<File | null>(null);
  const [customThumbnailUrl, setCustomThumbnailUrl] = useState<string | null>(null);
  const [autoThumbnailUrl, setAutoThumbnailUrl] = useState<string | null>(null);
  const [photoConfirm, setPhotoConfirm] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [videoSubtype, setVideoSubtype] = useState<string | null>(null);
  const [videoDurationError, setVideoDurationError] = useState<string | null>(null);
  const [faceModalOpen, setFaceModalOpen] = useState(false);
  const [userFaceVerified, setUserFaceVerified] = useState<boolean | null>(null);

  const handleClick = () => {
    setShowModal(true);
    setStep('type');
    setSelectedType('');
    setFile(null);
    setFileUrl(null);
    setIsVideo(false);
    setThumbnail(null);
    setFaceVerified(null);
  };

  const handleClose = () => {
    setShowModal(false);
    setSelectedType('');
    setStep('type');
    setFile(null);
    setFileUrl(null);
    setIsVideo(false);
    setThumbnail(null);
    setFaceVerified(null);
  };

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
  };

  const handleNext = () => {
    if (selectedType === 'moments') {
      setStep('file');
    } else if (selectedType === 'photos') {
      setShowModal(false);
      setShowPhotoModal(true);
    } else if (['flashes', 'glimpses', 'movies'].includes(selectedType)) {
      setShowModal(false);
      setVideoSubtype(selectedType);
      setShowVideoModal(true);
    }
    // (Other types can be handled here in the future)
  };

  // Step 2: File upload logic
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setFile(f);
    setFileUrl(URL.createObjectURL(f));
    const isVid = f.type.startsWith('video/');
    setIsVideo(isVid);
    setPhotoConfirm(false);
    setVideoDurationError(null);
    if (isVid && selectedType === 'moments') {
      // Check video duration before proceeding
      const video = document.createElement('video');
      video.preload = 'metadata';
      video.src = URL.createObjectURL(f);
      video.onloadedmetadata = () => {
        if (video.duration > 60) {
          setVideoDurationError('Please upload a video less than 1 minute for Moments.');
          setFile(null);
          setFileUrl(null);
          setIsVideo(false);
          return;
        }
        setStep('thumbnail');
      };
      return;
    }
    if (isVid) {
      setStep('thumbnail');
    } else {
      setStep('photo-confirm');
    }
  };

  // Step 3: Thumbnail selection logic (placeholder)
  const handleThumbnailSelect = (thumb: string) => {
    setThumbnail(thumb);
    setStep('face');
  };

  // Step 3b: Autogenerate thumbnail (random frame, placeholder logic)
  const handleAutoThumbnail = () => {
    // For now, just use the first frame as a placeholder
    if (fileUrl) setThumbnail(fileUrl);
    setStep('face');
  };

  // Handle custom thumbnail upload
  const handleCustomThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setCustomThumbnail(f);
    setCustomThumbnailUrl(URL.createObjectURL(f));
  };

  // Step 4: Face verification check
  useEffect(() => {
    if (step === 'face' && faceVerified === null) {
      setCheckingFace(true);
      fetch('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user', {
        headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      })
        .then(res => res.json())
        .then(data => {
          setFaceVerified(!!data.face_verified);
        })
        .catch(() => setFaceVerified(false))
        .finally(() => setCheckingFace(false));
    }
  }, [step, faceVerified]);

  // Step 4b: Face verification UI (placeholder)
  const handleFaceVerification = () => {
    // Placeholder: mark as verified
    setFaceVerified(true);
    // Here you would trigger the real face verification flow
  };

  // When entering the thumbnail step, auto-select the auto-generated thumbnail
  useEffect(() => {
    if (step === 'thumbnail' && fileUrl && !thumbnail) {
      setThumbnail(fileUrl);
    }
  }, [step, fileUrl, thumbnail]);

  // Extract the first frame of the video as an image for the auto-generated thumbnail
  useEffect(() => {
    if (step === 'thumbnail' && fileUrl && isVideo) {
      const video = document.createElement('video');
      video.src = fileUrl;
      video.crossOrigin = 'anonymous';
      // Try to use 5s, fallback to 3s, fallback to 0.1s if video is too short
      video.muted = true;
      video.playsInline = true;
      video.addEventListener('loadedmetadata', () => {
        let seekTime = 5;
        if (video.duration < 5) {
          seekTime = video.duration > 3 ? 3 : 0.1;
        }
        video.currentTime = seekTime;
      }, { once: true });
      video.addEventListener('seeked', () => {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          const dataUrl = canvas.toDataURL('image/jpeg');
          setAutoThumbnailUrl(dataUrl);
          setThumbnail(dataUrl); // Select auto thumbnail by default
        }
      }, { once: true });
    }
  }, [step, fileUrl, isVideo]);

  // Helper to reset all upload state
  const resetUploadState = () => {
    setShowModal(false);
    setStep('type');
    setSelectedType('');
    setFile(null);
    setFileUrl(null);
    setIsVideo(false);
    setThumbnail(null);
    setFaceVerified(null);
    setCustomThumbnail(null);
    setCustomThumbnailUrl(null);
    setAutoThumbnailUrl(null);
    setPhotoConfirm(false);
    setUploading(false);
    setProgress(0);
    setUploadError(null);
    setUploadSuccess(false);
  };

  // Main upload logic after face verification
  const startUpload = async () => {
    if (!file) return;
    setUploading(true);
    setProgress(5);
    setUploadError(null);
    setUploadSuccess(false);
    try {
      // 1. Prepare request for getPresignedUrl
      const isVid = file.type.startsWith('video/');
      const media_type = isVid ? 'video' : 'photo';
      // Map frontend type to backend subtype
      let media_subtype: string = 'post';
      let is_story = false;
      if (selectedType === 'moments') {
        is_story = true;
        // Do not set media_subtype for moments
      } else {
        if (selectedType === 'flashes') media_subtype = 'flash';
        if (selectedType === 'glimpses') media_subtype = 'glimpse';
        if (selectedType === 'movies') media_subtype = 'movie';
      }
      // For demo, use 'my_wedding' as video_category
      const video_category = isVid ? 'my_wedding' : undefined;
      setProgress(10);
      // 2. Get presigned URL
      let presigned;
      if (selectedType === 'moments') {
        presigned = await uploadService.getPresignedUrl({
          media_type,
          is_story: true,
          video_category: null,
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        } as any);
      } else if (selectedType === 'photos') {
        presigned = await uploadService.getPresignedUrl({
          media_type,
          media_subtype: null,
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        });
      } else {
        presigned = await uploadService.getPresignedUrl({
          media_type,
          media_subtype,
          ...(video_category ? { video_category } : {}),
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        });
      }
      setProgress(20);
      // 3. Upload main file to S3 (multipart)
      let uploadedParts: UploadedPart[] = [];
      if (presigned.multipart && presigned.part_urls && presigned.upload_id) {
        // Multipart upload
        uploadedParts = await multipartUpload(
          file,
          presigned.part_urls,
          (progress) => setProgress(20 + Math.round(progress.percentage * 0.5))
        );
        // 4. Complete multipart upload
        await uploadService.completeMultipartUpload({
          media_id: presigned.media_id,
          upload_id: presigned.upload_id,
          parts: uploadedParts,
        });
      }
      // 5. Upload thumbnail if video and custom thumbnail selected (single PUT)
      const thumbnailUploadUrl = presigned.thumbnail_url;
      if (isVid && thumbnail && thumbnailUploadUrl) {
        let thumbFile: File | null = null;
        if (thumbnail === autoThumbnailUrl) {
          const arr = thumbnail.split(','), mimeMatch = arr[0].match(/:(.*?);/), mime = mimeMatch ? mimeMatch[1] : null, bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
          for (let i = 0; i < n; i++) u8arr[i] = bstr.charCodeAt(i);
          if (mime) {
            thumbFile = new File([u8arr], 'thumbnail.jpg', { type: mime });
          }
        } else if (thumbnail === customThumbnailUrl && customThumbnail) {
          thumbFile = customThumbnail;
        }
        if (thumbFile) {
          await uploadToS3(
            thumbnailUploadUrl,
            thumbFile,
            (p) => setProgress(70 + Math.round((p * 0.2)))
          );
        }
      }
      setProgress(90);
      // 6. Call completeUpload
      let completeReq: any;
      if (selectedType === 'moments') {
        completeReq = {
          media_id: presigned.media_id,
          media_type: media_type as 'photo' | 'video',
          media_subtype: null,
          is_story: true,
          // No title field for moments
          // Add more fields as needed (caption, place, etc.)
        };
      } else if (selectedType === 'photos') {
        completeReq = {
          media_id: presigned.media_id,
          media_type: media_type as 'photo',
          media_subtype: null,
          // No title field for photos
          // Add more fields as needed (caption, place, etc.)
        };
      } else {
        completeReq = {
          media_id: presigned.media_id,
          media_type: media_type as 'photo' | 'video',
          media_subtype,
          title: file.name,
          // Add more fields as needed (caption, place, etc.)
        };
      }
      await uploadService.completeUpload(completeReq);
      setProgress(100);
      setUploadSuccess(true);
      setTimeout(() => {
        setUploading(false);
        setShowModal(false);
        resetUploadState();
      }, 1200);
    } catch (err: any) {
      setUploading(false);
      setUploadError(err?.error || err?.message || 'Upload failed.');
    }
  };

  // When face is verified, start upload
  useEffect(() => {
    if (step === 'face' && faceVerified) {
      startUpload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [step, faceVerified]);

  // On mount, fetch user face verification status
  useEffect(() => {
    fetch('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user', {
      headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
    })
      .then(res => res.json())
      .then(data => setUserFaceVerified(!!data.face_verified))
      .catch(() => setUserFaceVerified(false));
  }, []);

  const getIcon = () => {
    if (contentType === 'moments') return <Camera className="w-5 h-5" />;
    if (contentType === 'photos') return <Image className="w-5 h-5" />;
    if (contentType === 'videos') return <Video className="w-5 h-5" />;
    return <PlusCircle className="w-5 h-5" />;
  };

  const getText = () => {
    if (contentType === 'moments') return 'Upload Moment';
    if (contentType === 'photos') return 'Upload Photo';
    if (contentType === 'videos') return 'Upload Video';
    return 'Upload Content';
  };

  const baseClasses = "inline-flex items-center justify-center transition-colors duration-200";
  
  const variantClasses = {
    default: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",
    icon: "p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full",
    text: "text-red-600 hover:text-red-700 underline"
  };

  const [hoveredType, setHoveredType] = useState<string | null>(null);

  return (
    <>
      <div className="flex flex-col items-center space-y-1">
        <button
          onClick={handleClick}
          className={`flex items-center justify-center transition-all shadow-sm
            w-12 h-12 rounded-full bg-black text-white hover:bg-gray-800 active:bg-gray-900
            sm:w-auto sm:h-auto sm:px-4 sm:py-2.5 md:px-5 md:py-3
            ${className}`}
        >
          <Upload className="w-5 h-5 text-white" />
          <span className="hidden sm:inline font-medium ml-2">Upload</span>
        </button>
      </div>
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
          <div
            className="relative w-[700px] h-auto"
            style={{
              background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
              borderRadius: "20px",
              padding: "40px",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "60px",
              isolation: "isolate",
            }}
          >
             <button
              className="absolute top-5 right-5 text-gray-600 hover:text-gray-800 text-2xl font-light"
              onClick={handleClose}
              aria-label="Close"
            >
              ×
            </button>
            {/* Step 1: Type selection */}
            
            {step === "type" && (
              <div
                className="w-full"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: "40px",
                  maxWidth: "620px",
                  width: "100%",
                  margin: "0 auto",
                }}
              >
                {/* Header */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    gap: "20px",
                    width: "100%",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      gap: "20px",
                      height: "37.08px",
                    }}
                  >
                    {/* Heart logo */}
                    <div
                      style={{
                        width: "47px",
                        height: "37.08px",
                        position: "relative",
                      }}
                    >
                      {
                        <svg
                          width="47"
                          height="38"
                          viewBox="0 0 47 38"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                            fill="#B31B1E"
                          />
                          <path
                            d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                            fill="#B31B1E"
                          />
                          <path
                            d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                            fill="#B31B1E"
                          />
                          <path
                            d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                            fill="#B31B1E"
                          />
                          <path
                            d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                            fill="#B31B1E"
                          />
                        </svg>
                      }
                    </div>

                    {/* Title + icon */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "213px",
                        height: "27px",
                      }}
                    >
                      <h2
                        style={{
                          width: "179px",
                          height: "27px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "22px",
                          lineHeight: "27px",
                          textAlign: "center",
                          color: "#000000",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        Face Verification
                      </h2>
                      <div
                        style={{
                          width: "24px",
                          height: "24px",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="24"
                          height="25"
                          viewBox="0 0 24 25"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12.1205 14.0698C12.1005 14.0698 12.0705 14.0698 12.0505 14.0698C12.0205 14.0698 11.9805 14.0698 11.9505 14.0698C9.68047 13.9998 7.98047 12.2298 7.98047 10.0498C7.98047 7.82978 9.79047 6.01978 12.0105 6.01978C14.2305 6.01978 16.0405 7.82978 16.0405 10.0498C16.0305 12.2398 14.3205 13.9998 12.1505 14.0698C12.1305 14.0698 12.1305 14.0698 12.1205 14.0698ZM12.0005 7.50978C10.6005 7.50978 9.47047 8.64978 9.47047 10.0398C9.47047 11.4098 10.5405 12.5198 11.9005 12.5698C11.9305 12.5598 12.0305 12.5598 12.1305 12.5698C13.4705 12.4998 14.5205 11.3998 14.5305 10.0398C14.5305 8.64978 13.4005 7.50978 12.0005 7.50978Z"
                            fill="#292D32"
                          />
                          <path
                            d="M11.9998 23.2899C9.30984 23.2899 6.73984 22.2899 4.74984 20.4699C4.56984 20.3099 4.48984 20.0699 4.50984 19.8399C4.63984 18.6499 5.37984 17.5399 6.60984 16.7199C9.58984 14.7399 14.4198 14.7399 17.3898 16.7199C18.6198 17.5499 19.3598 18.6499 19.4898 19.8399C19.5198 20.0799 19.4298 20.3099 19.2498 20.4699C17.2598 22.2899 14.6898 23.2899 11.9998 23.2899ZM6.07984 19.6399C7.73984 21.0299 9.82984 21.7899 11.9998 21.7899C14.1698 21.7899 16.2598 21.0299 17.9198 19.6399C17.7398 19.0299 17.2598 18.4399 16.5498 17.9599C14.0898 16.3199 9.91984 16.3199 7.43984 17.9599C6.72984 18.4399 6.25984 19.0299 6.07984 19.6399Z"
                            fill="#292D32"
                          />
                          <path
                            d="M12 23.2898C6.07 23.2898 1.25 18.4698 1.25 12.5398C1.25 6.6098 6.07 1.78979 12 1.78979C17.93 1.78979 22.75 6.6098 22.75 12.5398C22.75 18.4698 17.93 23.2898 12 23.2898ZM12 3.28979C6.9 3.28979 2.75 7.4398 2.75 12.5398C2.75 17.6398 6.9 21.7898 12 21.7898C17.1 21.7898 21.25 17.6398 21.25 12.5398C21.25 7.4398 17.1 3.28979 12 3.28979Z"
                            fill="#292D32"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Subtitle */}
                  <p
                    style={{
                      fontFamily: "Inter",
                      fontWeight: 400,
                      fontSize: "18px",
                      lineHeight: "30px",
                      color: "#000000",
                      width: "100%",
                    }}
                  >
                    Upload What is Relevant to you
                  </p>
                </div>

                {/* Upload options grid */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: "40px",
                    width: "100%",
                  }}
                >
                  {[uploadTypes.slice(0, 3), uploadTypes.slice(3, 6)].map(
                    (row, rowIndex) => (
                      <div
                        key={rowIndex}
                        style={{
                          display: "flex",
                          flexDirection: "row",
                          alignItems: "flex-start",
                          gap: "40px",
                          flexWrap: "wrap",
                          justifyContent: "center",
                          width: "100%",
                        }}
                      >
                        {row.map((type) => (
                          <div
                            key={type.key}
                            style={{
                              boxSizing: "border-box",
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "center",
                              alignItems: "center",
                              padding: "30px 20px",
                              gap: "10px",
                              width: "130px",
                              height: "118px",
                              border:
                                selectedType === type.key
                                  ? "1px solid #4285F4"
                                  : "1px solid #D9D9D9",
                              borderRadius: "10px",
                              cursor: "pointer",
                              position: "relative",
                              transition: "all 0.3s ease-in-out",
                              filter:
                                selectedType === type.key
                                  ? "drop-shadow(4px 4px 14px rgba(0, 0, 0, 0.25))"
                                  : "none",
                            }}
                            onClick={() => handleTypeSelect(type.key)}
                            onMouseEnter={() => setHoveredType(type.key)}
                            onMouseLeave={() => setHoveredType(null)}
                          >
                            <div style={{ width: "30px", height: "30px" }}>
                              {type.icon}
                            </div>
                            <span
                              style={{
                                fontFamily: "Inter",
                                fontWeight: 600,
                                fontSize: "20px",
                                lineHeight: "18px",
                                color: "#000000",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              {type.label}
                            </span>

                            <div
                              style={{
                                position: "absolute",
                                width: "16px",
                                height: "16px",
                                left: "100px",
                                top: "10px",
                              }}
                            >
                              {selectedType === type.key ? (
                                <div
                                  style={{
                                    background: "#4285F4",
                                    borderRadius: "50%",
                                    width: "100%",
                                    height: "100%",
                                    position: "relative",
                                  }}
                                >
                                  <Check className="w-3 h-3 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                                </div>
                              ) : (
                                <div
                                  style={{
                                    border: "1px solid #030405",
                                    borderRadius: "50%",
                                    width: "100%",
                                    height: "100%",
                                  }}
                                />
                              )}
                            </div>

                            {/* Tooltip with FAE6C4 background */}
                            {hoveredType === type.key &&
                              (type.label === "Flashes" ||
                                type.label === "Glimpses" ||
                                type.label === "Moments") && (
                                <div
                                  style={{
                                    position: "absolute",
                                    bottom: "-38px",
                                    background: "#FAE6C4",
                                    color: "#000",
                                    padding: "6px 12px",
                                    borderRadius: "6px",
                                    fontSize: "12px",
                                    fontWeight: 500,
                                    whiteSpace: "nowrap",
                                    boxShadow:
                                      "0px 4px 8px rgba(0, 0, 0, 0.15)",
                                    zIndex: 10,
                                  }}
                                >
                                  {type.label === "Flashes" &&
                                    "video ≤ 1.5 min"}
                                  {type.label === "Glimpses" && "video ≤ 7 min"}
                                  {type.label === "Moments" &&
                                    "photo or video < 1 min"}
                                </div>
                              )}
                          </div>
                        ))}
                      </div>
                    )
                  )}
                </div>

                {/* Next Button */}
                <button
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "16px 30px",
                    gap: "10px",
                    width: "140px",
                    height: "62px",
                    background: "#B31B1E",
                    borderRadius: "10px",
                    border: "none",
                    cursor: selectedType ? "pointer" : "not-allowed",
                    opacity: selectedType ? 1 : 0.5,
                  }}
                  disabled={!selectedType}
                  onClick={handleNext}
                >
                  <span
                    style={{
                      fontFamily: "Inter",
                      fontWeight: 700,
                      fontSize: "20px",
                      lineHeight: "24px",
                      textTransform: "capitalize",
                      color: "#FDFCFF",
                    }}
                  >
                    Next
                  </span>
                  <ArrowRight className="w-6 h-6 text-white" />
                </button>
              </div>
            )}
            
            {/* Step 2: File upload */}
            
            {step === "file" && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  padding: "0px",
                  gap: "40px",
                  width: "620px",
                  height: "479.08px",
                  flex: "none",
                  order: 0,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                {/* Header */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "20px",
                    width: "620px",
                    height: "87.08px",
                    flex: "none",
                    order: 0,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      padding: "0px",
                      gap: "20px",
                      width: "222px",
                      height: "37.08px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    {/* Heart logo */}
                    <div
                      style={{
                        width: "47px",
                        height: "37.08px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="47"
                        height="38"
                        viewBox="0 0 47 38"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                          fill="#B31B1E"
                        />
                      </svg>
                    </div>

                    {/* Title + icon */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "200px",
                        height: "27px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <h2
                        style={{
                          width: "175px",
                          height: "27px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "22px",
                          lineHeight: "27px",
                          textAlign: "center",
                          color: "#000000",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        Upload Moments
                      </h2>
                      <div
                        style={{
                          width: "20px",
                          height: "20px",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="21"
                          viewBox="0 0 20 21"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.5 18.2047H12.5C16.25 18.2047 17.5 16.9547 17.5 13.2047V8.20469C17.5 4.45469 16.25 3.20469 12.5 3.20469H7.5C3.75 3.20469 2.5 4.45469 2.5 8.20469V13.2047C2.5 16.9547 3.75 18.2047 7.5 18.2047Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M7.5 8.95469C8.32843 8.95469 9 8.28312 9 7.45469C9 6.62626 8.32843 5.95469 7.5 5.95469C6.67157 5.95469 6 6.62626 6 7.45469C6 8.28312 6.67157 8.95469 7.5 8.95469Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M2.67004 15.4547L6.10004 12.8047C6.59004 12.4047 7.35004 12.4547 7.78004 12.9047L8.02004 13.1547C8.50004 13.6547 9.35004 13.6547 9.83004 13.1547L12.55 10.4047C13.03 9.90469 13.88 9.90469 14.36 10.4047L17.5 13.5547"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Subtitle */}
                  <p
                    style={{
                      width: "620px",
                      height: "30px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 400,
                      fontSize: "18px",
                      lineHeight: "30px",
                      color: "#000000",
                      flex: "none",
                      order: 1,
                      alignSelf: "stretch",
                      flexGrow: 0,
                    }}
                  >
                    Select a Photo or Video to upload
                  </p>
                </div>

                {/* Content area */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "250px",
                    flex: "none",
                    order: 1,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Upload section */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "centre",
                      padding: "0px",
                      gap: "30px",
                      width: "280px",
                      height: "250px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0, // ✅ changed from 1 to 0
                      margin: "0 auto",
                    }}
                  >
                    {/* Upload  section */}
                    <div
                      style={{
                        boxSizing: "border-box",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center",
                        padding: "20px 40px",
                        gap: "8px",
                        width: "280px",
                        height: "110px",
                        background: "#000000",
                        border: "1px solid #FFFFFF",
                        borderRadius: "20px",
                        flex: "none",
                        order: 0,
                        alignSelf: "stretch",
                        flexGrow: 0,
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        const input = document.createElement("input");
                        input.type = "file";
                        input.accept = "image/*,video/*";
                        input.onchange = (e) => handleFileChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
                        input.click();
                      }}
                    >
                      <div
                        style={{
                          width: "24px",
                          height: "24px",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 20 21"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                            fill="#FFFFFF"
                          />
                          <path
                            d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                            fill="#FFFFFF"
                          />
                          <path
                            d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                            fill="#FFFFFF"
                          />
                          <path
                            d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                            fill="#FFFFFF"
                          />
                        </svg>
                      </div>
                      
                      {file && (
                        <span
                          style={{
                            marginTop: "8px",
                            fontSize: "14px",
                            color: "#4B5563",
                            fontFamily: "Inter",
                            fontWeight: 500,
                            textAlign: "center",
                            alignSelf: "center",
                          }}
                        >
                          Selected: {file.name}
                        </span>
                      )}
                      {videoDurationError && (
                        <div
                          style={{
                            marginTop: "12px",
                            backgroundColor: "#FAE6C4",
                            padding: "1px 1px",
                            borderRadius: "8px",
                            border: "1px solid #B31B1E",
                            color: "#B31B1E",
                            fontFamily: "Inter",
                            fontSize: "16px",
                            fontWeight: 600,
                            width: "100%",
                            textAlign: "center",
                            boxSizing: "border-box",
                          }}
                        >
                          {videoDurationError}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bottom buttons */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "62px",
                    flex: "none",
                    order: 2,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Back button */}
                  <div
                    style={{
                      boxSizing: "border-box",
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "16px 30px",
                      gap: "10px",

                      width: "143px",
                      height: "62px",
                      border: "1px solid #D9D9D9",
                      borderRadius: "10px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      setFile(null); // Clear uploaded file
                      setFileUrl(""); // Clear preview
                      setAutoThumbnailUrl(""); 
                      setCustomThumbnailUrl(""); 
                      setThumbnail(""); 
                      setVideoDurationError(""); 
                      setStep("type");
                    }}
                  >
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                          fill="#262626"
                        />
                        <path
                          d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                          fill="#262626"
                        />
                      </svg>
                    </div>
                    <span
                      style={{
                        width: "49px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: "#292D32",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Back
                    </span>
                  </div>
                  {/* Next button */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "16px 30px",
                      gap: "10px",
                      marginLeft: "auto",
                      width: "140px",
                      height: "62px",
                      background: file ? "#B31B1E" : "#D9D9D9",
                      borderRadius: "10px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                      cursor: file ? "pointer" : "not-allowed",
                    }}
                    onClick={() => {
                      if (file) {
                        const input = document.createElement("input");
                        input.type = "file";
                        input.accept = "image/*";
                        input.onchange = (e) => handleFileChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
                        input.click();
                      }
                    }}
                  >
                    <span
                      style={{
                        width: "46px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: file ? "#FDFCFF" : "#999999",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      Next
                    </span>
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                          fill={file ? "#FFFFFF" : "#999999"}
                        />
                        <path
                          d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                          fill={file ? "#FFFFFF" : "#999999"}
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Step 3: Thumbnail selection (video only) */}
            
            {step === "thumbnail" && fileUrl && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  padding: "0px",
                  gap: "40px",
                  width: "620px",
                  height: "479.08px",
                  flex: "none",
                  order: 0,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                {/* Header */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "20px",
                    width: "620px",
                    height: "87.08px",
                    flex: "none",
                    order: 0,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      padding: "0px",
                      gap: "20px",
                      width: "222px",
                      height: "37.08px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    {/* Heart logo */}
                    <div
                      style={{
                        width: "47px",
                        height: "37.08px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="47"
                        height="38"
                        viewBox="0 0 47 38"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                          fill="#B31B1E"
                        />
                      </svg>
                    </div>

                    {/* Title + icon */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "155px",
                        height: "27px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <h2
                        style={{
                          width: "125px",
                          height: "27px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "22px",
                          lineHeight: "27px",
                          textAlign: "center",
                          color: "#000000",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        Thumbnail
                      </h2>
                      <div
                        style={{
                          width: "20px",
                          height: "20px",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="21"
                          viewBox="0 0 20 21"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.5 18.2047H12.5C16.25 18.2047 17.5 16.9547 17.5 13.2047V8.20469C17.5 4.45469 16.25 3.20469 12.5 3.20469H7.5C3.75 3.20469 2.5 4.45469 2.5 8.20469V13.2047C2.5 16.9547 3.75 18.2047 7.5 18.2047Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M7.5 8.95469C8.32843 8.95469 9 8.28312 9 7.45469C9 6.62626 8.32843 5.95469 7.5 5.95469C6.67157 5.95469 6 6.62626 6 7.45469C6 8.28312 6.67157 8.95469 7.5 8.95469Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M2.67004 15.4547L6.10004 12.8047C6.59004 12.4047 7.35004 12.4547 7.78004 12.9047L8.02004 13.1547C8.50004 13.6547 9.35004 13.6547 9.83004 13.1547L12.55 10.4047C13.03 9.90469 13.88 9.90469 14.36 10.4047L17.5 13.5547"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Subtitle */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      padding: "0px",
                      gap: "8px",
                      width: "620px",
                      height: "30px",
                      flex: "none",
                      order: 1,
                      alignSelf: "stretch",
                      flexGrow: 0,
                    }}
                  >
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9 22.8296H15C20 22.8296 22 20.8296 22 15.8296V9.82959C22 4.82959 20 2.82959 15 2.82959H9C4 2.82959 2 4.82959 2 9.82959V15.8296C2 20.8296 4 22.8296 9 22.8296Z"
                          stroke="#292D32"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M9 10.8296C10.1046 10.8296 11 9.93416 11 8.82959C11 7.72502 10.1046 6.82959 9 6.82959C7.89543 6.82959 7 7.72502 7 8.82959C7 9.93416 7.89543 10.8296 9 10.8296Z"
                          stroke="#292D32"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                          stroke="#292D32"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    <span
                      style={{
                        width: "200px",
                        height: "30px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 400,
                        fontSize: "18px",
                        lineHeight: "30px",
                        color: "#000000",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Select a thumbnail
                    </span>
                  </div>
                </div>

                {/* Content area */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "250px",
                    flex: "none",
                    order: 1,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Left side - Thumbnail options */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      padding: "0px",
                      gap: "20px",
                      width: "300px",
                      height: "250px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    {/* Auto-generated thumbnail */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "140px",
                        height: "110px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                        cursor: autoThumbnailUrl ? "pointer" : "default",
                      }}
                      onClick={() =>
                        autoThumbnailUrl && setThumbnail(autoThumbnailUrl)
                      }
                    >
                      <div
                        style={{
                          boxSizing: "border-box",
                          width: "140px",
                          height: "80px",
                          border:
                            thumbnail === "auto" && autoThumbnailUrl
                              ? "2px solid #B31B1E"
                              : "1px solid #D9D9D9",
                          borderRadius: "10px",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                          overflow: "hidden",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          background: "#F5F5F5",
                        }}
                      >
                        {autoThumbnailUrl ? (
                          <img
                            src={autoThumbnailUrl}
                            alt="Auto Thumbnail"
                            style={{
                              width: "100%",
                              height: "100%",
                              objectFit: "cover",
                            }}
                          />
                        ) : (
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: "100%",
                              height: "100%",
                              fontSize: "12px",
                              color: "#999999",
                            }}
                          >
                            Generating...
                          </div>
                        )}
                      </div>
                      <span
                        style={{
                          width: "140px",
                          height: "20px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight:
                            thumbnail === "auto" && autoThumbnailUrl
                              ? 600
                              : 400,
                          fontSize: "12px",
                          lineHeight: "20px",
                          textAlign: "center",
                          color:
                            thumbnail === "auto" && autoThumbnailUrl
                              ? "#B31B1E"
                              : "#000000",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        Auto Generated
                      </span>
                    </div>

                    {/* Custom thumbnail upload */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "140px",
                        height: "110px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      {customThumbnailUrl ? (
                        <div
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            gap: "10px",
                            cursor: "pointer",
                          }}
                          onClick={() => setThumbnail("custom")}
                        >
                          <div
                            style={{
                              boxSizing: "border-box",
                              width: "140px",
                              height: "80px",
                              border:
                                thumbnail === "custom"
                                  ? "2px solid #B31B1E"
                                  : "1px solid #D9D9D9",
                              borderRadius: "10px",
                              overflow: "hidden",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <img
                              src={customThumbnailUrl}
                              alt="Custom Thumbnail"
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                              }}
                            />
                          </div>
                          <span
                            style={{
                              width: "140px",
                              height: "20px",
                              fontFamily: "Inter",
                              fontStyle: "normal",
                              fontWeight: thumbnail === "custom" ? 600 : 400,
                              fontSize: "12px",
                              lineHeight: "20px",
                              textAlign: "center",
                              color:
                                thumbnail === "custom" ? "#B31B1E" : "#000000",
                            }}
                          >
                            Uploaded Thumbnail
                          </span>
                        </div>
                      ) : (
                        <label
                          style={{
                            boxSizing: "border-box",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                            padding: "20px",
                            gap: "8px",
                            width: "140px",
                            height: "80px",
                            border: "2px dashed #D9D9D9",
                            borderRadius: "10px",
                            cursor: "pointer",
                            background: "#FAFAFA",
                            flex: "none",
                            order: 0,
                            flexGrow: 0,
                          }}
                        >
                          <div
                            style={{
                              width: "24px",
                              height: "24px",
                              flex: "none",
                              order: 0,
                              flexGrow: 0,
                            }}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M12 16V8M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                                stroke="#999999"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <span
                            style={{
                              width: "100px",
                              height: "12px",
                              fontFamily: "Inter",
                              fontStyle: "normal",
                              fontWeight: 400,
                              fontSize: "10px",
                              lineHeight: "12px",
                              textAlign: "center",
                              color: "#999999",
                              flex: "none",
                              order: 1,
                              flexGrow: 0,
                            }}
                          >
                            Upload Thumbnail
                          </span>
                          <input
                            type="file"
                            accept="image/*"
                            style={{ display: "none" }}
                            onChange={(e) => {
                              const f = e.target.files?.[0];
                              if (!f) return;
                              setCustomThumbnail(f);
                              setCustomThumbnailUrl(URL.createObjectURL(f));
                              setThumbnail("custom");
                            }}
                          />
                        </label>
                      )}
                    </div>
                  </div>

                  {/* Right side - Video preview */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "0px",
                      gap: "10px",
                      width: "280px",
                      height: "250px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <video
                      src={fileUrl}
                      controls
                      style={{
                        width: "280px",
                        height: "200px",
                        borderRadius: "10px",
                        objectFit: "cover",
                        background:
                          "linear-gradient(0deg, rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.24))",
                      }}
                    />
                    <span
                      style={{
                        width: "280px",
                        height: "20px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 500,
                        fontSize: "14px",
                        lineHeight: "20px",
                        textAlign: "center",
                        color: "#262626",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Video Preview
                    </span>
                  </div>
                </div>

                {/* Bottom buttons */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "62px",
                    flex: "none",
                    order: 2,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Back button */}
                  <div
                    style={{
                      boxSizing: "border-box",
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "16px 30px",
                      gap: "10px",

                      width: "143px",
                      height: "62px",
                      border: "1px solid #D9D9D9",
                      borderRadius: "10px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      setFile(null); // Clear uploaded file
                      setFileUrl(""); // Clear the preview URL
                      setAutoThumbnailUrl("");
                      setCustomThumbnailUrl(""); 
                      setThumbnail(""); 
                      setStep("file");
                    }}
                  >
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                          fill="#262626"
                        />
                        <path
                          d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                          fill="#262626"
                        />
                      </svg>
                    </div>
                    <span
                      style={{
                        width: "49px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: "#292D32",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Back
                    </span>
                  </div>

                  {/* Next button */}
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: "16px 30px",
                      width: "140px",
                      height: "62px",
                      background: thumbnail ? "#B31B1E" : "#D9D9D9",
                      borderRadius: "10px",
                      cursor: thumbnail ? "pointer" : "not-allowed",
                    }}
                    onClick={() => {
                      if (thumbnail) setStep("face");
                    }}
                  >
                    <span
                      style={{
                        width: "46px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: (
                          thumbnail === "auto"
                            ? autoThumbnailUrl
                            : customThumbnailUrl
                        )
                          ? "#FDFCFF"
                          : "#999999",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      Next
                    </span>
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                          fill={
                            (
                              thumbnail === "auto"
                                ? autoThumbnailUrl
                                : customThumbnailUrl
                            )
                              ? "#FFFFFF"
                              : "#999999"
                          }
                        />
                        <path
                          d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                          fill={
                            (
                              thumbnail === "auto"
                                ? autoThumbnailUrl
                                : customThumbnailUrl
                            )
                              ? "#FFFFFF"
                              : "#999999"
                          }
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* Step 4: Face verification */}
            {step === 'face' && !userFaceVerified && (
              <FaceVerificationModal
                open={true}
                onVerified={() => { setUserFaceVerified(true); setFaceModalOpen(false); setFaceVerified(true); }}
                onClose={() => { setFaceModalOpen(false); setStep('file'); }}
              />
            )}
            
            {step === "face" && userFaceVerified && (
              <div className="flex flex-col items-center justify-center min-h-[200px]">
                <span className="text-green-600 font-semibold">
                  Face already verified! You can proceed.
                </span>
              </div>
            )}
            {step === "photo-confirm" && fileUrl && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  padding: "0px",
                  gap: "40px",
                  width: "620px",
                  height: "479.08px",
                  flex: "none",
                  order: 0,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                {/* Header */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "20px",
                    width: "620px",
                    height: "87.08px",
                    flex: "none",
                    order: 0,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      padding: "0px",
                      gap: "20px",
                      width: "222px",
                      height: "37.08px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    {/* Heart logo */}
                    <div
                      style={{
                        width: "47px",
                        height: "37.08px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="47"
                        height="38"
                        viewBox="0 0 47 38"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                          fill="#B31B1E"
                        />
                        <path
                          d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                          fill="#B31B1E"
                        />
                      </svg>
                    </div>

                    {/* Title + icon */}
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "0px",
                        gap: "10px",
                        width: "160px",
                        height: "27px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <h2
                        style={{
                          width: "140px",
                          height: "27px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "22px",
                          lineHeight: "27px",
                          textAlign: "center",
                          color: "#000000",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        Preview Photo
                      </h2>
                      <div
                        style={{
                          width: "20px",
                          height: "20px",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="20"
                          height="21"
                          viewBox="0 0 20 21"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.5 18.2047H12.5C16.25 18.2047 17.5 16.9547 17.5 13.2047V8.20469C17.5 4.45469 16.25 3.20469 12.5 3.20469H7.5C3.75 3.20469 2.5 4.45469 2.5 8.20469V13.2047C2.5 16.9547 3.75 18.2047 7.5 18.2047Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M7.5 8.95469C8.32843 8.95469 9 8.28312 9 7.45469C9 6.62626 8.32843 5.95469 7.5 5.95469C6.67157 5.95469 6 6.62626 6 7.45469C6 8.28312 6.67157 8.95469 7.5 8.95469Z"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M2.67004 15.4547L6.10004 12.8047C6.59004 12.4047 7.35004 12.4547 7.78004 12.9047L8.02004 13.1547C8.50004 13.6547 9.35004 13.6547 9.83004 13.1547L12.55 10.4047C13.03 9.90469 13.88 9.90469 14.36 10.4047L17.5 13.5547"
                            stroke="#262626"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content area - Photo Preview */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "250px",
                    flex: "none",
                    order: 1,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Photo preview container */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "0px",
                      gap: "10px",
                      width: "400px",
                      height: "250px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    <div
                      style={{
                        boxSizing: "border-box",
                        width: "400px",
                        height: "220px",
                        border: "1px solid #D9D9D9",
                        borderRadius: "20px",
                        overflow: "hidden",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        background: "#F5F5F5",
                      }}
                    >
                      <img
                        src={fileUrl}
                        alt="Photo Preview"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "contain",
                          borderRadius: "20px",
                        }}
                      />
                    </div>
                    <span
                      style={{
                        width: "400px",
                        height: "20px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 500,
                        fontSize: "14px",
                        lineHeight: "20px",
                        textAlign: "center",
                        color: "#262626",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Photo Preview
                    </span>
                  </div>
                </div>

                {/* Bottom buttons */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "40px",
                    width: "620px",
                    height: "62px",
                    flex: "none",
                    order: 2,
                    alignSelf: "stretch",
                    flexGrow: 0,
                  }}
                >
                  {/* Back button */}
                  <div
                    style={{
                      boxSizing: "border-box",
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "16px 30px",
                      gap: "10px",
                      width: "143px",
                      height: "62px",
                      border: "1px solid #D9D9D9",
                      borderRadius: "10px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                      cursor: "pointer",
                    }}
                    onClick={() => {
                      setFile(null);
                      setFileUrl(null);
                      setStep("file");
                    }}
                  >
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                          fill="#262626"
                        />
                        <path
                          d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                          fill="#262626"
                        />
                      </svg>
                    </div>
                    <span
                      style={{
                        width: "49px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: "#292D32",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      Back
                    </span>
                  </div>

                  {/* Next button */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                      padding: "16px 30px",
                      gap: "10px",
                      width: "140px",
                      height: "62px",
                      background: "#B31B1E",
                      borderRadius: "10px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                      cursor: "pointer",
                    }}
                    onClick={() => setStep("face")}
                  >
                    <span
                      style={{
                        width: "46px",
                        height: "24px",
                        fontFamily: "Inter",
                        fontStyle: "normal",
                        fontWeight: 700,
                        fontSize: "20px",
                        lineHeight: "24px",
                        textTransform: "capitalize",
                        color: "#FDFCFF",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      Next
                    </span>
                    <div
                      style={{
                        width: "24px",
                        height: "24px",
                        flex: "none",
                        order: 1,
                        flexGrow: 0,
                      }}
                    >
                      <svg
                        width="24"
                        height="25"
                        viewBox="0 0 24 25"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                          fill="#FFFFFF"
                        />
                        <path
                          d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {uploading && (
              <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
                <span className="mb-2 text-base font-semibold">Uploading...</span>
                <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                  <div
                    className="bg-red-600 h-4 rounded-full transition-all"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <span className="text-sm text-gray-700">{progress}%</span>
              </div>
            )}
            {uploadError && (
              <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
                <span className="mb-2 text-base font-semibold text-red-600">{uploadError}</span>
                <button
                  className="w-full py-2 rounded-md bg-red-600 text-white font-semibold text-base hover:bg-red-700 transition-colors mt-2"
                  onClick={resetUploadState}
                >
                  Try Again
                </button>
              </div>
            )}
            {uploadSuccess && (
              <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
                <span className="mb-2 text-base font-semibold text-green-600">Upload successful!</span>
              </div>
            )}
          </div>
        </div>
      )}
      <PhotoUploadModal open={showPhotoModal} onClose={() => setShowPhotoModal(false)} />
      {showVideoModal && videoSubtype && (
        <VideoUploadModal
          open={showVideoModal}
          subtype={videoSubtype as 'flashes' | 'glimpses' | 'movies'}
          onClose={() => setShowVideoModal(false)}
        />
      )}
    </>
  );
};

export default UploadButton;
