"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx":
/*!****************************************************!*\
  !*** ./app/home/<USER>/components/EInvites.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _EInviteEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EInviteEditor */ \"(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EInvites = (param)=>{\n    let { setError, setSuccessMessage, setLoading, loading, error, successMessage } = param;\n    _s();\n    const [einvites, setEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingEInvite, setEditingEInvite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingEInvites, setLoadingEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to clear all cached data\n    const clearAllCache = ()=>{\n        console.log('Clearing all cached data...');\n        setTemplates([]);\n        setEInvites([]);\n        setError(null);\n        setEditingEInvite(null);\n        // Clear any browser cache\n        if (true) {\n            const keys = Object.keys(localStorage);\n            keys.forEach((key)=>{\n                const value = localStorage.getItem(key);\n                if (value && value.includes('default-')) {\n                    localStorage.removeItem(key);\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EInvites.useEffect\": ()=>{\n            clearAllCache();\n            fetchEInvites();\n            fetchTemplates();\n        }\n    }[\"EInvites.useEffect\"], []);\n    const getAuthToken = ()=>{\n        return localStorage.getItem('token');\n    };\n    const fetchEInvites = async ()=>{\n        try {\n            const token = getAuthToken();\n            if (!token) {\n                // If no token, just set empty e-invites and show templates\n                setEInvites([]);\n                setLoadingEInvites(false);\n                return;\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.data && response.data.websites) {\n                // Filter for e-invites (websites marked with type='einvite' in design_settings)\n                const einvites = response.data.websites.filter((website)=>{\n                    var _website_design_settings;\n                    return ((_website_design_settings = website.design_settings) === null || _website_design_settings === void 0 ? void 0 : _website_design_settings.type) === 'einvite';\n                });\n                setEInvites(einvites);\n            }\n            setLoadingEInvites(false);\n        } catch (error) {\n            console.error('Error fetching e-invites:', error);\n            // Don't show error for e-invites, just show empty and let templates load\n            setEInvites([]);\n            setLoadingEInvites(false);\n        }\n    };\n    const fetchTemplates = async ()=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.log('No token available for fetching templates');\n                setLoadingTemplates(false);\n                return;\n            }\n            console.log('Fetching templates with token:', token.substring(0, 20) + '...');\n            // Use the same API call as websites - with authentication\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('Templates API response:', response.data);\n            // Default frontend templates including the new Indian template\n            const defaultTemplates = [\n                {\n                    template_id: 'indian-traditional-001',\n                    name: 'Indian Traditional',\n                    thumbnail_url: 'data:image/svg+xml;base64,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',\n                    description: 'Beautiful traditional Indian wedding invitation with Ganesh blessings, marigold decorations, and vibrant red-gold colors.',\n                    default_colors: {\n                        primary: '#B31B1E',\n                        secondary: '#FFD700',\n                        background: '#FFFFFF',\n                        text: '#000000',\n                        accent: '#FF8C00'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Playfair Display',\n                            'Georgia',\n                            'serif'\n                        ],\n                        body: [\n                            'Roboto',\n                            'Arial',\n                            'sans-serif'\n                        ],\n                        decorative: [\n                            'Dancing Script',\n                            'cursive'\n                        ]\n                    }\n                },\n                {\n                    template_id: 'classic-elegance-001',\n                    name: 'Classic Elegance',\n                    thumbnail_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjQjMxQjFFIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjRkZGRkZGIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Q2xhc3NpYyBFbGVnYW5jZTwvdGV4dD4KPC9zdmc+',\n                    description: 'A timeless and elegant design for your special day.',\n                    default_colors: {\n                        primary: '#B31B1E',\n                        secondary: '#333333',\n                        background: '#FFFFFF',\n                        text: '#000000'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Playfair Display',\n                            'Georgia',\n                            'serif'\n                        ],\n                        body: [\n                            'Roboto',\n                            'Arial',\n                            'sans-serif'\n                        ]\n                    }\n                },\n                {\n                    template_id: 'modern-minimalist-001',\n                    name: 'Modern Minimalist',\n                    thumbnail_url: 'https://via.placeholder.com/300x400/000000/FFFFFF?text=Modern+Minimalist',\n                    description: 'A clean, modern design with minimalist aesthetics.',\n                    default_colors: {\n                        primary: '#000000',\n                        secondary: '#CCCCCC',\n                        background: '#FFFFFF',\n                        text: '#333333'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Montserrat',\n                            'Helvetica',\n                            'sans-serif'\n                        ],\n                        body: [\n                            'Open Sans',\n                            'Arial',\n                            'sans-serif'\n                        ]\n                    }\n                }\n            ];\n            if (response.data && response.data.templates && response.data.templates.length > 0) {\n                const apiTemplates = response.data.templates.map((template)=>({\n                        ...template,\n                        thumbnail_url: template.thumbnail_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE4IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVtcGxhdGU8L3RleHQ+Cjwvc3ZnPg=='\n                    }));\n                // Combine API templates with default templates, avoiding duplicates\n                const combinedTemplates = [\n                    ...defaultTemplates,\n                    ...apiTemplates.filter((apiTemplate)=>!defaultTemplates.some((defaultTemplate)=>defaultTemplate.name === apiTemplate.name))\n                ];\n                console.log('Loaded templates (API + defaults):', combinedTemplates);\n                setTemplates(combinedTemplates);\n            } else {\n                console.log('No templates from API, using default templates');\n                setTemplates(defaultTemplates);\n            }\n            setLoadingTemplates(false);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response;\n                console.error('Templates API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            }\n            setError(\"Failed to load templates. Please refresh the page.\");\n            setLoadingTemplates(false);\n        }\n    };\n    const handleCreateEInvite = (template)=>{\n        if (template) {\n            // Create a new e-invite based on the selected template\n            const newEInvite = {\n                website_id: '',\n                title: 'Our Wedding E-Invite',\n                couple_names: 'Eiyana & Warlin',\n                template_id: template.template_id,\n                wedding_date: '',\n                wedding_location: 'THE LITTLE HOTEL, 888, Glenport Road, Australia',\n                about_couple: '',\n                deployed_url: '',\n                is_published: false,\n                created_at: '',\n                updated_at: '',\n                template_name: template.name,\n                template_thumbnail: template.thumbnail_url,\n                design_settings: {\n                    colors: {\n                        primary: \"#B31B1E\",\n                        secondary: \"#333333\",\n                        background: \"#FFFFFF\",\n                        text: \"#000000\"\n                    },\n                    fonts: {\n                        heading: \"Playfair Display\",\n                        body: \"Open Sans\",\n                        coupleNames: \"Dancing Script\",\n                        date: \"Open Sans\",\n                        location: \"Open Sans\",\n                        aboutCouple: \"Open Sans\"\n                    },\n                    customImage: template.thumbnail_url,\n                    couple_names: 'Eiyana & Warlin',\n                    type: 'einvite'\n                }\n            };\n            setEditingEInvite(newEInvite);\n        } else {\n            setEditingEInvite(null);\n        }\n        setShowEditor(true);\n    };\n    const handleEditEInvite = (einvite)=>{\n        setEditingEInvite(einvite);\n        setShowEditor(true);\n    };\n    const handleSaveEInvite = async (einviteData)=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem('token');\n            if (!token) {\n                setError(\"Authentication required. Please log in.\");\n                setLoading(false);\n                return;\n            }\n            // Check if templates are loaded\n            if (templates.length === 0) {\n                setError(\"Templates not loaded. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Ensure we have a valid template_id and REJECT any \"default-\" IDs\n            let templateId = einviteData.template_id;\n            // REJECT any \"default-\" template IDs completely\n            if (templateId && templateId.includes('default-')) {\n                console.error('REJECTING invalid template ID:', templateId);\n                templateId = null;\n            }\n            if (!templateId && templates.length > 0) {\n                templateId = templates[0].template_id;\n                console.log('No valid template_id provided, using first template:', templateId);\n            }\n            if (!templateId) {\n                setError(\"No valid template available. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Double check the template ID is valid UUID format\n            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\n            if (!uuidRegex.test(templateId)) {\n                console.error('Template ID is not valid UUID format:', templateId);\n                setError(\"Invalid template format. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Find the selected template to include its thumbnail\n            const selectedTemplate = templates.find((t)=>t.template_id === templateId);\n            // Prepare the data with template information - EXACTLY like website save\n            const completeEInviteData = {\n                ...einviteData,\n                template_id: templateId,\n                template_name: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) || '',\n                template_thumbnail: '',\n                // Add the type marker in design_settings\n                design_settings: {\n                    ...einviteData.design_settings,\n                    type: 'einvite' // This marks it as an e-invite\n                }\n            };\n            console.log('Saving e-invite with data:', completeEInviteData);\n            console.log('Template ID being used:', completeEInviteData.template_id);\n            console.log('Available templates:', templates.map((t)=>({\n                    id: t.template_id,\n                    name: t.name\n                })));\n            if (editingEInvite && editingEInvite.website_id) {\n                // Update existing e-invite - EXACTLY like website update\n                const updateData = {\n                    ...completeEInviteData,\n                    website_id: editingEInvite.website_id\n                };\n                console.log('Updating with data:', updateData);\n                await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website', updateData, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                setSuccessMessage(\"E-Invite updated successfully\");\n            } else {\n                // Create new e-invite - EXACTLY like website create\n                console.log('Creating new e-invite with data:', completeEInviteData);\n                // Check if we have a valid template_id\n                if (!completeEInviteData.template_id) {\n                    throw new Error('No template_id available. Please select a template.');\n                }\n                // Create a minimal test payload first\n                const minimalPayload = {\n                    title: completeEInviteData.title || 'Test E-Invite',\n                    template_id: completeEInviteData.template_id,\n                    wedding_date: completeEInviteData.wedding_date,\n                    wedding_location: completeEInviteData.wedding_location || '',\n                    about_couple: completeEInviteData.about_couple || '',\n                    couple_names: completeEInviteData.couple_names || '',\n                    design_settings: completeEInviteData.design_settings || {},\n                    is_published: true,\n                    template_name: completeEInviteData.template_name || '',\n                    template_thumbnail: ''\n                };\n                console.log('Minimal payload:', minimalPayload);\n                const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website', minimalPayload, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('Create response:', response.data);\n                setSuccessMessage(\"E-Invite created successfully! You can create another one or edit this template.\");\n            }\n            // Don't refresh e-invites list since we're not showing them\n            // Don't close the editor - stay on template selection\n            setLoading(false);\n        } catch (error) {\n            console.error('Error saving e-invite:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response, _error_response_data, _error_response1;\n                console.error('API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                setError(\"Failed to save e-invite: \".concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message));\n            } else {\n                setError(\"Failed to save e-invite. Please try again.\");\n            }\n            setLoading(false);\n        }\n    };\n    const handleDeleteEInvite = async (einviteId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this e-invite?\")) {\n            return;\n        }\n        try {\n            setDeletingId(einviteId);\n            const token = getAuthToken();\n            if (!token) {\n                setError(\"Authentication required\");\n                setDeletingId(null);\n                return;\n            }\n            await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                },\n                data: {\n                    website_id: einviteId\n                }\n            });\n            setSuccessMessage(\"E-Invite deleted successfully!\");\n            fetchEInvites();\n            setDeletingId(null);\n        } catch (error) {\n            console.error('Error deleting e-invite:', error);\n            setError(\"Failed to delete e-invite. Please try again.\");\n            setDeletingId(null);\n        }\n    };\n    const handleShareEInvite = async (einvite)=>{\n        try {\n            // Generate sharing link (viewable by anyone, but encourages login for RSVP)\n            const shareUrl = \"\".concat(window.location.origin, \"/e-invite/\").concat(einvite.website_id);\n            // Copy to clipboard\n            await navigator.clipboard.writeText(shareUrl);\n            setSuccessMessage(\"E-Invite link copied to clipboard! Anyone can view it, and they'll be encouraged to join Wedzat to RSVP.\");\n        } catch (error) {\n            console.error('Error sharing e-invite:', error);\n            setError(\"Failed to copy link. Please try again.\");\n        }\n    };\n    const handlePreviewEInvite = (einvite)=>{\n        // Open preview in new tab\n        const previewUrl = \"/e-invite/\".concat(einvite.website_id);\n        window.open(previewUrl, '_blank');\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'N/A';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    if (showEditor) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EInviteEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            templates: templates,\n            einvite: editingEInvite,\n            onSave: handleSaveEInvite,\n            onCancel: ()=>{\n                setShowEditor(false);\n                setEditingEInvite(null);\n            },\n            loading: loading\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n            lineNumber: 502,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-gray-400 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-black\",\n                            children: [\n                                \"Our Most Trending \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#B31B1E]\",\n                                    children: \"Invites\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 31\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined),\n            loadingEInvites || loadingTemplates ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 32,\n                    className: \"animate-spin text-[#B31B1E]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 530,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8\",\n                        children: templates.length > 0 ? templates.slice(0, 8).map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group cursor-pointer\",\n                                onClick: ()=>handleCreateEInvite(template),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: template.thumbnail_url || 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template',\n                                            alt: template.name,\n                                            className: \"w-full h-full object-cover\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.onerror = null;\n                                                target.src = 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \"Customise\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, template.template_id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 17\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-full text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Loading templates...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleCreateEInvite(),\n                                    className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\",\n                                    children: \"Create E-Invite\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, undefined),\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true),\n            error && !error.includes(\"Authentication\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-red-100 text-red-700 rounded-md\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 698,\n                columnNumber: 9\n            }, undefined),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-green-100 text-green-700 rounded-md\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 705,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n        lineNumber: 516,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInvites, \"Gi3ldhoSg2oZqGGlxEvm0L3Q6IA=\");\n_c = EInvites;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInvites);\nvar _c;\n$RefreshReg$(_c, \"EInvites\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx\n"));

/***/ })

});