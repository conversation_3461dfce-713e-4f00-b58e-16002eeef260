"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx":
/*!*********************************************************!*\
  !*** ./app/home/<USER>/components/EInviteEditor.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst EInviteEditor = (param)=>{\n    let { templates, einvite, onSave, onCancel, loading } = param;\n    var _einvite_design_settings;\n    _s();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.title) || \"Our Wedding E-Invite\");\n    const [weddingDate, setWeddingDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_date) || \"\");\n    const [weddingLocation, setWeddingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_location) || \"\");\n    const [aboutCouple, setAboutCouple] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.about_couple) || \"\");\n    const [coupleNames, setCoupleNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.couple_names) || (einvite === null || einvite === void 0 ? void 0 : (_einvite_design_settings = einvite.design_settings) === null || _einvite_design_settings === void 0 ? void 0 : _einvite_design_settings.couple_names) || \"\");\n    const [designSettings, setDesignSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.design_settings) || {\n        colors: {\n            primary: \"#B31B1E\",\n            secondary: \"#333333\",\n            background: \"#FFFFFF\",\n            text: \"#000000\"\n        },\n        fonts: {\n            heading: \"Playfair Display\",\n            body: \"Open Sans\",\n            coupleNames: \"Dancing Script\",\n            date: \"Open Sans\",\n            location: \"Open Sans\",\n            aboutCouple: \"Open Sans\"\n        },\n        customImage: \"\",\n        type: \"einvite\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('content');\n    const [uploadingImage, setUploadingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchingImages, setSearchingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Find the selected template based on the einvite's template_id\n    const selectedTemplate = templates.find((t)=>t.template_id === (einvite === null || einvite === void 0 ? void 0 : einvite.template_id)) || templates[0];\n    const fontOptions = [\n        \"Arial\",\n        \"Helvetica\",\n        \"Times New Roman\",\n        \"Georgia\",\n        \"Verdana\",\n        \"Playfair Display\",\n        \"Open Sans\",\n        \"Lato\",\n        \"Roboto\",\n        \"Dancing Script\",\n        \"Great Vibes\",\n        \"Pacifico\",\n        \"Lobster\",\n        \"Montserrat\",\n        \"Poppins\"\n    ];\n    const handleColorChange = (colorType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                colors: {\n                    ...prev.colors,\n                    [colorType]: value\n                }\n            }));\n    };\n    const handleFontChange = (fontType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                fonts: {\n                    ...prev.fonts,\n                    [fontType]: value\n                }\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        setUploadingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', file);\n            const response = await fetch('/api/upload-image', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setDesignSettings((prev)=>({\n                        ...prev,\n                        customImage: data.imageUrl\n                    }));\n            } else {\n                console.error('Failed to upload image');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n        } finally{\n            setUploadingImage(false);\n        }\n    };\n    const searchImages = async ()=>{\n        if (!searchQuery.trim()) return;\n        setSearchingImages(true);\n        try {\n            const response = await fetch(\"/api/search-images?q=\".concat(encodeURIComponent(searchQuery)));\n            if (response.ok) {\n                const data = await response.json();\n                setSearchResults(data.images || []);\n            }\n        } catch (error) {\n            console.error('Error searching images:', error);\n        } finally{\n            setSearchingImages(false);\n        }\n    };\n    const selectSearchImage = (imageUrl)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                customImage: imageUrl\n            }));\n        setSearchResults([]);\n        setSearchQuery(\"\");\n    };\n    // Function to render template with image-based text overlay\n    const renderTemplate = ()=>{\n        var _designSettings_colors, _designSettings_colors1, _designSettings_fonts, _designSettings_colors2, _designSettings_fonts1, _designSettings_colors3, _designSettings_fonts2, _designSettings_colors4, _designSettings_fonts3, _designSettings_colors5, _designSettings_fonts4;\n        // Check if this is the Indian Traditional template\n        if ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.template_id) === 'indian-traditional-001' || (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) === 'Indian Traditional') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/templates/indian-traditional-template.jpg\",\n                        alt: \"Indian Traditional Template\",\n                        className: \"w-full h-full object-cover absolute inset-0\",\n                        style: {\n                            zIndex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        style: {\n                            zIndex: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[0]) || 'Navneet',\n                                onChange: (e)=>{\n                                    const secondName = (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[1]) || 'Suknya';\n                                    setCoupleNames(\"\".concat(e.target.value, \" & \").concat(secondName));\n                                },\n                                className: \"absolute bg-transparent border-none outline-none text-center font-bold text-red-800\",\n                                style: {\n                                    top: '45%',\n                                    left: '20%',\n                                    width: '25%',\n                                    fontSize: '18px',\n                                    color: '#B31B1E',\n                                    fontFamily: 'Playfair Display, serif'\n                                },\n                                placeholder: \"Partner 1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[1]) || 'Suknya',\n                                onChange: (e)=>{\n                                    const firstName = (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[0]) || 'Navneet';\n                                    setCoupleNames(\"\".concat(firstName, \" & \").concat(e.target.value));\n                                },\n                                className: \"absolute bg-transparent border-none outline-none text-center font-bold text-red-800\",\n                                style: {\n                                    top: '45%',\n                                    right: '20%',\n                                    width: '25%',\n                                    fontSize: '18px',\n                                    color: '#B31B1E',\n                                    fontFamily: 'Playfair Display, serif'\n                                },\n                                placeholder: \"Partner 2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: weddingDate || 'Monday, 22th Aug 2022',\n                                onChange: (e)=>setWeddingDate(e.target.value),\n                                className: \"absolute bg-transparent border-none outline-none text-center text-white\",\n                                style: {\n                                    top: '65%',\n                                    left: '50%',\n                                    transform: 'translateX(-50%)',\n                                    width: '60%',\n                                    fontSize: '14px',\n                                    color: '#FFD700'\n                                },\n                                placeholder: \"Wedding Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',\n                                onChange: (e)=>setWeddingLocation(e.target.value),\n                                className: \"absolute bg-transparent border-none outline-none text-center text-white resize-none\",\n                                style: {\n                                    top: '75%',\n                                    left: '50%',\n                                    transform: 'translateX(-50%)',\n                                    width: '80%',\n                                    height: '15%',\n                                    fontSize: '12px',\n                                    color: 'white'\n                                },\n                                placeholder: \"Wedding Venue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Default template rendering for other templates\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n            style: {\n                backgroundImage: designSettings.customImage ? \"url(\".concat(designSettings.customImage, \")\") : 'none',\n                backgroundColor: ((_designSettings_colors = designSettings.colors) === null || _designSettings_colors === void 0 ? void 0 : _designSettings_colors.background) || '#FFFFFF',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 p-6 flex flex-col justify-center items-center text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors1 = designSettings.colors) === null || _designSettings_colors1 === void 0 ? void 0 : _designSettings_colors1.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts = designSettings.fonts) === null || _designSettings_fonts === void 0 ? void 0 : _designSettings_fonts.heading) || 'serif'\n                                },\n                                children: \"You are invited to celebrate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors2 = designSettings.colors) === null || _designSettings_colors2 === void 0 ? void 0 : _designSettings_colors2.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts1 = designSettings.fonts) === null || _designSettings_fonts1 === void 0 ? void 0 : _designSettings_fonts1.coupleNames) || 'serif'\n                                },\n                                children: designSettings.couple_names || coupleNames || 'Couple Names'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold mb-1\",\n                                style: {\n                                    color: ((_designSettings_colors3 = designSettings.colors) === null || _designSettings_colors3 === void 0 ? void 0 : _designSettings_colors3.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts2 = designSettings.fonts) === null || _designSettings_fonts2 === void 0 ? void 0 : _designSettings_fonts2.date) || 'serif'\n                                },\n                                children: weddingDate || 'Wedding Date'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                style: {\n                                    color: ((_designSettings_colors4 = designSettings.colors) === null || _designSettings_colors4 === void 0 ? void 0 : _designSettings_colors4.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts3 = designSettings.fonts) === null || _designSettings_fonts3 === void 0 ? void 0 : _designSettings_fonts3.location) || 'sans-serif'\n                                },\n                                children: weddingLocation || 'Wedding Location'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, undefined),\n                    aboutCouple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs italic\",\n                        style: {\n                            color: ((_designSettings_colors5 = designSettings.colors) === null || _designSettings_colors5 === void 0 ? void 0 : _designSettings_colors5.text) || '#000000',\n                            fontFamily: ((_designSettings_fonts4 = designSettings.fonts) === null || _designSettings_fonts4 === void 0 ? void 0 : _designSettings_fonts4.aboutCouple) || 'sans-serif'\n                        },\n                        children: aboutCouple\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleSave = ()=>{\n        var _templates_;\n        console.log('Save button clicked');\n        // Process colors to ensure they're valid hex codes\n        const processedColors = {\n            ...designSettings.colors\n        };\n        Object.keys(processedColors).forEach((key)=>{\n            const color = processedColors[key];\n            if (color && !color.startsWith('#')) {\n                processedColors[key] = \"#\".concat(color);\n            }\n        });\n        // Process fonts to ensure they're valid\n        const processedFonts = {\n            ...designSettings.fonts\n        };\n        Object.keys(processedFonts).forEach((key)=>{\n            if (!processedFonts[key]) {\n                processedFonts[key] = fontOptions[0];\n            }\n        });\n        // Get template_id and REJECT any \"default-\" IDs\n        let templateId = (einvite === null || einvite === void 0 ? void 0 : einvite.template_id) || ((_templates_ = templates[0]) === null || _templates_ === void 0 ? void 0 : _templates_.template_id);\n        // REJECT any \"default-\" template IDs completely\n        if (templateId && templateId.includes('default-')) {\n            var _templates_find;\n            console.error('REJECTING invalid template ID in editor:', templateId);\n            templateId = (_templates_find = templates.find((t)=>!t.template_id.includes('default-'))) === null || _templates_find === void 0 ? void 0 : _templates_find.template_id;\n        }\n        if (!templateId) {\n            alert('No valid template available. Please refresh the page.');\n            return;\n        }\n        // Prepare the data - EXACTLY like website editor\n        const einviteData = {\n            title,\n            template_id: templateId,\n            wedding_date: weddingDate || null,\n            wedding_location: weddingLocation,\n            about_couple: aboutCouple,\n            couple_names: coupleNames,\n            design_settings: {\n                colors: processedColors,\n                fonts: processedFonts,\n                // Use only the custom image from upload or search\n                customImage: designSettings.customImage || '',\n                // Store couple_names in design_settings for backward compatibility\n                couple_names: coupleNames,\n                // Add type marker for e-invites\n                type: 'einvite'\n            },\n            is_published: true\n        };\n        console.log('Saving e-invite data:', einviteData);\n        onSave(einviteData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"p-2 hover:bg-gray-100 rounded-md transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gray-400 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-black\",\n                                    children: [\n                                        \"Our Most Trending \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#B31B1E]\",\n                                            children: \"Invites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto mb-6\",\n                        children: renderTemplate()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(activeTab === 'design' ? 'content' : 'design'),\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    activeTab === 'design' ? 'Hide Settings' : 'Colors & Background'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Resize\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Undo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: \"Back to Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: loading,\n                                className: \"flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    \"Save & Continue Editing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === 'design' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: Object.entries(designSettings.colors || {}).map((param)=>{\n                                                let [colorType, colorValue] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-black mb-2 capitalize\",\n                                                            children: colorType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"color\",\n                                                                    value: colorValue || '#000000',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: colorValue || '',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black\",\n                                                                    placeholder: \"#000000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, colorType, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Background Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleImageUpload,\n                                                    className: \"hidden\",\n                                                    id: \"image-upload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"image-upload\",\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        uploadingImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        designSettings.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-black mb-2\",\n                                                    children: \"Current Background:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-20 bg-gray-200 rounded overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: designSettings.customImage,\n                                                        alt: \"Background\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n        lineNumber: 400,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInviteEditor, \"77H3G4EMQD+TN3xJ6g+tspSlyR4=\");\n_c = EInviteEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInviteEditor);\nvar _c;\n$RefreshReg$(_c, \"EInviteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\n"));

/***/ })

});