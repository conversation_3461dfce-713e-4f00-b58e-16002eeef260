"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import EInvites from "./components/EInvites";

export default function EInvitesPage() {
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    setIsClient(true);
    console.log('E-Invites page loaded');

    // FORCE CLEAR ALL CACHED DATA
    if (typeof window !== 'undefined') {
      // Clear ALL localStorage that might contain old template data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value && (value.includes('default-') || value.includes('template') || value.includes('einvite'))) {
          console.log('Removing cached data:', key);
          localStorage.removeItem(key);
        }
      });

      // Clear ALL sessionStorage that might contain old template data
      const sessionKeys = Object.keys(sessionStorage);
      sessionKeys.forEach(key => {
        const value = sessionStorage.getItem(key);
        if (value && (value.includes('default-') || value.includes('template') || value.includes('einvite'))) {
          console.log('Removing cached session data:', key);
          sessionStorage.removeItem(key);
        }
      });
    }

    // Clear force reload flag if it exists (no longer needed)
    const hasReloaded = sessionStorage.getItem('einvites-force-reloaded');
    if (hasReloaded) {
      sessionStorage.removeItem('einvites-force-reloaded');
      console.log('Cleared force reload flag');
    }
  }, []);

  if (!isClient) {
    return <div className="opacity-0">Loading...</div>;
  }

  return (
    <div className="flex flex-col min-h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
            width: "100%"
          }}
        >
          <div className="w-full mx-auto max-w-full">
            <EInvites
              setError={setError}
              setSuccessMessage={setSuccessMessage}
              setLoading={setLoading}
              loading={loading}
              error={error}
              successMessage={successMessage}
            />
          </div>
        </main>
      </div>
    </div>
  );
}
