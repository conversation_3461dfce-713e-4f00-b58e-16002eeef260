import { useRouter } from 'next/navigation';
import { businessSubtypesMap } from '../../config/constants';

const VendorCategoryNav = () => {
  const router = useRouter();
  const businessTypes = Object.keys(businessSubtypesMap);

  return (
    <div className="flex gap-4 overflow-x-auto">
      {businessTypes.map(type => (
        <button
          key={type}
          onClick={() => router.push(`/vendor/services/${encodeURIComponent(type)}`)}
          className="px-4 py-2 rounded shadow bg-white hover:bg-red-100"
        >
          {type}
        </button>
      ))}
    </div>
  );
};

export default VendorCategoryNav; 