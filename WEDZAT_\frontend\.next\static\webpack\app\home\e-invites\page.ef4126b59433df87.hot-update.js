"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx":
/*!****************************************************!*\
  !*** ./app/home/<USER>/components/EInvites.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _EInviteEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EInviteEditor */ \"(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EInvites = (param)=>{\n    let { setError, setSuccessMessage, setLoading, loading, error, successMessage } = param;\n    _s();\n    const [einvites, setEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingEInvite, setEditingEInvite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingEInvites, setLoadingEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to clear all cached data\n    const clearAllCache = ()=>{\n        console.log('Clearing all cached data...');\n        setTemplates([]);\n        setEInvites([]);\n        setError(null);\n        setEditingEInvite(null);\n        // Clear any browser cache\n        if (true) {\n            const keys = Object.keys(localStorage);\n            keys.forEach((key)=>{\n                const value = localStorage.getItem(key);\n                if (value && value.includes('default-')) {\n                    localStorage.removeItem(key);\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EInvites.useEffect\": ()=>{\n            clearAllCache();\n            fetchEInvites();\n            fetchTemplates();\n        }\n    }[\"EInvites.useEffect\"], []);\n    const getAuthToken = ()=>{\n        return localStorage.getItem('token');\n    };\n    const fetchEInvites = async ()=>{\n        try {\n            const token = getAuthToken();\n            if (!token) {\n                // If no token, just set empty e-invites and show templates\n                setEInvites([]);\n                setLoadingEInvites(false);\n                return;\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.data && response.data.websites) {\n                // Filter for e-invites (websites marked with type='einvite' in design_settings)\n                const einvites = response.data.websites.filter((website)=>{\n                    var _website_design_settings;\n                    return ((_website_design_settings = website.design_settings) === null || _website_design_settings === void 0 ? void 0 : _website_design_settings.type) === 'einvite';\n                });\n                setEInvites(einvites);\n            }\n            setLoadingEInvites(false);\n        } catch (error) {\n            console.error('Error fetching e-invites:', error);\n            // Don't show error for e-invites, just show empty and let templates load\n            setEInvites([]);\n            setLoadingEInvites(false);\n        }\n    };\n    const fetchTemplates = async ()=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.log('No token available for fetching templates');\n                setLoadingTemplates(false);\n                return;\n            }\n            console.log('Fetching templates with token:', token.substring(0, 20) + '...');\n            // Use the same API call as websites - with authentication\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('Templates API response:', response.data);\n            if (response.data && response.data.templates && response.data.templates.length > 0) {\n                const processedTemplates = response.data.templates.map((template)=>({\n                        ...template,\n                        thumbnail_url: template.thumbnail_url || 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template'\n                    }));\n                console.log('Loaded real templates from API:', processedTemplates);\n                setTemplates(processedTemplates);\n            } else {\n                console.log('No templates returned from API, using fallback');\n                // If no templates from API, we need to handle this case\n                setError(\"No templates available. Please contact support.\");\n            }\n            setLoadingTemplates(false);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response;\n                console.error('Templates API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            }\n            setError(\"Failed to load templates. Please refresh the page.\");\n            setLoadingTemplates(false);\n        }\n    };\n    const handleCreateEInvite = (template)=>{\n        if (template) {\n            // Create a new e-invite based on the selected template\n            const newEInvite = {\n                website_id: '',\n                title: 'Our Wedding E-Invite',\n                couple_names: 'Eiyana & Warlin',\n                template_id: template.template_id,\n                wedding_date: '',\n                wedding_location: 'THE LITTLE HOTEL, 888, Glenport Road, Australia',\n                about_couple: '',\n                deployed_url: '',\n                is_published: false,\n                created_at: '',\n                updated_at: '',\n                template_name: template.name,\n                template_thumbnail: template.thumbnail_url,\n                design_settings: {\n                    colors: {\n                        primary: \"#B31B1E\",\n                        secondary: \"#333333\",\n                        background: \"#FFFFFF\",\n                        text: \"#000000\"\n                    },\n                    fonts: {\n                        heading: \"Playfair Display\",\n                        body: \"Open Sans\",\n                        coupleNames: \"Dancing Script\",\n                        date: \"Open Sans\",\n                        location: \"Open Sans\",\n                        aboutCouple: \"Open Sans\"\n                    },\n                    customImage: template.thumbnail_url,\n                    couple_names: 'Eiyana & Warlin',\n                    type: 'einvite'\n                }\n            };\n            setEditingEInvite(newEInvite);\n        } else {\n            setEditingEInvite(null);\n        }\n        setShowEditor(true);\n    };\n    const handleEditEInvite = (einvite)=>{\n        setEditingEInvite(einvite);\n        setShowEditor(true);\n    };\n    const handleSaveEInvite = async (einviteData)=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem('token');\n            if (!token) {\n                setError(\"Authentication required. Please log in.\");\n                setLoading(false);\n                return;\n            }\n            // Check if templates are loaded\n            if (templates.length === 0) {\n                setError(\"Templates not loaded. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Ensure we have a valid template_id and REJECT any \"default-\" IDs\n            let templateId = einviteData.template_id;\n            // REJECT any \"default-\" template IDs completely\n            if (templateId && templateId.includes('default-')) {\n                console.error('REJECTING invalid template ID:', templateId);\n                templateId = null;\n            }\n            if (!templateId && templates.length > 0) {\n                templateId = templates[0].template_id;\n                console.log('No valid template_id provided, using first template:', templateId);\n            }\n            if (!templateId) {\n                setError(\"No valid template available. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Double check the template ID is valid UUID format\n            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\n            if (!uuidRegex.test(templateId)) {\n                console.error('Template ID is not valid UUID format:', templateId);\n                setError(\"Invalid template format. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Find the selected template to include its thumbnail\n            const selectedTemplate = templates.find((t)=>t.template_id === templateId);\n            // Prepare the data with template information - EXACTLY like website save\n            const completeEInviteData = {\n                ...einviteData,\n                template_id: templateId,\n                template_name: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) || '',\n                template_thumbnail: '',\n                // Add the type marker in design_settings\n                design_settings: {\n                    ...einviteData.design_settings,\n                    type: 'einvite' // This marks it as an e-invite\n                }\n            };\n            console.log('Saving e-invite with data:', completeEInviteData);\n            console.log('Template ID being used:', completeEInviteData.template_id);\n            console.log('Available templates:', templates.map((t)=>({\n                    id: t.template_id,\n                    name: t.name\n                })));\n            if (editingEInvite && editingEInvite.website_id) {\n                // Update existing e-invite - EXACTLY like website update\n                const updateData = {\n                    ...completeEInviteData,\n                    website_id: editingEInvite.website_id\n                };\n                console.log('Updating with data:', updateData);\n                await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website', updateData, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                setSuccessMessage(\"E-Invite updated successfully\");\n            } else {\n                // Create new e-invite - EXACTLY like website create\n                console.log('Creating new e-invite with data:', completeEInviteData);\n                // Check if we have a valid template_id\n                if (!completeEInviteData.template_id) {\n                    throw new Error('No template_id available. Please select a template.');\n                }\n                // Create a minimal test payload first\n                const minimalPayload = {\n                    title: completeEInviteData.title || 'Test E-Invite',\n                    template_id: completeEInviteData.template_id,\n                    wedding_date: completeEInviteData.wedding_date,\n                    wedding_location: completeEInviteData.wedding_location || '',\n                    about_couple: completeEInviteData.about_couple || '',\n                    couple_names: completeEInviteData.couple_names || '',\n                    design_settings: completeEInviteData.design_settings || {},\n                    is_published: true,\n                    template_name: completeEInviteData.template_name || '',\n                    template_thumbnail: ''\n                };\n                console.log('Minimal payload:', minimalPayload);\n                const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website', minimalPayload, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('Create response:', response.data);\n                setSuccessMessage(\"E-Invite created successfully! You can create another one or edit this template.\");\n            }\n            // Don't refresh e-invites list since we're not showing them\n            // Don't close the editor - stay on template selection\n            setLoading(false);\n        } catch (error) {\n            console.error('Error saving e-invite:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response, _error_response_data, _error_response1;\n                console.error('API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                setError(\"Failed to save e-invite: \".concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message));\n            } else {\n                setError(\"Failed to save e-invite. Please try again.\");\n            }\n            setLoading(false);\n        }\n    };\n    const handleDeleteEInvite = async (einviteId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this e-invite?\")) {\n            return;\n        }\n        try {\n            setDeletingId(einviteId);\n            const token = getAuthToken();\n            if (!token) {\n                setError(\"Authentication required\");\n                setDeletingId(null);\n                return;\n            }\n            await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                },\n                data: {\n                    website_id: einviteId\n                }\n            });\n            setSuccessMessage(\"E-Invite deleted successfully!\");\n            fetchEInvites();\n            setDeletingId(null);\n        } catch (error) {\n            console.error('Error deleting e-invite:', error);\n            setError(\"Failed to delete e-invite. Please try again.\");\n            setDeletingId(null);\n        }\n    };\n    const handleShareEInvite = async (einvite)=>{\n        try {\n            // Generate sharing link (viewable by anyone, but encourages login for RSVP)\n            const shareUrl = \"\".concat(window.location.origin, \"/e-invite/\").concat(einvite.website_id);\n            // Copy to clipboard\n            await navigator.clipboard.writeText(shareUrl);\n            setSuccessMessage(\"E-Invite link copied to clipboard! Anyone can view it, and they'll be encouraged to join Wedzat to RSVP.\");\n        } catch (error) {\n            console.error('Error sharing e-invite:', error);\n            setError(\"Failed to copy link. Please try again.\");\n        }\n    };\n    const handlePreviewEInvite = (einvite)=>{\n        // Open preview in new tab\n        const previewUrl = \"/e-invite/\".concat(einvite.website_id);\n        window.open(previewUrl, '_blank');\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'N/A';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    if (showEditor) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EInviteEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            templates: templates,\n            einvite: editingEInvite,\n            onSave: handleSaveEInvite,\n            onCancel: ()=>{\n                setShowEditor(false);\n                setEditingEInvite(null);\n            },\n            loading: loading\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-gray-400 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-black\",\n                            children: [\n                                \"Our Most Trending \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#B31B1E]\",\n                                    children: \"Invites\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 31\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 459,\n                columnNumber: 7\n            }, undefined),\n            loadingEInvites || loadingTemplates ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 32,\n                    className: \"animate-spin text-[#B31B1E]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 471,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8\",\n                        children: templates.length > 0 ? templates.slice(0, 8).map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group cursor-pointer\",\n                                onClick: ()=>handleCreateEInvite(template),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: template.thumbnail_url || 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template',\n                                            alt: template.name,\n                                            className: \"w-full h-full object-cover\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.onerror = null;\n                                                target.src = 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \"Customise\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, template.template_id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 17\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-full text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Loading templates...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleCreateEInvite(),\n                                    className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\",\n                                    children: \"Create E-Invite\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, undefined),\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true),\n            error && !error.includes(\"Authentication\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-red-100 text-red-700 rounded-md\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 639,\n                columnNumber: 9\n            }, undefined),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-green-100 text-green-700 rounded-md\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 646,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n        lineNumber: 457,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInvites, \"Gi3ldhoSg2oZqGGlxEvm0L3Q6IA=\");\n_c = EInvites;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInvites);\nvar _c;\n$RefreshReg$(_c, \"EInvites\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx\n"));

/***/ })

});