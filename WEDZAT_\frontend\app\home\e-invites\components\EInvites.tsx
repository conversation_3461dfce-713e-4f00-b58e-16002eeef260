"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Loader2, Plus, Edit, Trash2, Share2, Eye } from "lucide-react";
import EInviteEditor from "./EInviteEditor";

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  deployed_url: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  template_name: string;
  template_thumbnail: string;
  design_settings?: {
    colors?: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts?: {
      heading: string;
      body: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
    };
    customImage?: string;
    couple_names?: string;
    type?: string; // 'einvite' to distinguish from regular websites
  };
}

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface EInvitesProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const EInvites: React.FC<EInvitesProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  const [einvites, setEInvites] = useState<EInvite[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingEInvite, setEditingEInvite] = useState<EInvite | null>(null);
  const [loadingEInvites, setLoadingEInvites] = useState(true);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // Function to clear all cached data
  const clearAllCache = () => {
    console.log('Clearing all cached data...');
    setTemplates([]);
    setEInvites([]);
    setError(null);
    setEditingEInvite(null);

    // Clear any browser cache
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        const value = localStorage.getItem(key);
        if (value && value.includes('default-')) {
          localStorage.removeItem(key);
        }
      });
    }
  };

  useEffect(() => {
    clearAllCache();
    fetchEInvites();
    fetchTemplates();
  }, []);

  const getAuthToken = () => {
    return localStorage.getItem('token');
  };

  const fetchEInvites = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        // If no token, just set empty e-invites and show templates
        setEInvites([]);
        setLoadingEInvites(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.websites) {
        // Filter for e-invites (websites marked with type='einvite' in design_settings)
        const einvites = response.data.websites.filter((website: any) =>
          website.design_settings?.type === 'einvite'
        );

        setEInvites(einvites);
      }
      setLoadingEInvites(false);
    } catch (error) {
      console.error('Error fetching e-invites:', error);
      // Don't show error for e-invites, just show empty and let templates load
      setEInvites([]);
      setLoadingEInvites(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        console.log('No token available for fetching templates');
        setLoadingTemplates(false);
        return;
      }

      console.log('Fetching templates with token:', token.substring(0, 20) + '...');

      // Use the same API call as websites - with authentication
      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      console.log('Templates API response:', response.data);

      // Default frontend templates including the new Indian template
      const defaultTemplates = [
        {
          template_id: 'indian-traditional-001',
          name: 'Indian Traditional',
          thumbnail_url: 'data:image/svg+xml;base64,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',
          description: 'Beautiful traditional Indian wedding invitation with Ganesh blessings, marigold decorations, and vibrant red-gold colors.',
          default_colors: {
            primary: '#B31B1E',
            secondary: '#FFD700',
            background: '#FFFFFF',
            text: '#000000',
            accent: '#FF8C00'
          },
          default_fonts: {
            heading: ['Playfair Display', 'Georgia', 'serif'],
            body: ['Roboto', 'Arial', 'sans-serif'],
            decorative: ['Dancing Script', 'cursive']
          }
        },
        {
          template_id: 'classic-elegance-001',
          name: 'Classic Elegance',
          thumbnail_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjQjMxQjFFIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjRkZGRkZGIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Q2xhc3NpYyBFbGVnYW5jZTwvdGV4dD4KPC9zdmc+',
          description: 'A timeless and elegant design for your special day.',
          default_colors: {
            primary: '#B31B1E',
            secondary: '#333333',
            background: '#FFFFFF',
            text: '#000000'
          },
          default_fonts: {
            heading: ['Playfair Display', 'Georgia', 'serif'],
            body: ['Roboto', 'Arial', 'sans-serif']
          }
        },
        {
          template_id: 'modern-minimalist-001',
          name: 'Modern Minimalist',
          thumbnail_url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMDAwMDAwIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjRkZGRkZGIiBmb250LXNpemU9IjE0IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TW9kZXJuIE1pbmltYWxpc3Q8L3RleHQ+Cjwvc3ZnPg==',
          description: 'A clean, modern design with minimalist aesthetics.',
          default_colors: {
            primary: '#000000',
            secondary: '#CCCCCC',
            background: '#FFFFFF',
            text: '#333333'
          },
          default_fonts: {
            heading: ['Montserrat', 'Helvetica', 'sans-serif'],
            body: ['Open Sans', 'Arial', 'sans-serif']
          }
        }
      ];

      if (response.data && response.data.templates && response.data.templates.length > 0) {
        const apiTemplates = response.data.templates.map((template: any) => ({
          ...template,
          thumbnail_url: template.thumbnail_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE4IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVtcGxhdGU8L3RleHQ+Cjwvc3ZnPg=='
        }));

        // Combine API templates with default templates, avoiding duplicates
        const combinedTemplates = [...defaultTemplates, ...apiTemplates.filter(
          (apiTemplate: any) => !defaultTemplates.some(defaultTemplate => defaultTemplate.name === apiTemplate.name)
        )];

        console.log('Loaded templates (API + defaults):', combinedTemplates);
        setTemplates(combinedTemplates);
      } else {
        console.log('No templates from API, using default templates');
        setTemplates(defaultTemplates);
      }
      setLoadingTemplates(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      if (axios.isAxiosError(error)) {
        console.error('Templates API Error details:', error.response?.data);
      }
      setError("Failed to load templates. Please refresh the page.");
      setLoadingTemplates(false);
    }
  };

  const handleCreateEInvite = (template?: Template) => {
    if (template) {
      // Create a new e-invite based on the selected template
      const newEInvite = {
        website_id: '',
        title: 'Our Wedding E-Invite',
        couple_names: 'Eiyana & Warlin',
        template_id: template.template_id,
        wedding_date: '',
        wedding_location: 'THE LITTLE HOTEL, 888, Glenport Road, Australia',
        about_couple: '',
        deployed_url: '',
        is_published: false,
        created_at: '',
        updated_at: '',
        template_name: template.name,
        template_thumbnail: template.thumbnail_url,
        design_settings: {
          colors: {
            primary: "#B31B1E",
            secondary: "#333333",
            background: "#FFFFFF",
            text: "#000000"
          },
          fonts: {
            heading: "Playfair Display",
            body: "Open Sans",
            coupleNames: "Dancing Script",
            date: "Open Sans",
            location: "Open Sans",
            aboutCouple: "Open Sans"
          },
          customImage: template.thumbnail_url,
          couple_names: 'Eiyana & Warlin',
          type: 'einvite'
        }
      };
      setEditingEInvite(newEInvite as any);
    } else {
      setEditingEInvite(null);
    }
    setShowEditor(true);
  };

  const handleEditEInvite = (einvite: EInvite) => {
    setEditingEInvite(einvite);
    setShowEditor(true);
  };

  const handleSaveEInvite = async (einviteData: any) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError("Authentication required. Please log in.");
        setLoading(false);
        return;
      }

      // Check if templates are loaded
      if (templates.length === 0) {
        setError("Templates not loaded. Please refresh the page and try again.");
        setLoading(false);
        return;
      }

      // Ensure we have a valid template_id and REJECT any "default-" IDs
      let templateId = einviteData.template_id;

      // REJECT any "default-" template IDs completely
      if (templateId && templateId.includes('default-')) {
        console.error('REJECTING invalid template ID:', templateId);
        templateId = null;
      }

      if (!templateId && templates.length > 0) {
        templateId = templates[0].template_id;
        console.log('No valid template_id provided, using first template:', templateId);
      }

      if (!templateId) {
        setError("No valid template available. Please refresh the page and try again.");
        setLoading(false);
        return;
      }

      // Double check the template ID is valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(templateId)) {
        console.error('Template ID is not valid UUID format:', templateId);
        setError("Invalid template format. Please refresh the page and try again.");
        setLoading(false);
        return;
      }

      // Find the selected template to include its thumbnail
      const selectedTemplate = templates.find(t => t.template_id === templateId);

      // Prepare the data with template information - EXACTLY like website save
      const completeEInviteData = {
        ...einviteData,
        template_id: templateId, // Ensure template_id is set
        template_name: selectedTemplate?.name || '',
        template_thumbnail: '',
        // Add the type marker in design_settings
        design_settings: {
          ...einviteData.design_settings,
          type: 'einvite' // This marks it as an e-invite
        }
      };

      console.log('Saving e-invite with data:', completeEInviteData);
      console.log('Template ID being used:', completeEInviteData.template_id);
      console.log('Available templates:', templates.map(t => ({ id: t.template_id, name: t.name })));

      if (editingEInvite && editingEInvite.website_id) {
        // Update existing e-invite - EXACTLY like website update
        const updateData = {
          ...completeEInviteData,
          website_id: editingEInvite.website_id
        };
        console.log('Updating with data:', updateData);

        await axios.put(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website',
          updateData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        setSuccessMessage("E-Invite updated successfully");
      } else {
        // Create new e-invite - EXACTLY like website create
        console.log('Creating new e-invite with data:', completeEInviteData);

        // Check if we have a valid template_id
        if (!completeEInviteData.template_id) {
          throw new Error('No template_id available. Please select a template.');
        }

        // Create a minimal test payload first
        const minimalPayload = {
          title: completeEInviteData.title || 'Test E-Invite',
          template_id: completeEInviteData.template_id,
          wedding_date: completeEInviteData.wedding_date,
          wedding_location: completeEInviteData.wedding_location || '',
          about_couple: completeEInviteData.about_couple || '',
          couple_names: completeEInviteData.couple_names || '',
          design_settings: completeEInviteData.design_settings || {},
          is_published: true,
          template_name: completeEInviteData.template_name || '',
          template_thumbnail: ''
        };

        console.log('Minimal payload:', minimalPayload);

        const response = await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website',
          minimalPayload,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Create response:', response.data);

        setSuccessMessage("E-Invite created successfully! You can create another one or edit this template.");
      }

      // Don't refresh e-invites list since we're not showing them
      // Don't close the editor - stay on template selection
      setLoading(false);
    } catch (error) {
      console.error('Error saving e-invite:', error);
      if (axios.isAxiosError(error)) {
        console.error('API Error details:', error.response?.data);
        setError(`Failed to save e-invite: ${error.response?.data?.error || error.message}`);
      } else {
        setError("Failed to save e-invite. Please try again.");
      }
      setLoading(false);
    }
  };

  const handleDeleteEInvite = async (einviteId: string) => {
    if (!window.confirm("Are you sure you want to delete this e-invite?")) {
      return;
    }

    try {
      setDeletingId(einviteId);
      const token = getAuthToken();

      if (!token) {
        setError("Authentication required");
        setDeletingId(null);
        return;
      }

      await axios.delete(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website`,
        {
          headers: { Authorization: `Bearer ${token}` },
          data: { website_id: einviteId }
        }
      );

      setSuccessMessage("E-Invite deleted successfully!");
      fetchEInvites();
      setDeletingId(null);
    } catch (error) {
      console.error('Error deleting e-invite:', error);
      setError("Failed to delete e-invite. Please try again.");
      setDeletingId(null);
    }
  };

  const handleShareEInvite = async (einvite: EInvite) => {
    try {
      // Generate sharing link (viewable by anyone, but encourages login for RSVP)
      const shareUrl = `${window.location.origin}/e-invite/${einvite.website_id}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(shareUrl);
      setSuccessMessage("E-Invite link copied to clipboard! Anyone can view it, and they'll be encouraged to join Wedzat to RSVP.");
    } catch (error) {
      console.error('Error sharing e-invite:', error);
      setError("Failed to copy link. Please try again.");
    }
  };

  const handlePreviewEInvite = (einvite: EInvite) => {
    // Open preview in new tab
    const previewUrl = `/e-invite/${einvite.website_id}`;
    window.open(previewUrl, '_blank');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (showEditor) {
    return (
      <EInviteEditor
        templates={templates}
        einvite={editingEInvite as any}
        onSave={handleSaveEInvite}
        onCancel={() => {
          setShowEditor(false);
          setEditingEInvite(null);
        }}
        loading={loading}
      />
    );
  }

  return (
    <div className="bg-white">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
            <div className="w-4 h-4 bg-gray-400 rounded"></div>
          </div>
          <h2 className="text-2xl font-bold text-black">
            Our Most Trending <span className="text-[#B31B1E]">Invites</span>
          </h2>
        </div>
      </div>

      {loadingEInvites || loadingTemplates ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 size={32} className="animate-spin text-[#B31B1E]" />
        </div>
      ) : (
        <>
          {/* Templates Grid - Always show templates */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
            {templates.length > 0 ? templates.slice(0, 8).map((template) => (
                <div
                  key={template.template_id}
                  className="relative group cursor-pointer"
                  onClick={() => handleCreateEInvite(template)}
                >
                  <div className="aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative">
                    <img
                      src={template.thumbnail_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE4IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVtcGxhdGU8L3RleHQ+Cjwvc3ZnPg=='}
                      alt={template.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE4IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVtcGxhdGU8L3RleHQ+Cjwvc3ZnPg==';
                      }}
                    />

                    {/* Play button overlay */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                        <div className="w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                      </div>
                    </div>

                    {/* Hover overlay with "Customize" button */}
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2">
                        <Edit size={16} />
                        Customise
                      </button>
                    </div>
                  </div>
                </div>
              )) : (
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-600 mb-4">Loading templates...</p>
                  <button
                    onClick={() => handleCreateEInvite()}
                    className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    Create E-Invite
                  </button>
                </div>
              )}
            </div>

          {/* Hide saved e-invites - always show templates */}
          {false && einvites.length > 0 && (
            <>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-black">Your E-Invites</h3>
                <button
                  onClick={() => handleCreateEInvite()}
                  className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                  disabled={loading}
                >
                  <Plus size={20} />
                  Create New
                </button>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {einvites.map((einvite) => (
                  <div
                    key={einvite.website_id}
                    className="relative group cursor-pointer"
                    onMouseEnter={() => setHoveredCard(einvite.website_id)}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <div className="aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative">
                      <img
                        src={einvite.design_settings?.customImage || einvite.template_thumbnail || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RS1JbnZpdGU8L3RleHQ+Cjwvc3ZnPg=='}
                        alt={einvite.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjOWNhM2FmIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RS1JbnZpdGU8L3RleHQ+Cjwvc3ZnPg==';
                        }}
                      />

                      {/* Text overlay on thumbnail */}
                      <div className="absolute inset-0 flex flex-col justify-center items-center text-center p-2">
                        <div className="bg-white bg-opacity-90 rounded p-2 mb-2">
                          <h3 className="text-xs font-bold text-[#B31B1E] mb-1">
                            {einvite.couple_names || einvite.design_settings?.couple_names || 'Eiyana & Warlin'}
                          </h3>
                          {einvite.wedding_date && (
                            <p className="text-xs text-gray-600">
                              {new Date(einvite.wedding_date).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Play button overlay */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                          <div className="w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                        </div>
                      </div>

                      {/* Hover overlay with "Customize" button */}
                      {hoveredCard === einvite.website_id && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <button
                            onClick={() => handleEditEInvite(einvite)}
                            className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                          >
                            <Edit size={16} />
                            Customise
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Action buttons on hover */}
                    {hoveredCard === einvite.website_id && (
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShareEInvite(einvite);
                          }}
                          className="p-1 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Share E-Invite"
                        >
                          <Share2 size={14} className="text-gray-600" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEInvite(einvite.website_id);
                          }}
                          className="p-1 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Delete E-Invite"
                          disabled={deletingId === einvite.website_id}
                        >
                          {deletingId === einvite.website_id ? (
                            <Loader2 size={14} className="animate-spin text-gray-600" />
                          ) : (
                            <Trash2 size={14} className="text-gray-600" />
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}
        </>
      )}

      {/* Error message - Only show non-authentication errors */}
      {error && !error.includes("Authentication") && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default EInvites;
