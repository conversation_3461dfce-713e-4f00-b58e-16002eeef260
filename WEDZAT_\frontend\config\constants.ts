// API Base URL
export const API_BASE_URL = 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test';

// Other constants can be added here

export const businessSubtypesMap: Record<string, { subtype_id: string; name: string; description?: string }[]> = {
  "Venues": [
    { subtype_id: "venue-1", name: "Kalyana Mandapam" },
    { subtype_id: "venue-2", name: "Banquet Halls" },
    { subtype_id: "venue-3", name: "Small Party/Engagement Halls" },
    { subtype_id: "venue-4", name: "Outdoor Lawn/Garden" },
    { subtype_id: "venue-5", name: "Resort" },
    { subtype_id: "venue-6", name: "4-Star/5-Star Hotel" },
    { subtype_id: "venue-7", name: "Destination Weddings" }
  ],
  // ... (all other business types and subtypes from services/page.tsx) ...
};
