"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../components/HomeDashboard/Navigation";
import EInvites from "./components/EInvites";

export default function EInvitesPage() {
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    setIsClient(true);
    console.log('E-Invites page loaded');

    // Clear any cached data that might have old template IDs
    if (typeof window !== 'undefined') {
      // Clear any potential cached template data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('template') || key.includes('einvite')) {
          const value = localStorage.getItem(key);
          if (value && value.includes('default-')) {
            localStorage.removeItem(key);
          }
        }
      });
    }

    // Check if this is the first load and we're coming from navigation
    const hasReloaded = sessionStorage.getItem('einvites-reloaded');
    if (!hasReloaded && window.location.pathname === '/home/<USER>') {
      sessionStorage.setItem('einvites-reloaded', 'true');
      // Small delay to ensure the page is ready
      setTimeout(() => {
        window.location.reload();
      }, 100);
    }
  }, []);

  if (!isClient) {
    return <div className="opacity-0">Loading...</div>;
  }

  return (
    <div className="flex flex-col min-h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
            width: "100%"
          }}
        >
          <div className="w-full mx-auto max-w-full">
            <EInvites
              setError={setError}
              setSuccessMessage={setSuccessMessage}
              setLoading={setLoading}
              loading={loading}
              error={error}
              successMessage={successMessage}
            />
          </div>
        </main>
      </div>
    </div>
  );
}
