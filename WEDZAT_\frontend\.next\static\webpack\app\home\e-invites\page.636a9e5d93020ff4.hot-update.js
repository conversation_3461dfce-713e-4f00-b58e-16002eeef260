"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '400px',\n            width: '100%',\n            background: colors.primary || '#B31B1E',\n            borderRadius: '0px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif'),\n            aspectRatio: '3/4'\n        },\n        innerContainer: {\n            background: colors.background || '#FFFFFF',\n            margin: '20px',\n            borderRadius: '10px',\n            padding: '20px',\n            height: 'calc(100% - 40px)',\n            position: 'relative'\n        },\n        headerDecoration: {\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            height: '8px',\n            width: '100%'\n        },\n        marigoldBorder: {\n            background: \"url(\\\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='\".concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='30' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='50' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='70' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='90' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/></svg>\\\") repeat-x\"),\n            height: '20px',\n            width: '100%'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '30px 20px 20px',\n            background: \"linear-gradient(180deg, \".concat(colors.background, \" 0%, #FFF8E7 100%)\")\n        },\n        ganeshSymbol: {\n            fontSize: '48px',\n            color: colors.primary,\n            marginBottom: '10px'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '24px',\n            color: colors.primary,\n            fontWeight: '700',\n            marginBottom: '20px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '42px',\n            fontWeight: '700',\n            color: colors.primary,\n            textAlign: 'center',\n            margin: '20px 0',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '32px',\n            color: colors.secondary,\n            margin: '0 15px'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});