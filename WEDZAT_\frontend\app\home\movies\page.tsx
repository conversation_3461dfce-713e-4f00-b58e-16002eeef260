"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import { MdFavorite, MdFavoriteBorder, MdOutlineModeComment, MdOutlineShare } from "react-icons/md";
import VideoVendorDetails from "../../../components/VideoVendorDetails";
import { useRouter } from "next/navigation";
import useSWR from 'swr';

// Define interface for movie video items (updated for new API response)
interface MovieVideo {
  media_id: string;
  media_url: string;
  thumbnail_url: string;
  caption: string;
  location: string;
  event_type: string;
  video_type: string;
  partner: string;
  wedding_style: string;
  budget: string;
  created_at: string;
  user_id: string;
  user_name: string;
  user_avatar: string | null;
  is_own_content: boolean;
  followers_count: number;
  following_count: number;
  is_following: boolean;
  views_count: number;
  likes_count: number;
  is_liked: boolean;
}

interface ApiResponse {
  movies: MovieVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
  filtered_by_location?: string;
  location_matches_in_page?: number;
}

// SWR fetcher function
const fetcher = async (url: string, token: string) => {
  const response = await axios.get<ApiResponse>(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return response.data;
};

export default function MoviesPage() {
  const router = useRouter();
  const [movies, setMovies] = useState<MovieVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  // Removed right sidebar state
  const [likedVideos, setLikedVideos] = useState<string[]>([]);

  useEffect(() => setIsClient(true), []);

  // Get token from localStorage (client only)
  const token = typeof window !== 'undefined' ?
    (localStorage.getItem('token') ||
      localStorage.getItem('jwt_token') ||
      localStorage.getItem('auth_token')) : null;

  // Use SWR for fetching movies
  const { data, error: swrError, isValidating } = useSWR(
    token ? [`/movies?page=${page}&limit=10`, token] : null,
    ([url, token]) => fetcher(url, token),
    {
      revalidateOnFocus: false,
      dedupingInterval: 2 * 60 * 1000, // 2 minutes
      keepPreviousData: true,
    }
  );

  useEffect(() => {
    setLoading(isValidating);
    setError(swrError ? 'Failed to load movies' : null);
    if (data && data.movies) {
      // ... process as before ...
      const processedMovies = data.movies.map((movie, index) => {
        if (!movie.thumbnail_url && movie.media_url && movie.media_url.includes('youtube')) {
          const videoId = getYoutubeId(movie.media_url);
          if (videoId) {
            movie.thumbnail_url = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
          }
        }
        if (movie.media_url) {
          if (!movie.media_url.startsWith('http')) {
            movie.media_url = `https://${movie.media_url}`;
          }
        }
        return movie;
      });
      if (page === 1) {
        setMovies(processedMovies);
      } else {
        setMovies(prev => [...prev, ...processedMovies]);
      }
      setHasMore(data.next_page);
    }
  }, [data, swrError, isValidating, page]);

  // Reference for the last item in the list
  const lastMovieRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Setup Intersection Observer for lazy loading
  useEffect(() => {
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Don't observe if we're loading or there are no more items
    if (loading || !hasMore) return;

    // Create new observer
    const observer = new IntersectionObserver(
      (entries) => {
        // If the last item is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          console.log('Last movie is visible, loading more...');
          // Add a small delay to make loading feel more natural
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.1, rootMargin: '0px 0px 500px 0px' } // Load when item is just 10% visible or 500px before it comes into view
    );

    // Get the last item element
    const lastElement = lastMovieRef.current;
    if (lastElement) {
      observer.observe(lastElement);
    }

    // Save observer to ref
    observerRef.current = observer;

    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loading, hasMore, movies.length]);

  // Get appropriate image source for a movie
  const getImageSource = (video: MovieVideo): string => {
    // If we have a thumbnail, use it and log it for debugging
    if (video.thumbnail_url) {
      // console.log(`Using thumbnail from API: ${video.video_thumbnail}`);
      return video.thumbnail_url;
    }

    // Try to extract YouTube thumbnail if it's a YouTube video
    if (video.media_url && video.media_url.includes('youtube')) {
      const videoId = getYoutubeId(video.media_url);
      if (videoId) {
        const youtubeThumbnail = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        // console.log(`Using YouTube thumbnail: ${youtubeThumbnail}`);
        return youtubeThumbnail;
      }
    }

    // Default fallback - use a local placeholder image
    console.log('Using fallback placeholder image for video:', video.caption);
    return '/pics/placeholder.svg';
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Handle different YouTube URL formats
    if (url.includes('youtu.be/')) {
      // Short URL format: https://youtu.be/VIDEO_ID
      const id = url.split('youtu.be/')[1]?.split(/[?&]/)[0];
      return id || '';
    } else if (url.includes('youtube.com/watch')) {
      // Standard format: https://www.youtube.com/watch?v=VIDEO_ID
      const params = new URLSearchParams(url.split('?')[1] || '');
      return params.get('v') || '';
    } else if (url.includes('youtube.com/embed/')) {
      // Embed format: https://www.youtube.com/embed/VIDEO_ID
      return url.split('embed/')[1]?.split(/[?&]/)[0] || '';
    }

    // Fallback to regex for other formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2]?.length === 11) ? match[2] : '';
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for movie thumbnail:', url);
      return url;
    }

    // For YouTube thumbnails
    if (url.includes('img.youtube.com')) {
      // console.log('Using YouTube thumbnail URL:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string) => {
    // If we have a userId, navigate to that specific user's profile using the dynamic route
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // If we only have a username but no ID, create a temporary ID based on the username
      const tempId = username.toLowerCase().replace(/\s+/g, '-');
      router.push(`/profile/${tempId}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  const toggleLike = (videoId: string) => {
    setLikedVideos((prev) =>
      prev.includes(videoId)
        ? prev.filter((id) => id !== videoId)
        : [...prev, videoId]
    );
  };

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Wedding Videos</h1>
              <div className="text-sm text-gray-500">
                Showing {movies.length} items | Page {page}
              </div>
            </div>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading wedding videos</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {movies.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                {movies.map((video, index) => {
                  // Determine if this is the last item to observe
                  const isLastItem = index === movies.length - 1;

                  return (
                    <div
                      key={`${video.media_id}-${index}`}
                      // Apply ref to the last item for intersection observer
                      ref={isLastItem ? lastMovieRef : null}
                      className={`flex-shrink-0 border-b pb-8 ${isLastItem ? 'border-green-500 animate-pulse' : 'border-gray-200'}`}
                    >
                      {/* Section 1 - User Info */}
                      <div className="flex justify-between items-center p-2 mb-4">
                        <div className="flex items-center gap-3">
                          <div className="cursor-pointer" onClick={() => navigateToUserProfile(video.user_id, video.user_name)}>
                            {video.user_avatar ? (
                              <Image src={video.user_avatar} alt={video.user_name} width={34} height={34} className="rounded-full" />
                            ) : (
                              <UserAvatar username={video.user_name || "user"} size="sm" />
                            )}
                          </div>
                          <span
                            className="font-semibold cursor-pointer hover:underline"
                            onClick={() => navigateToUserProfile(video.user_id, video.user_name)}
                          >
                            {video.user_name || "user"}
                          </span>
                        </div>
                        <button>•••</button>
                      </div>

                      {/* Section 2 - Video */}
                      <div className="relative w-full aspect-video rounded-lg overflow-hidden border border-gray-200 mb-4">
                        <div className="relative w-full h-full rounded-[10px] overflow-hidden">
                          <Image
                            src={processImageUrl(getImageSource(video))}
                            alt={video.caption || "Wedding Video"}
                            fill
                            sizes="(max-width: 768px) 100vw, 800px"
                            className="object-cover"
                            priority={index < 2}
                            unoptimized={true}
                            placeholder="empty"
                            onLoadingComplete={(result) => {
                              if (result.naturalWidth === 0) {
                                const imgElement = result as unknown as HTMLImageElement;
                                if (imgElement && imgElement.src) {
                                  imgElement.src = '/pics/placeholder.svg';
                                }
                              }
                            }}
                            onError={(e) => {
                              console.error(`Failed to load image for video: ${video.caption}`);
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement) {
                                imgElement.src = '/pics/placeholder.svg';
                              }
                            }}
                          />

                          {/* Play Button Overlay */}
                          <div
                            className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-colors cursor-pointer"
                            onClick={() => {
                              if (video.media_id) {
                                window.location.href = `/home/<USER>/${video.media_id}`;
                              }
                            }}
                          >
                            <div className="w-14 h-14 rounded-full bg-red-500 bg-opacity-80 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z" /></svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Section 3 - Interactions */}
                      <div className="flex flex-col gap-3">
                        <div className="flex gap-4 items-center">
                          <button onClick={() => toggleLike(video.media_id)}>
                            {video.is_liked ? (
                              <MdFavorite size={24} color="#B31B1E" />
                            ) : (
                              <MdFavoriteBorder size={24} />
                            )}
                          </button>
                          <button>
                            <MdOutlineModeComment size={24} />
                          </button>
                          <button>
                            <MdOutlineShare size={24} />
                          </button>
                        </div>

                        <div className="flex gap-4 text-sm text-gray-600">
                          <span className={video.is_liked ? "text-red-600" : ""}>
                            {video.likes_count ? `${(video.likes_count / 1000).toFixed(1)}K` : '0'} likes
                          </span>
                          <span>{video.views_count ? `${(video.views_count / 1000).toFixed(1)}K` : '0'} views</span>
                        </div>

                        <div className="text-sm">
                          <span>{video.caption}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && movies.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more videos to load</div>
            )}

            {/* No content state */}
            {!loading && movies.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No wedding videos available</div>
            )}
          </div>
        </main>

        {/* Right sidebar removed */}
      </div>

      <MobileNavigation />
    </div>
  );
}
