import React, { useState } from 'react';

interface IndianTraditionalTemplateProps {
  data: {
    partner1_name?: string;
    partner2_name?: string;
    wedding_date?: string;
    wedding_venue?: string;
    muhurtam_time?: string;
    reception_date?: string;
    reception_venue?: string;
    reception_time?: string;
    couple_photo?: string;
    custom_message?: string;
    colors?: {
      primary?: string;
      secondary?: string;
      background?: string;
      text?: string;
      accent?: string;
    };
    fonts?: {
      heading?: string | string[];
      body?: string | string[];
      decorative?: string | string[];
    };
  };
  isEditing?: boolean;
  onTextChange?: (field: string, value: string) => void;
}

const IndianTraditionalTemplate: React.FC<IndianTraditionalTemplateProps> = ({ data }) => {
  // Helper function to handle font values (can be string or array)
  const getFontFamily = (fontValue: string | string[] | undefined, fallback: string): string => {
    if (!fontValue) return fallback;
    if (Array.isArray(fontValue)) return fontValue.join(', ');
    return fontValue;
  };

  const {
    partner1_name = "Navneet",
    partner2_name = "Suknya",
    wedding_date = "Monday, 22th Aug 2022",
    wedding_venue = "Unitech Unihomes Plots Sec -Mu Greater Noida",
    muhurtam_time = "7:00 Pm",
    reception_date = "Monday, 23th Aug 2022",
    reception_venue = "Unitech Unihomes Plots Sec -Mu Greater Noida",
    reception_time = "10:00 To 11:00 Am",
    couple_photo = "",
    custom_message = "May your love story be as magical and charming as in fairy tales!",
    colors = {
      primary: '#B31B1E',
      secondary: '#FFD700',
      background: '#FFFFFF',
      text: '#000000',
      accent: '#FF8C00'
    },
    fonts = {
      heading: ['Playfair Display', 'Georgia', 'serif'],
      body: ['Roboto', 'Arial', 'sans-serif'],
      decorative: ['Dancing Script', 'cursive']
    }
  } = data;

  const styles = {
    container: {
      maxWidth: '400px',
      width: '100%',
      background: colors.primary || '#B31B1E',
      borderRadius: '0px',
      overflow: 'hidden',
      boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
      position: 'relative' as const,
      margin: '0 auto',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif'),
      aspectRatio: '3/4'
    },
    innerContainer: {
      background: colors.background || '#FFFFFF',
      margin: '20px',
      borderRadius: '10px',
      padding: '20px',
      height: 'calc(100% - 40px)',
      position: 'relative' as const
    },
    marigoldBorder: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '10px 0',
      borderTop: `3px solid ${colors.secondary || '#FFD700'}`,
      borderBottom: `3px solid ${colors.secondary || '#FFD700'}`
    },
    marigoldDot: {
      width: '12px',
      height: '12px',
      borderRadius: '50%',
      backgroundColor: colors.secondary || '#FFD700'
    },
    ganeshSection: {
      textAlign: 'center' as const,
      padding: '20px 0',
      background: 'transparent'
    },
    ganeshSymbol: {
      width: '60px',
      height: '60px',
      backgroundColor: '#8A2BE2',
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 auto 15px',
      fontSize: '32px',
      color: 'white'
    },
    ganeshText: {
      fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),
      fontSize: '18px',
      color: colors.primary || '#B31B1E',
      fontWeight: '700',
      marginBottom: '30px'
    },
    coupleNames: {
      fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),
      fontSize: '36px',
      fontWeight: '700',
      color: colors.primary || '#B31B1E',
      textAlign: 'center' as const,
      margin: '20px 0',
      lineHeight: '1.2'
    },
    andSymbol: {
      fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),
      fontSize: '28px',
      color: '#333',
      margin: '0 10px',
      fontWeight: '400'
    },
    invitationText: {
      textAlign: 'center' as const,
      padding: '20px 0',
      fontSize: '14px',
      color: '#333',
      lineHeight: '1.5',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')
    },
    eventDetails: {
      background: colors.primary || '#B31B1E',
      color: 'white',
      padding: '25px 20px',
      textAlign: 'center' as const,
      margin: '20px 0',
      borderRadius: '8px'
    },
    eventTitle: {
      fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),
      fontSize: '20px',
      fontWeight: '700',
      marginBottom: '10px',
      color: colors.secondary || '#FFD700'
    },
    eventInfo: {
      margin: '8px 0',
      fontSize: '14px',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')
    },
    eventDate: {
      fontSize: '16px',
      fontWeight: '500',
      margin: '15px 0',
      color: colors.secondary || '#FFD700',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')
    },
    decorativeDivider: {
      width: '60px',
      height: '2px',
      background: colors.secondary || '#FFD700',
      margin: '15px auto',
      borderRadius: '1px'
    },
    couplePhoto: {
      textAlign: 'center' as const,
      padding: '30px',
      background: '#FFF8E7'
    },
    photoPlaceholder: {
      width: '200px',
      height: '200px',
      borderRadius: '50%',
      background: `linear-gradient(135deg, ${colors.secondary}, ${colors.accent})`,
      margin: '0 auto 20px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '18px',
      color: colors.primary,
      fontWeight: '500',
      border: `5px solid ${colors.primary}`,
      backgroundImage: couple_photo ? `url(${couple_photo})` : 'none',
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    },
    blessingText: {
      textAlign: 'center' as const,
      padding: '15px 10px',
      fontStyle: 'italic',
      color: '#666',
      fontSize: '12px',
      lineHeight: '1.4',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')
    }
  };

  return (
    <div style={styles.container}>
      {/* Marigold border at top */}
      <div style={styles.marigoldBorder}>
        {Array.from({ length: 20 }, (_, i) => (
          <div key={i} style={styles.marigoldDot}></div>
        ))}
      </div>

      <div style={styles.innerContainer}>
        <div style={styles.ganeshSection}>
          <div style={styles.ganeshSymbol}>🕉️</div>
          <div style={styles.ganeshText}>|| Shree Ganesh Namah ||</div>
        </div>

        <div style={styles.coupleNames}>
          {partner1_name}<span style={styles.andSymbol}>&</span>{partner2_name}
        </div>

        <div style={styles.invitationText}>
          We Invite You to Share in our Joy And Request<br />
          Your Presence At the Reception
        </div>

        <div style={styles.eventDetails}>
          <div style={styles.eventTitle}>✦ Muhurtam ✦</div>
          <div style={styles.eventInfo}>Time {muhurtam_time}</div>
          <div style={styles.eventDate}>On {wedding_date}</div>
          <div style={styles.eventInfo}>{wedding_venue}</div>

          <div style={styles.decorativeDivider}></div>

          <div style={styles.eventTitle}>✦ Reception ✦</div>
          <div style={styles.eventInfo}>Time {reception_time}</div>
          <div style={styles.eventDate}>On {reception_date}</div>
          <div style={styles.eventInfo}>{reception_venue}</div>
        </div>

        {couple_photo && (
          <div style={styles.couplePhoto}>
            <div style={styles.photoPlaceholder}>
              {!couple_photo && "Couple Photo"}
            </div>
          </div>
        )}

        <div style={styles.blessingText}>
          "{custom_message}"
        </div>
      </div>

      {/* Marigold border at bottom */}
      <div style={styles.marigoldBorder}>
        {Array.from({ length: 20 }, (_, i) => (
          <div key={i} style={styles.marigoldDot}></div>
        ))}
      </div>
    </div>
  );
};

export default IndianTraditionalTemplate;
