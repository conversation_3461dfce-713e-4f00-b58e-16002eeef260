"use client";
import React, { useState, useEffect } from "react";
import { ArrowLeft, Upload, Search, Loader2 } from "lucide-react";

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  design_settings: {
    colors?: {
      primary?: string;
      secondary?: string;
      background?: string;
      text?: string;
      [key: string]: string | undefined;
    };
    fonts?: {
      heading?: string;
      body?: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
      [key: string]: string | undefined;
    };
    customImage?: string;
    couple_names?: string;
    type?: string;
  };
  deployed_url: string;
  is_published: boolean;
}

interface EInviteEditorProps {
  templates: Template[];
  einvite: EInvite | null;
  onSave: (einviteData: any) => void;
  onCancel: () => void;
  loading: boolean;
}

const EInviteEditor: React.FC<EInviteEditorProps> = ({
  templates,
  einvite,
  onSave,
  onCancel,
  loading
}) => {
  const [title, setTitle] = useState(einvite?.title || "Our Wedding E-Invite");
  const [weddingDate, setWeddingDate] = useState(einvite?.wedding_date || "");
  const [weddingLocation, setWeddingLocation] = useState(einvite?.wedding_location || "");
  const [aboutCouple, setAboutCouple] = useState(einvite?.about_couple || "");
  const [coupleNames, setCoupleNames] = useState(einvite?.couple_names || einvite?.design_settings?.couple_names || "");
  const [designSettings, setDesignSettings] = useState(einvite?.design_settings || {
    colors: {
      primary: "#B31B1E",
      secondary: "#333333",
      background: "#FFFFFF",
      text: "#000000"
    },
    fonts: {
      heading: "Playfair Display",
      body: "Open Sans",
      coupleNames: "Dancing Script",
      date: "Open Sans",
      location: "Open Sans",
      aboutCouple: "Open Sans"
    },
    customImage: "",
    type: "einvite"
  });

  const [activeTab, setActiveTab] = useState<'content' | 'design'>('content');
  const [uploadingImage, setUploadingImage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchingImages, setSearchingImages] = useState(false);

  const fontOptions = [
    "Arial", "Helvetica", "Times New Roman", "Georgia", "Verdana",
    "Playfair Display", "Open Sans", "Lato", "Roboto", "Dancing Script",
    "Great Vibes", "Pacifico", "Lobster", "Montserrat", "Poppins"
  ];

  const handleColorChange = (colorType: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  const handleFontChange = (fontType: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [fontType]: value
      }
    }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadingImage(true);
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setDesignSettings(prev => ({
          ...prev,
          customImage: data.imageUrl
        }));
      } else {
        console.error('Failed to upload image');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  const searchImages = async () => {
    if (!searchQuery.trim()) return;

    setSearchingImages(true);
    try {
      const response = await fetch(`/api/search-images?q=${encodeURIComponent(searchQuery)}`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.images || []);
      }
    } catch (error) {
      console.error('Error searching images:', error);
    } finally {
      setSearchingImages(false);
    }
  };

  const selectSearchImage = (imageUrl: string) => {
    setDesignSettings(prev => ({
      ...prev,
      customImage: imageUrl
    }));
    setSearchResults([]);
    setSearchQuery("");
  };

  const handleSave = () => {
    // Process colors to ensure they're valid hex codes
    const processedColors = { ...designSettings.colors };
    Object.keys(processedColors).forEach(key => {
      const color = processedColors[key];
      if (color && !color.startsWith('#')) {
        processedColors[key] = `#${color}`;
      }
    });

    // Process fonts to ensure they're valid
    const processedFonts = { ...designSettings.fonts };
    Object.keys(processedFonts).forEach(key => {
      if (!processedFonts[key]) {
        processedFonts[key] = fontOptions[0];
      }
    });

    const einviteData = {
      title,
      template_id: einvite?.template_id || templates[0]?.template_id,
      wedding_date: weddingDate || null,
      wedding_location: weddingLocation,
      about_couple: aboutCouple,
      couple_names: coupleNames,
      design_settings: {
        colors: processedColors,
        fonts: processedFonts,
        customImage: designSettings.customImage || '',
        couple_names: coupleNames,
        type: 'einvite' // This marks it as an e-invite instead of a regular website
      },
      is_published: true
    };

    onSave(einviteData);
  };

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-4">
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <h2 className="text-2xl font-bold text-black">
            {einvite ? 'Edit E-Invite' : 'Create E-Invite'}
          </h2>
        </div>
        <button
          onClick={handleSave}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
        >
          {loading && <Loader2 size={16} className="animate-spin" />}
          {einvite ? 'Update E-Invite' : 'Create E-Invite'}
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b">
        <button
          onClick={() => setActiveTab('content')}
          className={`px-6 py-3 font-medium transition-colors ${
            activeTab === 'content'
              ? 'text-[#B31B1E] border-b-2 border-[#B31B1E]'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          Content
        </button>
        <button
          onClick={() => setActiveTab('design')}
          className={`px-6 py-3 font-medium transition-colors ${
            activeTab === 'design'
              ? 'text-[#B31B1E] border-b-2 border-[#B31B1E]'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          Design
        </button>
      </div>

      <div className="p-6">
        {activeTab === 'content' && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <label className="block text-sm font-medium text-black mb-2">
                E-Invite Title
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                placeholder="Our Wedding E-Invite"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2">
                Couple Names
              </label>
              <input
                type="text"
                value={coupleNames}
                onChange={(e) => setCoupleNames(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                placeholder="Jane & John"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-black mb-2">
                  Wedding Date
                </label>
                <input
                  type="date"
                  value={weddingDate}
                  onChange={(e) => setWeddingDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-black mb-2">
                  Wedding Location
                </label>
                <input
                  type="text"
                  value={weddingLocation}
                  onChange={(e) => setWeddingLocation(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                  placeholder="New York, NY"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-black mb-2">
                About the Couple
              </label>
              <textarea
                value={aboutCouple}
                onChange={(e) => setAboutCouple(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                placeholder="Tell your love story..."
              />
            </div>
          </div>
        )}

        {activeTab === 'design' && (
          <div className="space-y-8">
            {/* Colors Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-black">Colors</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(designSettings.colors || {}).map(([colorType, colorValue]) => (
                  <div key={colorType}>
                    <label className="block text-sm font-medium text-black mb-2 capitalize">
                      {colorType}
                    </label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        value={colorValue || '#000000'}
                        onChange={(e) => handleColorChange(colorType, e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={colorValue || ''}
                        onChange={(e) => handleColorChange(colorType, e.target.value)}
                        className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black"
                        placeholder="#000000"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Fonts Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-black">Fonts</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(designSettings.fonts || {}).map(([fontType, fontValue]) => (
                  <div key={fontType}>
                    <label className="block text-sm font-medium text-black mb-2 capitalize">
                      {fontType.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <select
                      value={fontValue || fontOptions[0]}
                      onChange={(e) => handleFontChange(fontType, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                      style={{ fontFamily: fontValue || fontOptions[0] }}
                    >
                      {fontOptions.map((font) => (
                        <option key={font} value={font} style={{ fontFamily: font }}>
                          {font}
                        </option>
                      ))}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Background Image Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4 text-black">Background Image</h3>
              
              {/* Upload Image */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-black mb-2">
                  Upload Image
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer"
                  >
                    {uploadingImage ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Upload size={16} />
                    )}
                    Upload Image
                  </label>
                </div>
              </div>

              {/* Search Images */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-black mb-2">
                  Search Images
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B31B1E] text-black"
                    placeholder="Search for wedding images..."
                    onKeyPress={(e) => e.key === 'Enter' && searchImages()}
                  />
                  <button
                    onClick={searchImages}
                    disabled={searchingImages}
                    className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                  >
                    {searchingImages ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Search size={16} />
                    )}
                    Search
                  </button>
                </div>
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-4">
                  {searchResults.slice(0, 12).map((image, index) => (
                    <div
                      key={index}
                      className="aspect-square bg-gray-200 rounded cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => selectSearchImage(image.webformatURL)}
                    >
                      <img
                        src={image.webformatURL}
                        alt={image.tags}
                        className="w-full h-full object-cover rounded"
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Current Image Preview */}
              {designSettings.customImage && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-black mb-2">Current Background:</p>
                  <div className="w-32 h-20 bg-gray-200 rounded overflow-hidden">
                    <img
                      src={designSettings.customImage}
                      alt="Background"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Preview Section */}
        <div className="mt-8 border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-2 text-black">Preview</h3>
          <div className="relative overflow-hidden rounded-lg border shadow-md" style={{ height: '500px' }}>
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: designSettings.customImage ? `url(${designSettings.customImage})` : 'none',
                opacity: 0.7,
                backgroundColor: designSettings.colors?.background || '#FFFFFF',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
              }}
            />
            
            {/* Content Overlay */}
            <div className="relative z-10 h-full flex flex-col justify-center items-center text-center p-8">
              <h1
                className="text-4xl font-bold mb-4"
                style={{
                  color: designSettings.colors?.primary || '#B31B1E',
                  fontFamily: designSettings.fonts?.coupleNames || 'Dancing Script'
                }}
              >
                {coupleNames || 'Jane & John'}
              </h1>
              
              <h2
                className="text-2xl mb-6"
                style={{
                  color: designSettings.colors?.text || '#000000',
                  fontFamily: designSettings.fonts?.heading || 'Playfair Display'
                }}
              >
                {title || 'Our Wedding E-Invite'}
              </h2>
              
              {weddingDate && (
                <p
                  className="text-lg mb-2"
                  style={{
                    color: designSettings.colors?.secondary || '#333333',
                    fontFamily: designSettings.fonts?.date || 'Open Sans'
                  }}
                >
                  {new Date(weddingDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              )}
              
              {weddingLocation && (
                <p
                  className="text-lg mb-6"
                  style={{
                    color: designSettings.colors?.secondary || '#333333',
                    fontFamily: designSettings.fonts?.location || 'Open Sans'
                  }}
                >
                  {weddingLocation}
                </p>
              )}
              
              {aboutCouple && (
                <p
                  className="text-sm max-w-md"
                  style={{
                    color: designSettings.colors?.text || '#000000',
                    fontFamily: designSettings.fonts?.aboutCouple || 'Open Sans'
                  }}
                >
                  {aboutCouple}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EInviteEditor;
