"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '400px',\n            width: '100%',\n            background: colors.primary || '#B31B1E',\n            borderRadius: '0px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif'),\n            aspectRatio: '3/4'\n        },\n        innerContainer: {\n            background: colors.background || '#FFFFFF',\n            margin: '20px',\n            borderRadius: '10px',\n            padding: '20px',\n            height: 'calc(100% - 40px)',\n            position: 'relative'\n        },\n        marigoldBorder: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '10px 0',\n            borderTop: \"3px solid \".concat(colors.secondary || '#FFD700'),\n            borderBottom: \"3px solid \".concat(colors.secondary || '#FFD700')\n        },\n        marigoldDot: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: colors.secondary || '#FFD700'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '20px 0',\n            background: 'transparent'\n        },\n        ganeshSymbol: {\n            width: '60px',\n            height: '60px',\n            backgroundColor: '#8A2BE2',\n            borderRadius: '8px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            margin: '0 auto 15px',\n            fontSize: '32px',\n            color: 'white'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '18px',\n            color: colors.primary || '#B31B1E',\n            fontWeight: '700',\n            marginBottom: '30px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '36px',\n            fontWeight: '700',\n            color: colors.primary || '#B31B1E',\n            textAlign: 'center',\n            margin: '20px 0',\n            lineHeight: '1.2'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '28px',\n            color: '#333',\n            margin: '0 10px',\n            fontWeight: '400'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 0',\n            fontSize: '14px',\n            color: '#333',\n            lineHeight: '1.5',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        eventDetails: {\n            background: colors.primary || '#B31B1E',\n            color: 'white',\n            padding: '25px 20px',\n            textAlign: 'center',\n            margin: '20px 0',\n            borderRadius: '8px'\n        },\n        eventTitle: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '20px',\n            fontWeight: '700',\n            marginBottom: '10px',\n            color: colors.secondary || '#FFD700'\n        },\n        eventInfo: {\n            margin: '8px 0',\n            fontSize: '14px',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        eventDate: {\n            fontSize: '16px',\n            fontWeight: '500',\n            margin: '15px 0',\n            color: colors.secondary || '#FFD700',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        decorativeDivider: {\n            width: '60px',\n            height: '2px',\n            background: colors.secondary || '#FFD700',\n            margin: '15px auto',\n            borderRadius: '1px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '15px 10px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '12px',\n            lineHeight: '1.4',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder,\n                children: Array.from({\n                    length: 20\n                }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.marigoldDot\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.innerContainer,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.ganeshSymbol,\n                                children: \"\\uD83D\\uDD49️\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.ganeshText,\n                                children: \"|| Shree Ganesh Namah ||\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.coupleNames,\n                        children: [\n                            partner1_name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: styles.andSymbol,\n                                children: \"&\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 26\n                            }, undefined),\n                            partner2_name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.invitationText,\n                        children: [\n                            \"We Invite You to Share in our Joy And Request\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 56\n                            }, undefined),\n                            \"Your Presence At the Reception\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDetails,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventTitle,\n                                children: \"✦ Muhurtam ✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: [\n                                    \"Time \",\n                                    muhurtam_time\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventDate,\n                                children: [\n                                    \"On \",\n                                    wedding_date\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: wedding_venue\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.decorativeDivider\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventTitle,\n                                children: \"✦ Reception ✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: [\n                                    \"Time \",\n                                    reception_time\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventDate,\n                                children: [\n                                    \"On \",\n                                    reception_date\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: reception_venue\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined),\n                    couple_photo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.couplePhoto,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: styles.photoPlaceholder,\n                            children: !couple_photo && \"Couple Photo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.blessingText,\n                        children: [\n                            '\"',\n                            custom_message,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder,\n                children: Array.from({\n                    length: 20\n                }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.marigoldDot\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9ob21lL2UtaW52aXRlcy9jb21wb25lbnRzL3RlbXBsYXRlcy9JbmRpYW5UcmFkaXRpb25hbFRlbXBsYXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQjtBQTZCMUIsTUFBTUMsNEJBQXNFO1FBQUMsRUFBRUMsSUFBSSxFQUFFO0lBQ25GLGlFQUFpRTtJQUNqRSxNQUFNQyxnQkFBZ0IsQ0FBQ0MsV0FBMENDO1FBQy9ELElBQUksQ0FBQ0QsV0FBVyxPQUFPQztRQUN2QixJQUFJQyxNQUFNQyxPQUFPLENBQUNILFlBQVksT0FBT0EsVUFBVUksSUFBSSxDQUFDO1FBQ3BELE9BQU9KO0lBQ1Q7SUFFQSxNQUFNLEVBQ0pLLGdCQUFnQixTQUFTLEVBQ3pCQyxnQkFBZ0IsUUFBUSxFQUN4QkMsZUFBZSx1QkFBdUIsRUFDdENDLGdCQUFnQiw4Q0FBOEMsRUFDOURDLGdCQUFnQixTQUFTLEVBQ3pCQyxpQkFBaUIsdUJBQXVCLEVBQ3hDQyxrQkFBa0IsOENBQThDLEVBQ2hFQyxpQkFBaUIsbUJBQW1CLEVBQ3BDQyxlQUFlLEVBQUUsRUFDakJDLGlCQUFpQixtRUFBbUUsRUFDcEZDLFNBQVM7UUFDUEMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFlBQVk7UUFDWkMsTUFBTTtRQUNOQyxRQUFRO0lBQ1YsQ0FBQyxFQUNEQyxRQUFRO1FBQ05DLFNBQVM7WUFBQztZQUFvQjtZQUFXO1NBQVE7UUFDakRDLE1BQU07WUFBQztZQUFVO1lBQVM7U0FBYTtRQUN2Q0MsWUFBWTtZQUFDO1lBQWtCO1NBQVU7SUFDM0MsQ0FBQyxFQUNGLEdBQUcxQjtJQUVKLE1BQU0yQixTQUFTO1FBQ2JDLFdBQVc7WUFDVEMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BWLFlBQVlILE9BQU9DLE9BQU8sSUFBSTtZQUM5QmEsY0FBYztZQUNkQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLFlBQVluQyxjQUFjc0IsTUFBTUUsSUFBSSxFQUFFO1lBQ3RDWSxhQUFhO1FBQ2Y7UUFDQUMsZ0JBQWdCO1lBQ2RsQixZQUFZSCxPQUFPRyxVQUFVLElBQUk7WUFDakNlLFFBQVE7WUFDUkosY0FBYztZQUNkUSxTQUFTO1lBQ1RDLFFBQVE7WUFDUk4sVUFBVTtRQUNaO1FBQ0FPLGdCQUFnQjtZQUNkQyxTQUFTO1lBQ1RDLGdCQUFnQjtZQUNoQkMsWUFBWTtZQUNaTCxTQUFTO1lBQ1RNLFdBQVcsYUFBMkMsT0FBOUI1QixPQUFPRSxTQUFTLElBQUk7WUFDNUMyQixjQUFjLGFBQTJDLE9BQTlCN0IsT0FBT0UsU0FBUyxJQUFJO1FBQ2pEO1FBQ0E0QixhQUFhO1lBQ1hqQixPQUFPO1lBQ1BVLFFBQVE7WUFDUlQsY0FBYztZQUNkaUIsaUJBQWlCL0IsT0FBT0UsU0FBUyxJQUFJO1FBQ3ZDO1FBQ0E4QixlQUFlO1lBQ2JDLFdBQVc7WUFDWFgsU0FBUztZQUNUbkIsWUFBWTtRQUNkO1FBQ0ErQixjQUFjO1lBQ1pyQixPQUFPO1lBQ1BVLFFBQVE7WUFDUlEsaUJBQWlCO1lBQ2pCakIsY0FBYztZQUNkVyxTQUFTO1lBQ1RFLFlBQVk7WUFDWkQsZ0JBQWdCO1lBQ2hCUixRQUFRO1lBQ1JpQixVQUFVO1lBQ1ZDLE9BQU87UUFDVDtRQUNBQyxZQUFZO1lBQ1ZsQixZQUFZbkMsY0FBY3NCLE1BQU1HLFVBQVUsRUFBRTtZQUM1QzBCLFVBQVU7WUFDVkMsT0FBT3BDLE9BQU9DLE9BQU8sSUFBSTtZQUN6QnFDLFlBQVk7WUFDWkMsY0FBYztRQUNoQjtRQUNBQyxhQUFhO1lBQ1hyQixZQUFZbkMsY0FBY3NCLE1BQU1DLE9BQU8sRUFBRTtZQUN6QzRCLFVBQVU7WUFDVkcsWUFBWTtZQUNaRixPQUFPcEMsT0FBT0MsT0FBTyxJQUFJO1lBQ3pCZ0MsV0FBVztZQUNYZixRQUFRO1lBQ1J1QixZQUFZO1FBQ2Q7UUFDQUMsV0FBVztZQUNUdkIsWUFBWW5DLGNBQWNzQixNQUFNRyxVQUFVLEVBQUU7WUFDNUMwQixVQUFVO1lBQ1ZDLE9BQU87WUFDUGxCLFFBQVE7WUFDUm9CLFlBQVk7UUFDZDtRQUNBSyxnQkFBZ0I7WUFDZFYsV0FBVztZQUNYWCxTQUFTO1lBQ1RhLFVBQVU7WUFDVkMsT0FBTztZQUNQSyxZQUFZO1lBQ1p0QixZQUFZbkMsY0FBY3NCLE1BQU1FLElBQUksRUFBRTtRQUN4QztRQUNBb0MsY0FBYztZQUNaekMsWUFBWUgsT0FBT0MsT0FBTyxJQUFJO1lBQzlCbUMsT0FBTztZQUNQZCxTQUFTO1lBQ1RXLFdBQVc7WUFDWGYsUUFBUTtZQUNSSixjQUFjO1FBQ2hCO1FBQ0ErQixZQUFZO1lBQ1YxQixZQUFZbkMsY0FBY3NCLE1BQU1DLE9BQU8sRUFBRTtZQUN6QzRCLFVBQVU7WUFDVkcsWUFBWTtZQUNaQyxjQUFjO1lBQ2RILE9BQU9wQyxPQUFPRSxTQUFTLElBQUk7UUFDN0I7UUFDQTRDLFdBQVc7WUFDVDVCLFFBQVE7WUFDUmlCLFVBQVU7WUFDVmhCLFlBQVluQyxjQUFjc0IsTUFBTUUsSUFBSSxFQUFFO1FBQ3hDO1FBQ0F1QyxXQUFXO1lBQ1RaLFVBQVU7WUFDVkcsWUFBWTtZQUNacEIsUUFBUTtZQUNSa0IsT0FBT3BDLE9BQU9FLFNBQVMsSUFBSTtZQUMzQmlCLFlBQVluQyxjQUFjc0IsTUFBTUUsSUFBSSxFQUFFO1FBQ3hDO1FBQ0F3QyxtQkFBbUI7WUFDakJuQyxPQUFPO1lBQ1BVLFFBQVE7WUFDUnBCLFlBQVlILE9BQU9FLFNBQVMsSUFBSTtZQUNoQ2dCLFFBQVE7WUFDUkosY0FBYztRQUNoQjtRQUNBbUMsYUFBYTtZQUNYaEIsV0FBVztZQUNYWCxTQUFTO1lBQ1RuQixZQUFZO1FBQ2Q7UUFDQStDLGtCQUFrQjtZQUNoQnJDLE9BQU87WUFDUFUsUUFBUTtZQUNSVCxjQUFjO1lBQ2RYLFlBQVksMkJBQWdESCxPQUFyQkEsT0FBT0UsU0FBUyxFQUFDLE1BQWtCLE9BQWRGLE9BQU9LLE1BQU0sRUFBQztZQUMxRWEsUUFBUTtZQUNSTyxTQUFTO1lBQ1RFLFlBQVk7WUFDWkQsZ0JBQWdCO1lBQ2hCUyxVQUFVO1lBQ1ZDLE9BQU9wQyxPQUFPQyxPQUFPO1lBQ3JCcUMsWUFBWTtZQUNaYSxRQUFRLGFBQTRCLE9BQWZuRCxPQUFPQyxPQUFPO1lBQ25DbUQsaUJBQWlCdEQsZUFBZSxPQUFvQixPQUFiQSxjQUFhLE9BQUs7WUFDekR1RCxnQkFBZ0I7WUFDaEJDLG9CQUFvQjtRQUN0QjtRQUNBQyxjQUFjO1lBQ1p0QixXQUFXO1lBQ1hYLFNBQVM7WUFDVGtDLFdBQVc7WUFDWHBCLE9BQU87WUFDUEQsVUFBVTtZQUNWTSxZQUFZO1lBQ1p0QixZQUFZbkMsY0FBY3NCLE1BQU1FLElBQUksRUFBRTtRQUN4QztJQUNGO0lBRUEscUJBQ0UsOERBQUNpRDtRQUFJQyxPQUFPaEQsT0FBT0MsU0FBUzs7MEJBRTFCLDhEQUFDOEM7Z0JBQUlDLE9BQU9oRCxPQUFPYyxjQUFjOzBCQUM5QnJDLE1BQU13RSxJQUFJLENBQUM7b0JBQUVDLFFBQVE7Z0JBQUcsR0FBRyxDQUFDQyxHQUFHQyxrQkFDOUIsOERBQUNMO3dCQUFZQyxPQUFPaEQsT0FBT29CLFdBQVc7dUJBQTVCZ0M7Ozs7Ozs7Ozs7MEJBSWQsOERBQUNMO2dCQUFJQyxPQUFPaEQsT0FBT1csY0FBYzs7a0NBQy9CLDhEQUFDb0M7d0JBQUlDLE9BQU9oRCxPQUFPc0IsYUFBYTs7MENBQzlCLDhEQUFDeUI7Z0NBQUlDLE9BQU9oRCxPQUFPd0IsWUFBWTswQ0FBRTs7Ozs7OzBDQUNqQyw4REFBQ3VCO2dDQUFJQyxPQUFPaEQsT0FBTzJCLFVBQVU7MENBQUU7Ozs7Ozs7Ozs7OztrQ0FHakMsOERBQUNvQjt3QkFBSUMsT0FBT2hELE9BQU84QixXQUFXOzs0QkFDM0JsRDswQ0FBYyw4REFBQ3lFO2dDQUFLTCxPQUFPaEQsT0FBT2dDLFNBQVM7MENBQUU7Ozs7Ozs0QkFBU25EOzs7Ozs7O2tDQUd6RCw4REFBQ2tFO3dCQUFJQyxPQUFPaEQsT0FBT2lDLGNBQWM7OzRCQUFFOzBDQUNZLDhEQUFDcUI7Ozs7OzRCQUFLOzs7Ozs7O2tDQUlyRCw4REFBQ1A7d0JBQUlDLE9BQU9oRCxPQUFPa0MsWUFBWTs7MENBQzdCLDhEQUFDYTtnQ0FBSUMsT0FBT2hELE9BQU9tQyxVQUFVOzBDQUFFOzs7Ozs7MENBQy9CLDhEQUFDWTtnQ0FBSUMsT0FBT2hELE9BQU9vQyxTQUFTOztvQ0FBRTtvQ0FBTXBEOzs7Ozs7OzBDQUNwQyw4REFBQytEO2dDQUFJQyxPQUFPaEQsT0FBT3FDLFNBQVM7O29DQUFFO29DQUFJdkQ7Ozs7Ozs7MENBQ2xDLDhEQUFDaUU7Z0NBQUlDLE9BQU9oRCxPQUFPb0MsU0FBUzswQ0FBR3JEOzs7Ozs7MENBRS9CLDhEQUFDZ0U7Z0NBQUlDLE9BQU9oRCxPQUFPc0MsaUJBQWlCOzs7Ozs7MENBRXBDLDhEQUFDUztnQ0FBSUMsT0FBT2hELE9BQU9tQyxVQUFVOzBDQUFFOzs7Ozs7MENBQy9CLDhEQUFDWTtnQ0FBSUMsT0FBT2hELE9BQU9vQyxTQUFTOztvQ0FBRTtvQ0FBTWpEOzs7Ozs7OzBDQUNwQyw4REFBQzREO2dDQUFJQyxPQUFPaEQsT0FBT3FDLFNBQVM7O29DQUFFO29DQUFJcEQ7Ozs7Ozs7MENBQ2xDLDhEQUFDOEQ7Z0NBQUlDLE9BQU9oRCxPQUFPb0MsU0FBUzswQ0FBR2xEOzs7Ozs7Ozs7Ozs7b0JBR2hDRSw4QkFDQyw4REFBQzJEO3dCQUFJQyxPQUFPaEQsT0FBT3VDLFdBQVc7a0NBQzVCLDRFQUFDUTs0QkFBSUMsT0FBT2hELE9BQU93QyxnQkFBZ0I7c0NBQ2hDLENBQUNwRCxnQkFBZ0I7Ozs7Ozs7Ozs7O2tDQUt4Qiw4REFBQzJEO3dCQUFJQyxPQUFPaEQsT0FBTzZDLFlBQVk7OzRCQUFFOzRCQUM3QnhEOzRCQUFlOzs7Ozs7Ozs7Ozs7OzBCQUtyQiw4REFBQzBEO2dCQUFJQyxPQUFPaEQsT0FBT2MsY0FBYzswQkFDOUJyQyxNQUFNd0UsSUFBSSxDQUFDO29CQUFFQyxRQUFRO2dCQUFHLEdBQUcsQ0FBQ0MsR0FBR0Msa0JBQzlCLDhEQUFDTDt3QkFBWUMsT0FBT2hELE9BQU9vQixXQUFXO3VCQUE1QmdDOzs7Ozs7Ozs7Ozs7Ozs7O0FBS3BCO0tBbFBNaEY7QUFvUE4saUVBQWVBLHlCQUF5QkEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcYXBwXFxob21lXFxlLWludml0ZXNcXGNvbXBvbmVudHNcXHRlbXBsYXRlc1xcSW5kaWFuVHJhZGl0aW9uYWxUZW1wbGF0ZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEluZGlhblRyYWRpdGlvbmFsVGVtcGxhdGVQcm9wcyB7XG4gIGRhdGE6IHtcbiAgICBwYXJ0bmVyMV9uYW1lPzogc3RyaW5nO1xuICAgIHBhcnRuZXIyX25hbWU/OiBzdHJpbmc7XG4gICAgd2VkZGluZ19kYXRlPzogc3RyaW5nO1xuICAgIHdlZGRpbmdfdmVudWU/OiBzdHJpbmc7XG4gICAgbXVodXJ0YW1fdGltZT86IHN0cmluZztcbiAgICByZWNlcHRpb25fZGF0ZT86IHN0cmluZztcbiAgICByZWNlcHRpb25fdmVudWU/OiBzdHJpbmc7XG4gICAgcmVjZXB0aW9uX3RpbWU/OiBzdHJpbmc7XG4gICAgY291cGxlX3Bob3RvPzogc3RyaW5nO1xuICAgIGN1c3RvbV9tZXNzYWdlPzogc3RyaW5nO1xuICAgIGNvbG9ycz86IHtcbiAgICAgIHByaW1hcnk/OiBzdHJpbmc7XG4gICAgICBzZWNvbmRhcnk/OiBzdHJpbmc7XG4gICAgICBiYWNrZ3JvdW5kPzogc3RyaW5nO1xuICAgICAgdGV4dD86IHN0cmluZztcbiAgICAgIGFjY2VudD86IHN0cmluZztcbiAgICB9O1xuICAgIGZvbnRzPzoge1xuICAgICAgaGVhZGluZz86IHN0cmluZyB8IHN0cmluZ1tdO1xuICAgICAgYm9keT86IHN0cmluZyB8IHN0cmluZ1tdO1xuICAgICAgZGVjb3JhdGl2ZT86IHN0cmluZyB8IHN0cmluZ1tdO1xuICAgIH07XG4gIH07XG59XG5cbmNvbnN0IEluZGlhblRyYWRpdGlvbmFsVGVtcGxhdGU6IFJlYWN0LkZDPEluZGlhblRyYWRpdGlvbmFsVGVtcGxhdGVQcm9wcz4gPSAoeyBkYXRhIH0pID0+IHtcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGhhbmRsZSBmb250IHZhbHVlcyAoY2FuIGJlIHN0cmluZyBvciBhcnJheSlcbiAgY29uc3QgZ2V0Rm9udEZhbWlseSA9IChmb250VmFsdWU6IHN0cmluZyB8IHN0cmluZ1tdIHwgdW5kZWZpbmVkLCBmYWxsYmFjazogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICBpZiAoIWZvbnRWYWx1ZSkgcmV0dXJuIGZhbGxiYWNrO1xuICAgIGlmIChBcnJheS5pc0FycmF5KGZvbnRWYWx1ZSkpIHJldHVybiBmb250VmFsdWUuam9pbignLCAnKTtcbiAgICByZXR1cm4gZm9udFZhbHVlO1xuICB9O1xuXG4gIGNvbnN0IHtcbiAgICBwYXJ0bmVyMV9uYW1lID0gXCJOYXZuZWV0XCIsXG4gICAgcGFydG5lcjJfbmFtZSA9IFwiU3VrbnlhXCIsXG4gICAgd2VkZGluZ19kYXRlID0gXCJNb25kYXksIDIydGggQXVnIDIwMjJcIixcbiAgICB3ZWRkaW5nX3ZlbnVlID0gXCJVbml0ZWNoIFVuaWhvbWVzIFBsb3RzIFNlYyAtTXUgR3JlYXRlciBOb2lkYVwiLFxuICAgIG11aHVydGFtX3RpbWUgPSBcIjc6MDAgUG1cIixcbiAgICByZWNlcHRpb25fZGF0ZSA9IFwiTW9uZGF5LCAyM3RoIEF1ZyAyMDIyXCIsXG4gICAgcmVjZXB0aW9uX3ZlbnVlID0gXCJVbml0ZWNoIFVuaWhvbWVzIFBsb3RzIFNlYyAtTXUgR3JlYXRlciBOb2lkYVwiLFxuICAgIHJlY2VwdGlvbl90aW1lID0gXCIxMDowMCBUbyAxMTowMCBBbVwiLFxuICAgIGNvdXBsZV9waG90byA9IFwiXCIsXG4gICAgY3VzdG9tX21lc3NhZ2UgPSBcIk1heSB5b3VyIGxvdmUgc3RvcnkgYmUgYXMgbWFnaWNhbCBhbmQgY2hhcm1pbmcgYXMgaW4gZmFpcnkgdGFsZXMhXCIsXG4gICAgY29sb3JzID0ge1xuICAgICAgcHJpbWFyeTogJyNCMzFCMUUnLFxuICAgICAgc2Vjb25kYXJ5OiAnI0ZGRDcwMCcsXG4gICAgICBiYWNrZ3JvdW5kOiAnI0ZGRkZGRicsXG4gICAgICB0ZXh0OiAnIzAwMDAwMCcsXG4gICAgICBhY2NlbnQ6ICcjRkY4QzAwJ1xuICAgIH0sXG4gICAgZm9udHMgPSB7XG4gICAgICBoZWFkaW5nOiBbJ1BsYXlmYWlyIERpc3BsYXknLCAnR2VvcmdpYScsICdzZXJpZiddLFxuICAgICAgYm9keTogWydSb2JvdG8nLCAnQXJpYWwnLCAnc2Fucy1zZXJpZiddLFxuICAgICAgZGVjb3JhdGl2ZTogWydEYW5jaW5nIFNjcmlwdCcsICdjdXJzaXZlJ11cbiAgICB9XG4gIH0gPSBkYXRhO1xuXG4gIGNvbnN0IHN0eWxlcyA9IHtcbiAgICBjb250YWluZXI6IHtcbiAgICAgIG1heFdpZHRoOiAnNDAwcHgnLFxuICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgIGJhY2tncm91bmQ6IGNvbG9ycy5wcmltYXJ5IHx8ICcjQjMxQjFFJyxcbiAgICAgIGJvcmRlclJhZGl1czogJzBweCcsXG4gICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICBib3hTaGFkb3c6ICcwIDIwcHggNDBweCByZ2JhKDAsMCwwLDAuMyknLFxuICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScgYXMgY29uc3QsXG4gICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgZm9udEZhbWlseTogZ2V0Rm9udEZhbWlseShmb250cy5ib2R5LCAnUm9ib3RvLCBBcmlhbCwgc2Fucy1zZXJpZicpLFxuICAgICAgYXNwZWN0UmF0aW86ICczLzQnXG4gICAgfSxcbiAgICBpbm5lckNvbnRhaW5lcjoge1xuICAgICAgYmFja2dyb3VuZDogY29sb3JzLmJhY2tncm91bmQgfHwgJyNGRkZGRkYnLFxuICAgICAgbWFyZ2luOiAnMjBweCcsXG4gICAgICBib3JkZXJSYWRpdXM6ICcxMHB4JyxcbiAgICAgIHBhZGRpbmc6ICcyMHB4JyxcbiAgICAgIGhlaWdodDogJ2NhbGMoMTAwJSAtIDQwcHgpJyxcbiAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnIGFzIGNvbnN0XG4gICAgfSxcbiAgICBtYXJpZ29sZEJvcmRlcjoge1xuICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyxcbiAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgcGFkZGluZzogJzEwcHggMCcsXG4gICAgICBib3JkZXJUb3A6IGAzcHggc29saWQgJHtjb2xvcnMuc2Vjb25kYXJ5IHx8ICcjRkZENzAwJ31gLFxuICAgICAgYm9yZGVyQm90dG9tOiBgM3B4IHNvbGlkICR7Y29sb3JzLnNlY29uZGFyeSB8fCAnI0ZGRDcwMCd9YFxuICAgIH0sXG4gICAgbWFyaWdvbGREb3Q6IHtcbiAgICAgIHdpZHRoOiAnMTJweCcsXG4gICAgICBoZWlnaHQ6ICcxMnB4JyxcbiAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zZWNvbmRhcnkgfHwgJyNGRkQ3MDAnXG4gICAgfSxcbiAgICBnYW5lc2hTZWN0aW9uOiB7XG4gICAgICB0ZXh0QWxpZ246ICdjZW50ZXInIGFzIGNvbnN0LFxuICAgICAgcGFkZGluZzogJzIwcHggMCcsXG4gICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnXG4gICAgfSxcbiAgICBnYW5lc2hTeW1ib2w6IHtcbiAgICAgIHdpZHRoOiAnNjBweCcsXG4gICAgICBoZWlnaHQ6ICc2MHB4JyxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogJyM4QTJCRTInLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgbWFyZ2luOiAnMCBhdXRvIDE1cHgnLFxuICAgICAgZm9udFNpemU6ICczMnB4JyxcbiAgICAgIGNvbG9yOiAnd2hpdGUnXG4gICAgfSxcbiAgICBnYW5lc2hUZXh0OiB7XG4gICAgICBmb250RmFtaWx5OiBnZXRGb250RmFtaWx5KGZvbnRzLmRlY29yYXRpdmUsICdEYW5jaW5nIFNjcmlwdCwgY3Vyc2l2ZScpLFxuICAgICAgZm9udFNpemU6ICcxOHB4JyxcbiAgICAgIGNvbG9yOiBjb2xvcnMucHJpbWFyeSB8fCAnI0IzMUIxRScsXG4gICAgICBmb250V2VpZ2h0OiAnNzAwJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogJzMwcHgnXG4gICAgfSxcbiAgICBjb3VwbGVOYW1lczoge1xuICAgICAgZm9udEZhbWlseTogZ2V0Rm9udEZhbWlseShmb250cy5oZWFkaW5nLCAnUGxheWZhaXIgRGlzcGxheSwgc2VyaWYnKSxcbiAgICAgIGZvbnRTaXplOiAnMzZweCcsXG4gICAgICBmb250V2VpZ2h0OiAnNzAwJyxcbiAgICAgIGNvbG9yOiBjb2xvcnMucHJpbWFyeSB8fCAnI0IzMUIxRScsXG4gICAgICB0ZXh0QWxpZ246ICdjZW50ZXInIGFzIGNvbnN0LFxuICAgICAgbWFyZ2luOiAnMjBweCAwJyxcbiAgICAgIGxpbmVIZWlnaHQ6ICcxLjInXG4gICAgfSxcbiAgICBhbmRTeW1ib2w6IHtcbiAgICAgIGZvbnRGYW1pbHk6IGdldEZvbnRGYW1pbHkoZm9udHMuZGVjb3JhdGl2ZSwgJ0RhbmNpbmcgU2NyaXB0LCBjdXJzaXZlJyksXG4gICAgICBmb250U2l6ZTogJzI4cHgnLFxuICAgICAgY29sb3I6ICcjMzMzJyxcbiAgICAgIG1hcmdpbjogJzAgMTBweCcsXG4gICAgICBmb250V2VpZ2h0OiAnNDAwJ1xuICAgIH0sXG4gICAgaW52aXRhdGlvblRleHQ6IHtcbiAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicgYXMgY29uc3QsXG4gICAgICBwYWRkaW5nOiAnMjBweCAwJyxcbiAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgbGluZUhlaWdodDogJzEuNScsXG4gICAgICBmb250RmFtaWx5OiBnZXRGb250RmFtaWx5KGZvbnRzLmJvZHksICdSb2JvdG8sIEFyaWFsLCBzYW5zLXNlcmlmJylcbiAgICB9LFxuICAgIGV2ZW50RGV0YWlsczoge1xuICAgICAgYmFja2dyb3VuZDogY29sb3JzLnByaW1hcnkgfHwgJyNCMzFCMUUnLFxuICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICBwYWRkaW5nOiAnMjVweCAyMHB4JyxcbiAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicgYXMgY29uc3QsXG4gICAgICBtYXJnaW46ICcyMHB4IDAnLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4J1xuICAgIH0sXG4gICAgZXZlbnRUaXRsZToge1xuICAgICAgZm9udEZhbWlseTogZ2V0Rm9udEZhbWlseShmb250cy5oZWFkaW5nLCAnUGxheWZhaXIgRGlzcGxheSwgc2VyaWYnKSxcbiAgICAgIGZvbnRTaXplOiAnMjBweCcsXG4gICAgICBmb250V2VpZ2h0OiAnNzAwJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogJzEwcHgnLFxuICAgICAgY29sb3I6IGNvbG9ycy5zZWNvbmRhcnkgfHwgJyNGRkQ3MDAnXG4gICAgfSxcbiAgICBldmVudEluZm86IHtcbiAgICAgIG1hcmdpbjogJzhweCAwJyxcbiAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICBmb250RmFtaWx5OiBnZXRGb250RmFtaWx5KGZvbnRzLmJvZHksICdSb2JvdG8sIEFyaWFsLCBzYW5zLXNlcmlmJylcbiAgICB9LFxuICAgIGV2ZW50RGF0ZToge1xuICAgICAgZm9udFNpemU6ICcxNnB4JyxcbiAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgbWFyZ2luOiAnMTVweCAwJyxcbiAgICAgIGNvbG9yOiBjb2xvcnMuc2Vjb25kYXJ5IHx8ICcjRkZENzAwJyxcbiAgICAgIGZvbnRGYW1pbHk6IGdldEZvbnRGYW1pbHkoZm9udHMuYm9keSwgJ1JvYm90bywgQXJpYWwsIHNhbnMtc2VyaWYnKVxuICAgIH0sXG4gICAgZGVjb3JhdGl2ZURpdmlkZXI6IHtcbiAgICAgIHdpZHRoOiAnNjBweCcsXG4gICAgICBoZWlnaHQ6ICcycHgnLFxuICAgICAgYmFja2dyb3VuZDogY29sb3JzLnNlY29uZGFyeSB8fCAnI0ZGRDcwMCcsXG4gICAgICBtYXJnaW46ICcxNXB4IGF1dG8nLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnMXB4J1xuICAgIH0sXG4gICAgY291cGxlUGhvdG86IHtcbiAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicgYXMgY29uc3QsXG4gICAgICBwYWRkaW5nOiAnMzBweCcsXG4gICAgICBiYWNrZ3JvdW5kOiAnI0ZGRjhFNydcbiAgICB9LFxuICAgIHBob3RvUGxhY2Vob2xkZXI6IHtcbiAgICAgIHdpZHRoOiAnMjAwcHgnLFxuICAgICAgaGVpZ2h0OiAnMjAwcHgnLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke2NvbG9ycy5zZWNvbmRhcnl9LCAke2NvbG9ycy5hY2NlbnR9KWAsXG4gICAgICBtYXJnaW46ICcwIGF1dG8gMjBweCcsXG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgIGZvbnRTaXplOiAnMThweCcsXG4gICAgICBjb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgIGJvcmRlcjogYDVweCBzb2xpZCAke2NvbG9ycy5wcmltYXJ5fWAsXG4gICAgICBiYWNrZ3JvdW5kSW1hZ2U6IGNvdXBsZV9waG90byA/IGB1cmwoJHtjb3VwbGVfcGhvdG99KWAgOiAnbm9uZScsXG4gICAgICBiYWNrZ3JvdW5kU2l6ZTogJ2NvdmVyJyxcbiAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogJ2NlbnRlcidcbiAgICB9LFxuICAgIGJsZXNzaW5nVGV4dDoge1xuICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyBhcyBjb25zdCxcbiAgICAgIHBhZGRpbmc6ICcxNXB4IDEwcHgnLFxuICAgICAgZm9udFN0eWxlOiAnaXRhbGljJyxcbiAgICAgIGNvbG9yOiAnIzY2NicsXG4gICAgICBmb250U2l6ZTogJzEycHgnLFxuICAgICAgbGluZUhlaWdodDogJzEuNCcsXG4gICAgICBmb250RmFtaWx5OiBnZXRGb250RmFtaWx5KGZvbnRzLmJvZHksICdSb2JvdG8sIEFyaWFsLCBzYW5zLXNlcmlmJylcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuY29udGFpbmVyfT5cbiAgICAgIHsvKiBNYXJpZ29sZCBib3JkZXIgYXQgdG9wICovfVxuICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLm1hcmlnb2xkQm9yZGVyfT5cbiAgICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IDIwIH0sIChfLCBpKSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2l9IHN0eWxlPXtzdHlsZXMubWFyaWdvbGREb3R9PjwvZGl2PlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuaW5uZXJDb250YWluZXJ9PlxuICAgICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuZ2FuZXNoU2VjdGlvbn0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmdhbmVzaFN5bWJvbH0+8J+Vie+4jzwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5nYW5lc2hUZXh0fT58fCBTaHJlZSBHYW5lc2ggTmFtYWggfHw8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmNvdXBsZU5hbWVzfT5cbiAgICAgICAgICB7cGFydG5lcjFfbmFtZX08c3BhbiBzdHlsZT17c3R5bGVzLmFuZFN5bWJvbH0+Jjwvc3Bhbj57cGFydG5lcjJfbmFtZX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmludml0YXRpb25UZXh0fT5cbiAgICAgICAgICBXZSBJbnZpdGUgWW91IHRvIFNoYXJlIGluIG91ciBKb3kgQW5kIFJlcXVlc3Q8YnIgLz5cbiAgICAgICAgICBZb3VyIFByZXNlbmNlIEF0IHRoZSBSZWNlcHRpb25cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50RGV0YWlsc30+XG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50VGl0bGV9PuKcpiBNdWh1cnRhbSDinKY8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuZXZlbnRJbmZvfT5UaW1lIHttdWh1cnRhbV90aW1lfTwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5ldmVudERhdGV9Pk9uIHt3ZWRkaW5nX2RhdGV9PC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50SW5mb30+e3dlZGRpbmdfdmVudWV9PC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuZGVjb3JhdGl2ZURpdmlkZXJ9PjwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50VGl0bGV9PuKcpiBSZWNlcHRpb24g4pymPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50SW5mb30+VGltZSB7cmVjZXB0aW9uX3RpbWV9PC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17c3R5bGVzLmV2ZW50RGF0ZX0+T24ge3JlY2VwdGlvbl9kYXRlfTwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5ldmVudEluZm99PntyZWNlcHRpb25fdmVudWV9PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHtjb3VwbGVfcGhvdG8gJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5jb3VwbGVQaG90b30+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMucGhvdG9QbGFjZWhvbGRlcn0+XG4gICAgICAgICAgICAgIHshY291cGxlX3Bob3RvICYmIFwiQ291cGxlIFBob3RvXCJ9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuYmxlc3NpbmdUZXh0fT5cbiAgICAgICAgICBcIntjdXN0b21fbWVzc2FnZX1cIlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFyaWdvbGQgYm9yZGVyIGF0IGJvdHRvbSAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5tYXJpZ29sZEJvcmRlcn0+XG4gICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiAyMCB9LCAoXywgaSkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXtpfSBzdHlsZT17c3R5bGVzLm1hcmlnb2xkRG90fT48L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEluZGlhblRyYWRpdGlvbmFsVGVtcGxhdGU7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJJbmRpYW5UcmFkaXRpb25hbFRlbXBsYXRlIiwiZGF0YSIsImdldEZvbnRGYW1pbHkiLCJmb250VmFsdWUiLCJmYWxsYmFjayIsIkFycmF5IiwiaXNBcnJheSIsImpvaW4iLCJwYXJ0bmVyMV9uYW1lIiwicGFydG5lcjJfbmFtZSIsIndlZGRpbmdfZGF0ZSIsIndlZGRpbmdfdmVudWUiLCJtdWh1cnRhbV90aW1lIiwicmVjZXB0aW9uX2RhdGUiLCJyZWNlcHRpb25fdmVudWUiLCJyZWNlcHRpb25fdGltZSIsImNvdXBsZV9waG90byIsImN1c3RvbV9tZXNzYWdlIiwiY29sb3JzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsImJhY2tncm91bmQiLCJ0ZXh0IiwiYWNjZW50IiwiZm9udHMiLCJoZWFkaW5nIiwiYm9keSIsImRlY29yYXRpdmUiLCJzdHlsZXMiLCJjb250YWluZXIiLCJtYXhXaWR0aCIsIndpZHRoIiwiYm9yZGVyUmFkaXVzIiwib3ZlcmZsb3ciLCJib3hTaGFkb3ciLCJwb3NpdGlvbiIsIm1hcmdpbiIsImZvbnRGYW1pbHkiLCJhc3BlY3RSYXRpbyIsImlubmVyQ29udGFpbmVyIiwicGFkZGluZyIsImhlaWdodCIsIm1hcmlnb2xkQm9yZGVyIiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImJvcmRlclRvcCIsImJvcmRlckJvdHRvbSIsIm1hcmlnb2xkRG90IiwiYmFja2dyb3VuZENvbG9yIiwiZ2FuZXNoU2VjdGlvbiIsInRleHRBbGlnbiIsImdhbmVzaFN5bWJvbCIsImZvbnRTaXplIiwiY29sb3IiLCJnYW5lc2hUZXh0IiwiZm9udFdlaWdodCIsIm1hcmdpbkJvdHRvbSIsImNvdXBsZU5hbWVzIiwibGluZUhlaWdodCIsImFuZFN5bWJvbCIsImludml0YXRpb25UZXh0IiwiZXZlbnREZXRhaWxzIiwiZXZlbnRUaXRsZSIsImV2ZW50SW5mbyIsImV2ZW50RGF0ZSIsImRlY29yYXRpdmVEaXZpZGVyIiwiY291cGxlUGhvdG8iLCJwaG90b1BsYWNlaG9sZGVyIiwiYm9yZGVyIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUG9zaXRpb24iLCJibGVzc2luZ1RleHQiLCJmb250U3R5bGUiLCJkaXYiLCJzdHlsZSIsImZyb20iLCJsZW5ndGgiLCJfIiwiaSIsInNwYW4iLCJiciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});