"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import UserAvatar from "./UserAvatar";
import axios from "../../services/axiosConfig";
import api from '../../services/api';
import { useRouter } from "next/navigation";
import { useLocation } from "../../contexts/LocationContext";
import { ThumbsUp } from "lucide-react";

// Updated interface for flash video items (matches new API response)
interface FlashVideo {
  media_id: string;
  media_url: string;
  thumbnail_url: string | null;
  caption?: string;
  location?: string;
  event_type?: string;
  video_type?: string;
  partner?: string;
  wedding_style?: string;
  budget?: string;
  created_at: string;
  user_id: string;
  user_name: string;
  user_avatar: string | null;
  is_own_content: boolean;
  followers_count: number;
  following_count: number;
  is_following: boolean;
  views_count: number;
  likes_count: number;
  is_liked: boolean;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
  filtered_by_location?: string;
  location_matches_in_page?: number;
}

interface FlashesProps {
  shouldLoad?: boolean;
}

const FlashesSection: React.FC<FlashesProps> = ({ shouldLoad = false }) => {
  const router = useRouter();
  const { selectedLocation } = useLocation();
  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Function to navigate to a user's profile
  const navigateToUserProfile = (userId?: string, username?: string, event?: React.MouseEvent) => {
    // Stop event propagation to prevent triggering parent click events
    if (event) {
      event.stopPropagation();
    }

    // If we have a userId, navigate to that specific user's profile
    if (userId) {
      router.push(`/profile/${userId}`);
    } else if (username) {
      // For now, just use the username as a parameter
      // In a real app, you might want to fetch the user ID first
      router.push(`/profile/${username}`);
    } else {
      console.warn('Cannot navigate to profile: missing both userId and username');
    }
  };

  // User avatar placeholders (using local images instead of external URLs)
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];

  // Fallback data if API fails - using empty array
  const getFallbackFlashes = (): FlashVideo[] => [];

  // Function to fetch flashes from the API
  const fetchFlashes = async (pageNumber: number, isInitialLoad: boolean = false) => {
    // Prevent multiple simultaneous requests
    if ((isInitialLoad && loading) || (!isInitialLoad && loadingMore)) {
      return;
    }

    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // Simple token retrieval
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication required');
        setFlashes([]);
        return;
      }

      // Build API URL with location parameter if selected
      let apiUrl = `/flashes?page=${pageNumber}&limit=10`;
      if (selectedLocation) {
        apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
      }

      // Make API request
      const response = await axios.get<ApiResponse>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // Process the data
      if (response.data && response.data.flashes) {
        const processedFlashes = response.data.flashes.map(flash => {
          if (!flash.thumbnail_url) {
            return flash;
          }
          return flash;
        });

        if (pageNumber === 1) {
          setFlashes(processedFlashes);
        } else {
          setFlashes(prev => [...prev, ...processedFlashes]);
        }

        setHasMore(response.data.next_page);
        setPage(pageNumber); // Update the current page
        setError(null); // Clear any previous errors
      } else {
        setError('Failed to load flashes - unexpected response format');
        setFlashes([]);
      }
    } catch (err) {
      setError('Failed to load flashes');
      setFlashes([]);
    } finally {
      // Always clear loading states
      if (isInitialLoad) {
        setLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // Fetch first page of flashes as soon as the component mounts
  useEffect(() => {
    // Only trigger when shouldLoad changes to true
    if (shouldLoad) {
      // Set a flag to track initial load
      setInitialLoadComplete(true);

      // IMMEDIATE API request with no conditions or delays
      setLoading(true);

      // Skip the fetchFlashes function and make the API call directly here
      const token = localStorage.getItem('token');

      if (token) {
        // Build API URL with location parameter if selected
        let apiUrl = `/flashes?page=1&limit=10`;
        if (selectedLocation) {
          apiUrl += `&location=${encodeURIComponent(selectedLocation)}`;
        }

        // Make direct API request
        axios.get<ApiResponse>(apiUrl, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
          .then(response => {
            if (response.data && response.data.flashes) {
              setFlashes(response.data.flashes);
              setHasMore(response.data.next_page);
              setPage(1);
              setError(null);
            } else {
              setFlashes([]);
              setError('No flashes found');
            }
          })
          .catch(err => {
            setError('Failed to load flashes');
            setFlashes([]);
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(false);
        setError('Authentication required');
      }
    }
  }, [shouldLoad, selectedLocation]); // Only depend on shouldLoad and selectedLocation

  // Failsafe to ensure content is loaded - only show error after timeout
  useEffect(() => {
    // If we're stuck in loading state for more than 10 seconds, show error
    let timeoutId: NodeJS.Timeout | null = null;

    if (loading && initialLoadComplete) {
      timeoutId = setTimeout(() => {
        if (flashes.length === 0) {
          setError('Unable to load flashes. Please check your network connection.');
        }
      }, 10000); // 10 second timeout for slow networks
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [loading, initialLoadComplete, flashes.length]);

  // Setup Intersection Observer for horizontal lazy loading
  useEffect(() => {
    // Only set up observer after initial load and if there's more content to fetch
    if (!initialLoadComplete || !hasMore) return;

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        // If the trigger element is visible and we're not already loading
        if (entries[0]?.isIntersecting && !loading && !loadingMore && hasMore) {
          const nextPage = page + 1;
          fetchFlashes(nextPage, false);
        }
      },
      // Use the scroll container as the root for the intersection observer
      {
        root: scrollContainerRef.current,
        threshold: 0.1, // Trigger when 10% of the element is visible
        rootMargin: '0px 150px 0px 0px' // Add margin to the right to load earlier
      }
    );

    // Start observing the trigger element
    if (loadMoreTriggerRef.current) {
      observerRef.current.observe(loadMoreTriggerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, loadingMore, page, initialLoadComplete]);

  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    if (flash.thumbnail_url) {
      return flash.thumbnail_url;
    }
    return '/pics/placeholder.svg';
  };

  return (
    <section className="w-full mb-8 relative">
      <div className="flex items-center justify-between mb-4 px-2">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Flashes
        </h2>
        <a
          href="/home/<USER>"
          className="text-red-500 text-sm font-medium hover:underline z-10 relative ml-auto mr-1"
        >
          See all
        </a>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-10 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading flashes...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-10 text-center">
          <div className="text-red-500 mb-4">{error}</div>
          <button
            onClick={() => {
              setError(null);
              fetchFlashes(1, true);
            }}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            Retry
          </button>
        </div>
      )}

      {/* Debug info in development only */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 right-0 bg-black/50 text-white text-xs p-2 rounded-bl-md z-50 max-w-[200px]">
          <div>State: {loading ? 'Loading' : error ? 'Error' : flashes.length === 0 ? 'Empty' : 'Loaded'}</div>
          <div>Count: {flashes.length}</div>
          <div>Page: {page}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
        </div>
      )} */}

      {/* Horizontal scroll container */}
      {!loading && (
        <div
          className="overflow-x-auto scrollbar-hide relative w-full"
          ref={scrollContainerRef}
          style={{
            scrollBehavior: 'smooth',
            WebkitOverflowScrolling: 'touch',
            minHeight: flashes.length === 0 && !error ? '220px' : 'auto'
          }}
        >
          {/* Empty state message when no flashes */}
          {flashes.length === 0 && !error && !loading && (
            <div className="flex items-center justify-center h-[220px] w-full">
              <div className="text-gray-400">No flashes available</div>
            </div>
          )}

          <div className="flex gap-3 pb-4 flex-nowrap">
            {flashes.map((flash, index) => (
              <div
                key={`${flash.media_id}-${index}`}
                onClick={() => {
                  window.location.href = `/home/<USER>/shorts?index=${index}`;
                }}
                className="min-w-[40vw] w-[40vw] sm:min-w-[30vw] sm:w-[30vw] md:min-w-[22vw] md:w-[22vw] lg:min-w-[18vw] lg:w-[18vw] h-[220px] sm:h-[280px] md:h-[308px] max-w-[200px] flex-shrink-0 inline-block relative rounded-[10px] border border-[#B31B1E] p-[6px] sm:p-[10px] cursor-pointer overflow-hidden transition-transform duration-200 hover:scale-105"
                style={{
                  background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    top: "10px",
                    left: "10px",
                    zIndex: 2,
                  }}
                  onClick={(e: React.MouseEvent) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                >
                  <UserAvatar
                    username={flash.user_name || "user"}
                    size="sm"
                    isGradientBorder={true}
                    imageUrl={flash.user_avatar || undefined}
                    onClick={(e) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                  />
                </div>

                {/* Add username text that's also clickable */}
                <div
                  style={{
                    position: "absolute",
                    top: "10px",
                    left: "50px",
                    zIndex: 2,
                    color: "white",
                  }}
                  onClick={(e: React.MouseEvent) => navigateToUserProfile(flash.user_id, flash.user_name, e)}
                  className="cursor-pointer hover:underline text-sm font-medium"
                >
                  {flash.user_name}
                </div>

                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 1,
                  }}
                >
                  {/* Show video preview for non-YouTube videos, image for others */}
                  {flash.media_url && (flash.media_url.includes(".mp4") || flash.media_url.includes("cloudfront") || flash.media_url.includes(".webm")) ? (
                    <video
                      src={flash.media_url}
                      className="object-cover w-full h-full rounded-[10px]"
                      style={{ width: '100%', height: '100%' }}
                      playsInline
                      muted
                      loop
                      preload="metadata"
                      poster={getImageSource(flash)}
                      onLoadedMetadata={e => {
                        // Optionally, you can auto-play preview if desired
                        // e.currentTarget.play();
                      }}
                    />
                  ) : (
                    <img
                      src={getImageSource(flash)}
                      alt={flash.caption || flash.media_id}
                      className="object-cover w-full h-full rounded-[10px]"
                      style={{ width: '100%', height: '100%' }}
                    />
                  )}
                </div>

                <div
                  style={{
                    position: "absolute",
                    bottom: "10px",
                    left: "10px",
                    right: "10px",
                    zIndex: 2,
                    color: "white",
                  }}
                >
                  <div style={{ fontSize: "14px", fontWeight: 500 }}>
                    {flash.caption}
                  </div>
                  <div style={{ fontSize: "12px" }}>
                    {flash.location ? `${flash.location}` : ''}
                    {flash.views_count ? ` • ${flash.views_count} views` : ''}
                    {typeof flash.likes_count === 'number' ? ` • ${flash.likes_count} likes` : ''}
                  </div>
                </div>
              </div>
            ))}

            {/* Load more trigger element - this is what IntersectionObserver watches */}
            {hasMore && (
              <div
                ref={loadMoreTriggerRef}
                className="flex-shrink-0 w-10 h-full opacity-0"
                style={{
                  position: 'relative',
                  // Add debug outline in development
                  outline: process.env.NODE_ENV === 'development' ? '1px dashed rgba(255, 0, 0, 0.3)' : 'none'
                }}
                aria-hidden="true"
                data-testid="flashes-load-more-trigger"
              />
            )}

            {/* Loading indicator - only show when loading more */}
            {loadingMore && (
              <div className="flex-shrink-0 flex items-center justify-center min-w-[100px] h-[220px]">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
};

export default FlashesSection;