"use client";
import React, { useState, useEffect } from "react";
import {
  TopNavigation,
  SideNavigation,
} from "../../../../components/HomeDashboard/Navigation";
import axios from "../../../../services/axiosConfig";
import Image from "next/image";
import { useParams } from "next/navigation";
import VideoInteractionBar from "../../../../components/VideoInteractionBar";
import VideoVendorDetails from "../../../../components/VideoVendorDetails";
import Glimpses from "../../../../components/HomeDashboard/Glimpses";
import Flashes from "../../../../components/HomeDashboard/Flashes";
import WeddingVideosSection from "../../../../components/HomeDashboard/WeddingVideos";
import Photos from "../../../../components/HomeDashboard/Photos";
import DetailPageLazyLoad from "../../../../components/DetailPageLazyLoad";
import useIsMobile from "../../../../hooks/useIsMobile";

// Updated interface for glimpse items from new API
interface Glimpse {
  media_id: string;
  media_url: string;
  thumbnail_url?: string;
  caption?: string;
  location?: string;
  event_type?: string;
  video_type?: string;
  partner?: string;
  wedding_style?: string;
  budget?: string;
  created_at: string;
  user_id: string;
  user_name?: string;
  user_avatar?: string | null;
  is_own_content?: boolean;
  followers_count?: number;
  following_count?: number;
  is_following?: boolean;
  views_count?: number;
  likes_count?: number;
  is_liked?: boolean;
}

interface RecommendedVideo {
  media_id: string;
  caption?: string;
  thumbnail_url?: string;
  user_name?: string;
  views_count?: number;
  created_at: string;
}

export default function GlimpseDetailPage() {
  const params = useParams();
  const glimpseId = params?.id as string;
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [glimpse, setGlimpse] = useState<Glimpse | null>(null);
  const [recommendedVideos, setRecommendedVideos] = useState<RecommendedVideo[]>(
    []
  );
  const [filteredVideos, setFilteredVideos] = useState<RecommendedVideo[]>([]);
  const [activeFilter, setActiveFilter] = useState<"all" | "creator">("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const fetchGlimpseDetails = async () => {
      try {
        setLoading(true);
        const tokenKey = localStorage.getItem("token")
          ? "token"
          : localStorage.getItem("jwt_token")
          ? "jwt_token"
          : localStorage.getItem("auth_token")
          ? "auth_token"
          : null;
        const token = tokenKey ? localStorage.getItem(tokenKey) : null;
        if (!token) {
          setError("Authentication required");
          return;
        }
        const response = await axios.get(`/glimpses?page=1&limit=10`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (response.data && response.data.glimpses) {
          const foundGlimpse = response.data.glimpses.find(
            (g: Glimpse) => g.media_id === glimpseId
          );
          if (foundGlimpse) {
            setGlimpse(foundGlimpse);
            const recommendations = response.data.glimpses
              .filter((g: Glimpse) => g.media_id !== glimpseId)
              .slice(0, 7)
              .map((g: Glimpse) => ({
                media_id: g.media_id,
                caption: g.caption,
                thumbnail_url: g.thumbnail_url,
                user_name: g.user_name,
                views_count: g.views_count,
                created_at: g.created_at,
              }));
            setRecommendedVideos(recommendations);
          } else {
            setError("Glimpse not found");
          }
        } else {
          setError("Failed to load glimpse details");
        }
      } catch (err) {
        setError("Failed to load glimpse details");
      } finally {
        setLoading(false);
      }
    };
    if (glimpseId) fetchGlimpseDetails();
  }, [glimpseId]);

  const formatViewCount = (count: number): string => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };
  const getYoutubeEmbedUrl = (url: string): string => {
    if (url.includes("youtu.be/")) {
      const videoId = url.split("youtu.be/")[1].split("?")[0];
      return `https://www.youtube.com/embed/${videoId}`;
    }
    if (url.includes("youtube.com/watch?v=")) {
      const videoId = new URLSearchParams(url.split("?")[1]).get("v");
      return `https://www.youtube.com/embed/${videoId}`;
    }
    return url;
  };
  useEffect(() => {
    if (recommendedVideos.length > 0 && glimpse) {
      if (activeFilter === "all") setFilteredVideos(recommendedVideos);
      else if (activeFilter === "creator")
        setFilteredVideos(
          recommendedVideos.filter(
            (video) => video.user_name === glimpse.user_name
          )
        );
    } else {
      setFilteredVideos(recommendedVideos);
    }
  }, [recommendedVideos, activeFilter, glimpse]);

  return (
    <div
      className={
        isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"
      }
    >
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <div className="hidden md:block fixed left-0 top-[80px] h-[calc(100vh-80px)] z-30">
          <SideNavigation
            expanded={sidebarExpanded}
            onExpand={() => setSidebarExpanded(true)}
            onCollapse={() => setSidebarExpanded(false)}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col lg:flex-row flex-1 mt-[80px] w-full">
          {/* Video and Details Section */}
          <main
            className={`flex-1 bg-white flex flex-col w-full transition-all duration-300 ease-in-out ${
              isMobile 
                ? "p-0" 
                : `p-4 md:ml-[70px] ${sidebarExpanded ? "md:ml-60" : "md:ml-20"}`
            }`}
            style={{
              minHeight: "calc(100vh - 80px)",
              paddingBottom: isMobile ? "20px" : "40px",
              overflowY: "auto",
              overflowX: "hidden",
              position: "relative",
            }}
          >
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-600">{error}</div>
              </div>
            ) : glimpse ? (
              <div
                className={`flex flex-col gap-2 w-full mx-auto ${
                  isMobile ? "px-0" : "sm:gap-4 md:gap-8 px-1 sm:px-2 md:px-4"
                }`}
                style={{ maxWidth: "100%" }}
              >
                {/* Video Player and Info Section */}
                <div
                  className={`flex flex-col w-full items-center justify-center ${
                    isMobile ? "py-0" : "py-2 sm:py-3"
                  }`}
                  style={{ maxWidth: "100%", margin: "0 auto" }}
                >
                  {/* Video Player */}
                  <div
                    className={`w-full bg-black overflow-hidden relative ${
                      isMobile 
                        ? "rounded-none shadow-none border-none" 
                        : "rounded-lg sm:rounded-xl shadow-xl border border-gray-200"
                    }`}
                    style={{
                      maxWidth: "100%",
                      width: "100%",
                      height: "auto",
                    }}
                  >
                    {/* Video Title Overlay */}
                    <div className={`absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent z-10 ${
                      isMobile ? "p-3" : "p-2 sm:p-4"
                    }`}>
                      <h1 className={`text-white font-medium line-clamp-2 ${
                        isMobile ? "text-base" : "text-sm sm:text-lg md:text-xl"
                      }`}>
                        {glimpse.caption}
                      </h1>
                    </div>

                    {glimpse.media_url && (glimpse.media_url.includes("youtube.com") ||
                    glimpse.media_url.includes("youtu.be")) ? (
                      // YouTube embed with improved error handling
                      <div className="w-full aspect-video relative">
                        <iframe
                          src={getYoutubeEmbedUrl(glimpse.media_url)}
                          className="w-full h-full absolute top-0 left-0"
                          title={glimpse.caption}
                          style={{ border: "none" }}
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        ></iframe>
                      </div>
                    ) : (
                      // Native video player with improved error handling
                      <div className="w-full aspect-video relative">
                        <video
                          className="w-full h-full object-contain absolute top-0 left-0"
                          controls
                          poster={glimpse.thumbnail_url || '/pics/placeholder.svg'}
                          preload="metadata"
                          autoPlay
                          controlsList="nodownload"
                          playsInline
                          webkit-playsinline="true"
                          x5-playsinline="true"
                          x5-video-player-type="h5"
                        >
                          <source src={glimpse.media_url} type="video/mp4" />
                          <source src={glimpse.media_url} type="video/webm" />
                          <source src={glimpse.media_url} type="video/quicktime" />
                          <source src={glimpse.media_url} type="video/x-matroska" />
                          Your browser does not support the video tag.
                        </video>
                        <div id="video-error-fallback" className="hidden absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white p-4 text-center">
                          <div>
                            <p className="text-sm sm:text-lg font-semibold mb-2">Video playback error</p>
                            <p className="text-xs sm:text-base">The video could not be played. Please try again later or view another glimpse.</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Video container with interaction bar - directly connected to video */}
                  <div className={`w-full mx-auto overflow-hidden ${
                    isMobile ? "mb-2 -mt-0" : "mb-2 sm:mb-4 -mt-1"
                  }`}>
                    {/* Video Interaction Bar */}
                    <VideoInteractionBar
                      username={glimpse.user_name || "Anonymous"}
                      uploadDate={glimpse.created_at || "recently"}
                      viewCount={glimpse.views_count || 0}
                      description={glimpse.caption}
                      userId={glimpse.user_id || "default"}
                      contentId={glimpse.media_id}
                      contentType="glimpses"
                      isOwnContent={glimpse.is_own_content}
                      isFollowing={glimpse.is_following}
                    />

                    {/* Vendor Details Section */}
                    <div className={`bg-white ${
                      isMobile ? "px-3 pb-3" : "px-2 sm:px-4 pb-2 sm:pb-4"
                    }`}>
                      <VideoVendorDetails
                        videoId={glimpse.media_id}
                        isVerified={false}
                      />
                    </div>
                  </div>
                </div>

                {/* Mobile Recommended Videos Section */}
                {isMobile && (
                  <div className="px-3 mt-6">
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="font-semibold text-lg text-black">
                          Recommended Videos
                        </h2>
                        <button className="bg-white border border-gray-200 rounded-full p-1 hover:bg-gray-50">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <rect
                              width="18"
                              height="18"
                              x="3"
                              y="3"
                              rx="2"
                              ry="2"
                            ></rect>
                            <line x1="3" x2="21" y1="9" y2="9"></line>
                            <line x1="9" x2="9" y1="21" y2="9"></line>
                          </svg>
                        </button>
                      </div>

                      <div className="flex mb-4 gap-2 overflow-x-auto pb-1">
                        <button
                          onClick={() => setActiveFilter("all")}
                          className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors ${
                            activeFilter === "all"
                              ? "bg-red-600 text-white"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          All
                        </button>
                        <button
                          onClick={() => setActiveFilter("creator")}
                          className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors ${
                            activeFilter === "creator"
                              ? "bg-red-600 text-white"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          From {glimpse?.user_name || "Creator"}
                        </button>
                      </div>

                      {/* Mobile List View */}
                      <div className="space-y-4">
                        {filteredVideos.map((video) => (
                          <div
                            key={video.media_id}
                            className="cursor-pointer hover:bg-gray-50 p-3 rounded-lg transition-all duration-200 border border-gray-100"
                            onClick={() => {
                              window.location.href = `/home/<USER>/${video.media_id}`;
                            }}
                          >
                            <div className="relative w-full h-48 rounded-lg overflow-hidden mb-3">
                              <Image
                                src={video.thumbnail_url || "/pics/placeholder.svg"}
                                alt={video.caption || "Glimpse"}
                                fill
                                className="object-cover"
                                sizes="100vw"
                              />
                              <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                23:45
                              </div>
                            </div>
                            <div>
                              <h3 className="text-base font-medium line-clamp-2 text-black mb-2">
                                {video.caption}
                              </h3>
                              <p className="text-sm text-gray-600 mb-1">
                                {formatViewCount(video.views_count || 0)} views • {video.created_at || "3 years ago"}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Additional Content Sections - Lazy Loaded */}
                <div className={isMobile ? "px-3" : ""}>
                  <DetailPageLazyLoad
                    id="glimpses-section"
                    index={0}
                    rootMargin="0px 0px 500px 0px"
                  >
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Glimpses shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad
                    id="flashes-section"
                    index={1}
                    rootMargin="0px 0px 400px 0px"
                  >
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Flashes shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad
                    id="wedding-videos-section"
                    index={2}
                    rootMargin="0px 0px 300px 0px"
                  >
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <WeddingVideosSection shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad
                    id="photos-section"
                    index={3}
                    rootMargin="0px 0px 200px 0px"
                  >
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Photos shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>
                </div>
              </div>
            ) : null}
          </main>

          {/* Right Sidebar - Recommended Videos (Desktop Only) */}
          <aside
            className="hidden md:block lg:block md:relative md:bg-transparent md:w-[350px] lg:w-[400px] md:sticky md:top-20 bg-white border-l border-gray-100 overflow-y-auto md:h-[calc(100vh-80px)] shadow-md"
          >
            <div className="md:w-full md:static md:shadow-none">
              <div className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-semibold text-lg text-black">
                    Recommended Glimpses
                  </h2>
                  <button className="bg-white border border-gray-200 rounded-full p-1 hover:bg-gray-50">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect
                        width="18"
                        height="18"
                        x="3"
                        y="3"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="3" x2="21" y1="9" y2="9"></line>
                      <line x1="9" x2="9" y1="21" y2="9"></line>
                    </svg>
                  </button>
                </div>

                <div className="flex mb-3 gap-2 overflow-x-auto pb-1">
                  <button
                    onClick={() => setActiveFilter("all")}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                      activeFilter === "all"
                        ? "bg-red-600 text-white"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    All
                  </button>
                  <button
                    onClick={() => setActiveFilter("creator")}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                      activeFilter === "creator"
                        ? "bg-red-600 text-white"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    From {glimpse?.user_name || "Creator"}
                  </button>
                </div>

                <div className="space-y-3">
                  {filteredVideos.map((video) => (
                    <div
                      key={video.media_id}
                      className="flex space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-all duration-200"
                      onClick={() => {
                        window.location.href = `/home/<USER>/${video.media_id}`;
                      }}
                    >
                      <div className={`relative flex-shrink-0 rounded-lg overflow-hidden ${
                        isMobile ? "w-24 h-14" : "w-28 sm:w-32 md:w-36 h-16 sm:h-18 md:h-20"
                      }`}>
                        <Image
                          src={video.thumbnail_url || "/pics/placeholder.svg"}
                          alt={video.caption || "Glimpse"}
                          fill
                          className="object-cover"
                          sizes={isMobile ? "96px" : "128px"}
                        />
                        <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                          0:00
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className={`font-medium line-clamp-2 text-black ${
                          isMobile ? "text-sm" : "text-sm"
                        }`}>
                          {video.caption}
                        </h3>
                        <p className={`text-black mt-1 ${
                          isMobile ? "text-xs" : "text-xs"
                        }`}>
                          {video.user_name}
                        </p>
                        <p className={`text-gray-500 ${
                          isMobile ? "text-xs" : "text-xs"
                        }`}>
                          {formatViewCount(video.views_count || 0)} views {" "}
                          {video.created_at || "recently"}
                        </p>
                      </div>
                      <button className="text-gray-500 self-start mt-1 p-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="12" cy="12" r="1"></circle>
                          <circle cx="12" cy="5" r="1"></circle>
                          <circle cx="12" cy="19" r="1"></circle>
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}
