"use client";
import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import axios from "axios";
import { Loader2, Heart } from "lucide-react";

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  design_settings?: {
    colors?: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts?: {
      heading: string;
      body: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
    };
    customImage?: string;
    couple_names?: string;
    type?: string;
  };
  template_name: string;
  template_thumbnail: string;
}

export default function EInvitePage() {
  const params = useParams();
  const router = useRouter();
  const [einvite, setEInvite] = useState<EInvite | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    checkAuthAndFetchEInvite();
  }, [params.id]);

  const getAuthToken = () => {
    return localStorage.getItem('authToken');
  };

  const checkAuthAndFetchEInvite = async () => {
    const token = getAuthToken();

    if (token) {
      setIsAuthenticated(true);
      await fetchEInvite(token);
    } else {
      // For e-invites, we can still show the content but encourage login
      setIsAuthenticated(false);
      await fetchEInvitePublic();
    }
  };

  const fetchEInvite = async (token: string) => {
    try {
      const response = await axios.get(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/public-website?website_id=${params.id}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.website) {
        const website = response.data.website;

        // Check if this is actually an e-invite
        if (website.design_settings?.type === 'einvite') {
          setEInvite(website);
        } else {
          setError("This is not a valid e-invite.");
        }
      } else {
        setError("E-Invite not found.");
      }
    } catch (error) {
      console.error('Error fetching e-invite:', error);
      setError("Failed to load e-invite. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const fetchEInvitePublic = async () => {
    try {
      // Try to fetch without authentication first
      const response = await axios.get(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/public-website?website_id=${params.id}`
      );

      if (response.data && response.data.website) {
        const website = response.data.website;

        // Check if this is actually an e-invite
        if (website.design_settings?.type === 'einvite') {
          setEInvite(website);
        } else {
          setError("This is not a valid e-invite.");
        }
      } else {
        setError("E-Invite not found.");
      }
    } catch (error) {
      console.error('Error fetching public e-invite:', error);
      setError("Failed to load e-invite. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 size={48} className="animate-spin text-[#B31B1E] mx-auto mb-4" />
          <p className="text-gray-600">Loading e-invite...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-white rounded-lg shadow-md p-8 max-w-md">
            <div className="text-red-500 mb-4">
              <Heart size={48} className="mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Oops!</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push('/home')}
              className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!einvite) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">E-Invite not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: einvite.design_settings?.customImage 
            ? `url(${einvite.design_settings.customImage})` 
            : 'none',
          backgroundColor: einvite.design_settings?.colors?.background || '#FFFFFF',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
      
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black bg-opacity-20" />
      
      {/* Content */}
      <div className="relative z-10 min-h-screen flex flex-col justify-center items-center text-center p-8">
        {/* Wedzat Logo */}
        <div className="absolute top-8 left-8">
          <div className="flex items-center gap-2">
            <Heart size={24} className="text-[#B31B1E] fill-current" />
            <span className="text-xl font-bold text-[#B31B1E]">Wedzat</span>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-2xl mx-auto bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-8 shadow-lg">
          {/* Couple Names */}
          <h1
            className="text-5xl md:text-6xl font-bold mb-6"
            style={{
              color: einvite.design_settings?.colors?.primary || '#B31B1E',
              fontFamily: einvite.design_settings?.fonts?.coupleNames || 'Dancing Script'
            }}
          >
            {einvite.couple_names || einvite.design_settings?.couple_names || 'Jane & John'}
          </h1>
          
          {/* Title */}
          <h2
            className="text-2xl md:text-3xl mb-8"
            style={{
              color: einvite.design_settings?.colors?.text || '#000000',
              fontFamily: einvite.design_settings?.fonts?.heading || 'Playfair Display'
            }}
          >
            {einvite.title}
          </h2>
          
          {/* Wedding Date */}
          {einvite.wedding_date && (
            <div className="mb-6">
              <p
                className="text-xl md:text-2xl font-semibold"
                style={{
                  color: einvite.design_settings?.colors?.secondary || '#333333',
                  fontFamily: einvite.design_settings?.fonts?.date || 'Open Sans'
                }}
              >
                {formatDate(einvite.wedding_date)}
              </p>
            </div>
          )}
          
          {/* Wedding Location */}
          {einvite.wedding_location && (
            <div className="mb-8">
              <p
                className="text-lg md:text-xl"
                style={{
                  color: einvite.design_settings?.colors?.secondary || '#333333',
                  fontFamily: einvite.design_settings?.fonts?.location || 'Open Sans'
                }}
              >
                📍 {einvite.wedding_location}
              </p>
            </div>
          )}
          
          {/* About Couple */}
          {einvite.about_couple && (
            <div className="mb-8">
              <p
                className="text-base md:text-lg leading-relaxed"
                style={{
                  color: einvite.design_settings?.colors?.text || '#000000',
                  fontFamily: einvite.design_settings?.fonts?.aboutCouple || 'Open Sans'
                }}
              >
                {einvite.about_couple}
              </p>
            </div>
          )}
          
          {/* Call to Action */}
          <div className="mt-8">
            <p
              className="text-lg mb-4"
              style={{
                color: einvite.design_settings?.colors?.text || '#000000',
                fontFamily: einvite.design_settings?.fonts?.body || 'Open Sans'
              }}
            >
              We can't wait to celebrate with you! 💕
            </p>

            {!isAuthenticated ? (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Join Wedzat to RSVP and see more wedding content
                </p>
                <div className="flex gap-3 justify-center">
                  <button
                    onClick={() => {
                      const returnUrl = encodeURIComponent(window.location.pathname);
                      router.push(`/login?returnUrl=${returnUrl}`);
                    }}
                    className="px-6 py-3 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors font-semibold"
                  >
                    Login to RSVP
                  </button>
                  <button
                    onClick={() => router.push('/signup')}
                    className="px-6 py-3 border border-[#B31B1E] text-[#B31B1E] rounded-md hover:bg-red-50 transition-colors font-semibold"
                  >
                    Sign Up
                  </button>
                </div>
              </div>
            ) : (
              <button
                onClick={() => router.push('/home')}
                className="px-6 py-3 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors font-semibold"
              >
                Visit Wedzat
              </button>
            )}
          </div>
        </div>
        
        {/* Footer */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <p className="text-sm text-gray-600">
            Created with ❤️ on <span className="font-semibold text-[#B31B1E]">Wedzat</span>
          </p>
        </div>
      </div>
    </div>
  );
}
