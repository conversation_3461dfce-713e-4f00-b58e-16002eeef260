"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '400px',\n            width: '100%',\n            background: colors.primary || '#B31B1E',\n            borderRadius: '0px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif'),\n            aspectRatio: '3/4'\n        },\n        innerContainer: {\n            background: colors.background || '#FFFFFF',\n            margin: '20px',\n            borderRadius: '10px',\n            padding: '20px',\n            height: 'calc(100% - 40px)',\n            position: 'relative'\n        },\n        marigoldBorder: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '10px 0',\n            borderTop: \"3px solid \".concat(colors.secondary || '#FFD700'),\n            borderBottom: \"3px solid \".concat(colors.secondary || '#FFD700')\n        },\n        marigoldDot: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: colors.secondary || '#FFD700'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '20px 0',\n            background: 'transparent'\n        },\n        ganeshSymbol: {\n            width: '60px',\n            height: '60px',\n            backgroundColor: '#8A2BE2',\n            borderRadius: '8px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            margin: '0 auto 15px',\n            fontSize: '32px',\n            color: 'white'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '18px',\n            color: colors.primary || '#B31B1E',\n            fontWeight: '700',\n            marginBottom: '30px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '36px',\n            fontWeight: '700',\n            color: colors.primary || '#B31B1E',\n            textAlign: 'center',\n            margin: '20px 0',\n            lineHeight: '1.2'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '28px',\n            color: '#333',\n            margin: '0 10px',\n            fontWeight: '400'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 0',\n            fontSize: '14px',\n            color: '#333',\n            lineHeight: '1.5',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        eventDetails: {\n            background: colors.primary || '#B31B1E',\n            color: 'white',\n            padding: '25px 20px',\n            textAlign: 'center',\n            margin: '20px 0',\n            borderRadius: '8px'\n        },\n        eventTitle: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '20px',\n            fontWeight: '700',\n            marginBottom: '10px',\n            color: colors.secondary || '#FFD700'\n        },\n        eventInfo: {\n            margin: '8px 0',\n            fontSize: '14px',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        eventDate: {\n            fontSize: '16px',\n            fontWeight: '500',\n            margin: '15px 0',\n            color: colors.secondary || '#FFD700',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        decorativeDivider: {\n            width: '60px',\n            height: '2px',\n            background: colors.secondary || '#FFD700',\n            margin: '15px auto',\n            borderRadius: '1px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder,\n                children: Array.from({\n                    length: 20\n                }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.marigoldDot\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.innerContainer,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.ganeshSymbol,\n                                children: \"\\uD83D\\uDD49️\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.ganeshText,\n                                children: \"|| Shree Ganesh Namah ||\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.coupleNames,\n                        children: [\n                            partner1_name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: styles.andSymbol,\n                                children: \"&\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 26\n                            }, undefined),\n                            partner2_name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.invitationText,\n                        children: [\n                            \"We Invite You to Share in our Joy And Request\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 56\n                            }, undefined),\n                            \"Your Presence At the Reception\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDetails,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventTitle,\n                                children: \"✦ Muhurtam ✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: [\n                                    \"Time \",\n                                    muhurtam_time\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventDate,\n                                children: [\n                                    \"On \",\n                                    wedding_date\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: wedding_venue\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.decorativeDivider\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventTitle,\n                                children: \"✦ Reception ✦\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: [\n                                    \"Time \",\n                                    reception_time\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventDate,\n                                children: [\n                                    \"On \",\n                                    reception_date\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: styles.eventInfo,\n                                children: reception_venue\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    couple_photo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.couplePhoto,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: styles.photoPlaceholder,\n                            children: !couple_photo && \"Couple Photo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.blessingText,\n                        children: [\n                            '\"',\n                            custom_message,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder,\n                children: Array.from({\n                    length: 20\n                }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.marigoldDot\n                    }, i, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});