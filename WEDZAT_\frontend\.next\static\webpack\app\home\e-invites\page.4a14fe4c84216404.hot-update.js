"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx":
/*!*********************************************************!*\
  !*** ./app/home/<USER>/components/EInviteEditor.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _templates_IndianTraditionalTemplate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templates/IndianTraditionalTemplate */ \"(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst EInviteEditor = (param)=>{\n    let { templates, einvite, onSave, onCancel, loading } = param;\n    var _einvite_design_settings, _designSettings_colors, _designSettings_colors1, _designSettings_fonts, _designSettings_colors2, _designSettings_fonts1, _coupleNames_split_, _designSettings_colors3, _designSettings_fonts2, _designSettings_colors4, _designSettings_fonts3, _coupleNames_split_1, _designSettings_colors5, _designSettings_fonts4, _designSettings_colors6, _designSettings_fonts5, _designSettings_colors7, _designSettings_fonts6, _designSettings_colors8, _designSettings_fonts7;\n    _s();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.title) || \"Our Wedding E-Invite\");\n    const [weddingDate, setWeddingDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_date) || \"\");\n    const [weddingLocation, setWeddingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_location) || \"\");\n    const [aboutCouple, setAboutCouple] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.about_couple) || \"\");\n    const [coupleNames, setCoupleNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.couple_names) || (einvite === null || einvite === void 0 ? void 0 : (_einvite_design_settings = einvite.design_settings) === null || _einvite_design_settings === void 0 ? void 0 : _einvite_design_settings.couple_names) || \"\");\n    const [designSettings, setDesignSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.design_settings) || {\n        colors: {\n            primary: \"#B31B1E\",\n            secondary: \"#333333\",\n            background: \"#FFFFFF\",\n            text: \"#000000\"\n        },\n        fonts: {\n            heading: \"Playfair Display\",\n            body: \"Open Sans\",\n            coupleNames: \"Dancing Script\",\n            date: \"Open Sans\",\n            location: \"Open Sans\",\n            aboutCouple: \"Open Sans\"\n        },\n        customImage: \"\",\n        type: \"einvite\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('content');\n    const [uploadingImage, setUploadingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchingImages, setSearchingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fontOptions = [\n        \"Arial\",\n        \"Helvetica\",\n        \"Times New Roman\",\n        \"Georgia\",\n        \"Verdana\",\n        \"Playfair Display\",\n        \"Open Sans\",\n        \"Lato\",\n        \"Roboto\",\n        \"Dancing Script\",\n        \"Great Vibes\",\n        \"Pacifico\",\n        \"Lobster\",\n        \"Montserrat\",\n        \"Poppins\"\n    ];\n    const handleColorChange = (colorType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                colors: {\n                    ...prev.colors,\n                    [colorType]: value\n                }\n            }));\n    };\n    const handleFontChange = (fontType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                fonts: {\n                    ...prev.fonts,\n                    [fontType]: value\n                }\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        setUploadingImage(true);\n        try {\n            const formData1 = new FormData();\n            formData1.append('image', file);\n            const response = await fetch('/api/upload-image', {\n                method: 'POST',\n                body: formData1\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setDesignSettings((prev)=>({\n                        ...prev,\n                        customImage: data.imageUrl\n                    }));\n            } else {\n                console.error('Failed to upload image');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n        } finally{\n            setUploadingImage(false);\n        }\n    };\n    const searchImages = async ()=>{\n        if (!searchQuery.trim()) return;\n        setSearchingImages(true);\n        try {\n            const response = await fetch(\"/api/search-images?q=\".concat(encodeURIComponent(searchQuery)));\n            if (response.ok) {\n                const data = await response.json();\n                setSearchResults(data.images || []);\n            }\n        } catch (error) {\n            console.error('Error searching images:', error);\n        } finally{\n            setSearchingImages(false);\n        }\n    };\n    const selectSearchImage = (imageUrl)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                customImage: imageUrl\n            }));\n        setSearchResults([]);\n        setSearchQuery(\"\");\n    };\n    // Function to render the appropriate template\n    const renderTemplate = ()=>{\n        var _designSettings_couple_names, _formData_couple_names, _designSettings_couple_names1, _formData_couple_names1, _selectedTemplate, _selectedTemplate1, _designSettings_colors, _designSettings_colors1, _designSettings_fonts, _designSettings_colors2, _designSettings_fonts1, _designSettings_colors3, _designSettings_fonts2, _designSettings_colors4, _designSettings_fonts3, _designSettings_colors5, _designSettings_fonts4;\n        const templateData = {\n            partner1_name: ((_designSettings_couple_names = designSettings.couple_names) === null || _designSettings_couple_names === void 0 ? void 0 : _designSettings_couple_names.split(' & ')[0]) || ((_formData_couple_names = formData.couple_names) === null || _formData_couple_names === void 0 ? void 0 : _formData_couple_names.split(' & ')[0]) || 'Partner 1',\n            partner2_name: ((_designSettings_couple_names1 = designSettings.couple_names) === null || _designSettings_couple_names1 === void 0 ? void 0 : _designSettings_couple_names1.split(' & ')[1]) || ((_formData_couple_names1 = formData.couple_names) === null || _formData_couple_names1 === void 0 ? void 0 : _formData_couple_names1.split(' & ')[1]) || 'Partner 2',\n            wedding_date: formData.wedding_date || 'Wedding Date',\n            wedding_venue: formData.wedding_location || 'Wedding Venue',\n            muhurtam_time: '7:00 PM',\n            reception_date: formData.wedding_date || 'Reception Date',\n            reception_venue: formData.wedding_location || 'Reception Venue',\n            reception_time: '10:00 AM to 11:00 AM',\n            couple_photo: designSettings.customImage || '',\n            custom_message: formData.about_couple || 'May your love story be as magical and charming as in fairy tales!',\n            colors: designSettings.colors,\n            fonts: designSettings.fonts\n        };\n        // Check if this is the Indian Traditional template\n        if (((_selectedTemplate = selectedTemplate) === null || _selectedTemplate === void 0 ? void 0 : _selectedTemplate.template_id) === 'indian-traditional-001' || ((_selectedTemplate1 = selectedTemplate) === null || _selectedTemplate1 === void 0 ? void 0 : _selectedTemplate1.name) === 'Indian Traditional') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_templates_IndianTraditionalTemplate__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: templateData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 194,\n                columnNumber: 14\n            }, undefined);\n        }\n        // Default template rendering for other templates\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n            style: {\n                backgroundImage: designSettings.customImage ? \"url(\".concat(designSettings.customImage, \")\") : 'none',\n                backgroundColor: ((_designSettings_colors = designSettings.colors) === null || _designSettings_colors === void 0 ? void 0 : _designSettings_colors.background) || '#FFFFFF',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 p-6 flex flex-col justify-center items-center text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors1 = designSettings.colors) === null || _designSettings_colors1 === void 0 ? void 0 : _designSettings_colors1.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts = designSettings.fonts) === null || _designSettings_fonts === void 0 ? void 0 : _designSettings_fonts.heading) || 'serif'\n                                },\n                                children: \"You are invited to celebrate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors2 = designSettings.colors) === null || _designSettings_colors2 === void 0 ? void 0 : _designSettings_colors2.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts1 = designSettings.fonts) === null || _designSettings_fonts1 === void 0 ? void 0 : _designSettings_fonts1.coupleNames) || 'serif'\n                                },\n                                children: designSettings.couple_names || formData.couple_names || 'Couple Names'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold mb-1\",\n                                style: {\n                                    color: ((_designSettings_colors3 = designSettings.colors) === null || _designSettings_colors3 === void 0 ? void 0 : _designSettings_colors3.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts2 = designSettings.fonts) === null || _designSettings_fonts2 === void 0 ? void 0 : _designSettings_fonts2.date) || 'serif'\n                                },\n                                children: formData.wedding_date || 'Wedding Date'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                style: {\n                                    color: ((_designSettings_colors4 = designSettings.colors) === null || _designSettings_colors4 === void 0 ? void 0 : _designSettings_colors4.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts3 = designSettings.fonts) === null || _designSettings_fonts3 === void 0 ? void 0 : _designSettings_fonts3.location) || 'sans-serif'\n                                },\n                                children: formData.wedding_location || 'Wedding Location'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.about_couple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs italic\",\n                        style: {\n                            color: ((_designSettings_colors5 = designSettings.colors) === null || _designSettings_colors5 === void 0 ? void 0 : _designSettings_colors5.text) || '#000000',\n                            fontFamily: ((_designSettings_fonts4 = designSettings.fonts) === null || _designSettings_fonts4 === void 0 ? void 0 : _designSettings_fonts4.aboutCouple) || 'sans-serif'\n                        },\n                        children: formData.about_couple\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleSave = ()=>{\n        var _templates_;\n        console.log('Save button clicked');\n        // Process colors to ensure they're valid hex codes\n        const processedColors = {\n            ...designSettings.colors\n        };\n        Object.keys(processedColors).forEach((key)=>{\n            const color = processedColors[key];\n            if (color && !color.startsWith('#')) {\n                processedColors[key] = \"#\".concat(color);\n            }\n        });\n        // Process fonts to ensure they're valid\n        const processedFonts = {\n            ...designSettings.fonts\n        };\n        Object.keys(processedFonts).forEach((key)=>{\n            if (!processedFonts[key]) {\n                processedFonts[key] = fontOptions[0];\n            }\n        });\n        // Get template_id and REJECT any \"default-\" IDs\n        let templateId = (einvite === null || einvite === void 0 ? void 0 : einvite.template_id) || ((_templates_ = templates[0]) === null || _templates_ === void 0 ? void 0 : _templates_.template_id);\n        // REJECT any \"default-\" template IDs completely\n        if (templateId && templateId.includes('default-')) {\n            var _templates_find;\n            console.error('REJECTING invalid template ID in editor:', templateId);\n            templateId = (_templates_find = templates.find((t)=>!t.template_id.includes('default-'))) === null || _templates_find === void 0 ? void 0 : _templates_find.template_id;\n        }\n        if (!templateId) {\n            alert('No valid template available. Please refresh the page.');\n            return;\n        }\n        // Prepare the data - EXACTLY like website editor\n        const einviteData = {\n            title,\n            template_id: templateId,\n            wedding_date: weddingDate || null,\n            wedding_location: weddingLocation,\n            about_couple: aboutCouple,\n            couple_names: coupleNames,\n            design_settings: {\n                colors: processedColors,\n                fonts: processedFonts,\n                // Use only the custom image from upload or search\n                customImage: designSettings.customImage || '',\n                // Store couple_names in design_settings for backward compatibility\n                couple_names: coupleNames,\n                // Add type marker for e-invites\n                type: 'einvite'\n            },\n            is_published: true\n        };\n        console.log('Saving e-invite data:', einviteData);\n        onSave(einviteData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"p-2 hover:bg-gray-100 rounded-md transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gray-400 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-black\",\n                                    children: [\n                                        \"Our Most Trending \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#B31B1E]\",\n                                            children: \"Invites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n                            style: {\n                                backgroundImage: designSettings.customImage ? \"url(\".concat(designSettings.customImage, \")\") : 'none',\n                                backgroundColor: ((_designSettings_colors = designSettings.colors) === null || _designSettings_colors === void 0 ? void 0 : _designSettings_colors.background) || '#FFFFFF',\n                                backgroundSize: 'cover',\n                                backgroundPosition: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 p-6 flex flex-col justify-center items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mb-2\",\n                                                style: {\n                                                    color: ((_designSettings_colors1 = designSettings.colors) === null || _designSettings_colors1 === void 0 ? void 0 : _designSettings_colors1.text) || '#666666',\n                                                    fontFamily: ((_designSettings_fonts = designSettings.fonts) === null || _designSettings_fonts === void 0 ? void 0 : _designSettings_fonts.body) || 'Open Sans'\n                                                },\n                                                children: \"The pleasure your company is requested for\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                style: {\n                                                    color: ((_designSettings_colors2 = designSettings.colors) === null || _designSettings_colors2 === void 0 ? void 0 : _designSettings_colors2.primary) || '#B31B1E',\n                                                    fontFamily: ((_designSettings_fonts1 = designSettings.fonts) === null || _designSettings_fonts1 === void 0 ? void 0 : _designSettings_fonts1.heading) || 'Playfair Display'\n                                                },\n                                                children: \"THE WEDDING OF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: coupleNames ? ((_coupleNames_split_ = coupleNames.split('&')[0]) === null || _coupleNames_split_ === void 0 ? void 0 : _coupleNames_split_.trim()) || 'Eiyana' : 'Eiyana',\n                                                onChange: (e)=>{\n                                                    var _coupleNames_split_;\n                                                    const secondName = coupleNames ? ((_coupleNames_split_ = coupleNames.split('&')[1]) === null || _coupleNames_split_ === void 0 ? void 0 : _coupleNames_split_.trim()) || 'Warlin' : 'Warlin';\n                                                    setCoupleNames(\"\".concat(e.target.value, \" & \").concat(secondName));\n                                                },\n                                                className: \"text-3xl font-bold mb-2 bg-transparent border-none outline-none text-center w-full\",\n                                                style: {\n                                                    color: ((_designSettings_colors3 = designSettings.colors) === null || _designSettings_colors3 === void 0 ? void 0 : _designSettings_colors3.primary) || '#B31B1E',\n                                                    fontFamily: ((_designSettings_fonts2 = designSettings.fonts) === null || _designSettings_fonts2 === void 0 ? void 0 : _designSettings_fonts2.coupleNames) || 'Dancing Script'\n                                                },\n                                                placeholder: \"Bride Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-4 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-px bg-gray-400 flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        style: {\n                                                            color: ((_designSettings_colors4 = designSettings.colors) === null || _designSettings_colors4 === void 0 ? void 0 : _designSettings_colors4.text) || '#666666',\n                                                            fontFamily: ((_designSettings_fonts3 = designSettings.fonts) === null || _designSettings_fonts3 === void 0 ? void 0 : _designSettings_fonts3.body) || 'Open Sans'\n                                                        },\n                                                        children: \"AND\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-px bg-gray-400 flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: coupleNames ? ((_coupleNames_split_1 = coupleNames.split('&')[1]) === null || _coupleNames_split_1 === void 0 ? void 0 : _coupleNames_split_1.trim()) || 'Warlin' : 'Warlin',\n                                                onChange: (e)=>{\n                                                    var _coupleNames_split_;\n                                                    const firstName = coupleNames ? ((_coupleNames_split_ = coupleNames.split('&')[0]) === null || _coupleNames_split_ === void 0 ? void 0 : _coupleNames_split_.trim()) || 'Eiyana' : 'Eiyana';\n                                                    setCoupleNames(\"\".concat(firstName, \" & \").concat(e.target.value));\n                                                },\n                                                className: \"text-3xl font-bold bg-transparent border-none outline-none text-center w-full\",\n                                                style: {\n                                                    color: ((_designSettings_colors5 = designSettings.colors) === null || _designSettings_colors5 === void 0 ? void 0 : _designSettings_colors5.primary) || '#B31B1E',\n                                                    fontFamily: ((_designSettings_fonts4 = designSettings.fonts) === null || _designSettings_fonts4 === void 0 ? void 0 : _designSettings_fonts4.coupleNames) || 'Dancing Script'\n                                                },\n                                                placeholder: \"Groom Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: weddingDate,\n                                                onChange: (e)=>setWeddingDate(e.target.value),\n                                                className: \"text-sm mb-1 bg-transparent border-none outline-none text-center\",\n                                                style: {\n                                                    color: ((_designSettings_colors6 = designSettings.colors) === null || _designSettings_colors6 === void 0 ? void 0 : _designSettings_colors6.text) || '#666666',\n                                                    fontFamily: ((_designSettings_fonts5 = designSettings.fonts) === null || _designSettings_fonts5 === void 0 ? void 0 : _designSettings_fonts5.date) || 'Open Sans'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs mb-2\",\n                                                style: {\n                                                    color: ((_designSettings_colors7 = designSettings.colors) === null || _designSettings_colors7 === void 0 ? void 0 : _designSettings_colors7.text) || '#666666',\n                                                    fontFamily: ((_designSettings_fonts6 = designSettings.fonts) === null || _designSettings_fonts6 === void 0 ? void 0 : _designSettings_fonts6.body) || 'Open Sans'\n                                                },\n                                                children: \"At 6:00 o'clock in the evening\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-300 p-2 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: weddingLocation,\n                                                    onChange: (e)=>setWeddingLocation(e.target.value),\n                                                    className: \"text-xs font-semibold bg-transparent border-none outline-none text-center w-full\",\n                                                    style: {\n                                                        color: ((_designSettings_colors8 = designSettings.colors) === null || _designSettings_colors8 === void 0 ? void 0 : _designSettings_colors8.text) || '#666666',\n                                                        fontFamily: ((_designSettings_fonts7 = designSettings.fonts) === null || _designSettings_fonts7 === void 0 ? void 0 : _designSettings_fonts7.location) || 'Open Sans'\n                                                    },\n                                                    placeholder: \"Wedding Location\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(activeTab === 'design' ? 'content' : 'design'),\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    activeTab === 'design' ? 'Hide Settings' : 'Colors & Background'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Resize\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Undo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: \"Back to Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: loading,\n                                className: \"flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 16,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    \"Save & Continue Editing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === 'design' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: Object.entries(designSettings.colors || {}).map((param)=>{\n                                                let [colorType, colorValue] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-black mb-2 capitalize\",\n                                                            children: colorType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"color\",\n                                                                    value: colorValue || '#000000',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: colorValue || '',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black\",\n                                                                    placeholder: \"#000000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, colorType, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Background Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleImageUpload,\n                                                    className: \"hidden\",\n                                                    id: \"image-upload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"image-upload\",\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        uploadingImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        designSettings.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-black mb-2\",\n                                                    children: \"Current Background:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-20 bg-gray-200 rounded overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: designSettings.customImage,\n                                                        alt: \"Background\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInviteEditor, \"77H3G4EMQD+TN3xJ6g+tspSlyR4=\");\n_c = EInviteEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInviteEditor);\nvar _c;\n$RefreshReg$(_c, \"EInviteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    var _fonts_body, _fonts_decorative, _fonts_heading, _fonts_decorative1, _fonts_heading1;\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '600px',\n            width: '100%',\n            background: colors.background,\n            borderRadius: '20px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: ((_fonts_body = fonts.body) === null || _fonts_body === void 0 ? void 0 : _fonts_body.join(', ')) || 'Roboto, Arial, sans-serif'\n        },\n        headerDecoration: {\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            height: '8px',\n            width: '100%'\n        },\n        marigoldBorder: {\n            background: \"url(\\\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='\".concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='30' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='50' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='70' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='90' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/></svg>\\\") repeat-x\"),\n            height: '20px',\n            width: '100%'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '30px 20px 20px',\n            background: \"linear-gradient(180deg, \".concat(colors.background, \" 0%, #FFF8E7 100%)\")\n        },\n        ganeshSymbol: {\n            fontSize: '48px',\n            color: colors.primary,\n            marginBottom: '10px'\n        },\n        ganeshText: {\n            fontFamily: ((_fonts_decorative = fonts.decorative) === null || _fonts_decorative === void 0 ? void 0 : _fonts_decorative.join(', ')) || 'Dancing Script, cursive',\n            fontSize: '24px',\n            color: colors.primary,\n            fontWeight: '700',\n            marginBottom: '20px'\n        },\n        coupleNames: {\n            fontFamily: ((_fonts_heading = fonts.heading) === null || _fonts_heading === void 0 ? void 0 : _fonts_heading.join(', ')) || 'Playfair Display, serif',\n            fontSize: '42px',\n            fontWeight: '700',\n            color: colors.primary,\n            textAlign: 'center',\n            margin: '20px 0',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        andSymbol: {\n            fontFamily: ((_fonts_decorative1 = fonts.decorative) === null || _fonts_decorative1 === void 0 ? void 0 : _fonts_decorative1.join(', ')) || 'Dancing Script, cursive',\n            fontSize: '32px',\n            color: colors.secondary,\n            margin: '0 15px'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: ((_fonts_heading1 = fonts.heading) === null || _fonts_heading1 === void 0 ? void 0 : _fonts_heading1.join(', ')) || 'Playfair Display, serif',\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});