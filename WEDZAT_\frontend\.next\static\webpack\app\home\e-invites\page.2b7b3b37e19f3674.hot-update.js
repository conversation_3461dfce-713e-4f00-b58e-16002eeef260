"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/page.tsx":
/*!*************************************!*\
  !*** ./app/home/<USER>/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EInvitesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/HomeDashboard/Navigation */ \"(app-pages-browser)/./components/HomeDashboard/Navigation.tsx\");\n/* harmony import */ var _components_EInvites__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/EInvites */ \"(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EInvitesPage() {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarExpanded, setSidebarExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EInvitesPage.useEffect\": ()=>{\n            setIsClient(true);\n            console.log('E-Invites page loaded - URL:', window.location.href);\n            console.log('E-Invites page loaded - pathname:', window.location.pathname);\n            // FORCE CLEAR ALL CACHED DATA\n            if (true) {\n                // Clear ALL localStorage that might contain old template data\n                const keys = Object.keys(localStorage);\n                keys.forEach({\n                    \"EInvitesPage.useEffect\": (key)=>{\n                        const value = localStorage.getItem(key);\n                        if (value && (value.includes('default-') || value.includes('template') || value.includes('einvite'))) {\n                            console.log('Removing cached data:', key);\n                            localStorage.removeItem(key);\n                        }\n                    }\n                }[\"EInvitesPage.useEffect\"]);\n                // Clear ALL sessionStorage that might contain old template data\n                const sessionKeys = Object.keys(sessionStorage);\n                sessionKeys.forEach({\n                    \"EInvitesPage.useEffect\": (key)=>{\n                        const value = sessionStorage.getItem(key);\n                        if (value && (value.includes('default-') || value.includes('template') || value.includes('einvite'))) {\n                            console.log('Removing cached session data:', key);\n                            sessionStorage.removeItem(key);\n                        }\n                    }\n                }[\"EInvitesPage.useEffect\"]);\n            }\n            // Clear force reload flag if it exists (no longer needed)\n            const hasReloaded = sessionStorage.getItem('einvites-force-reloaded');\n            if (hasReloaded) {\n                sessionStorage.removeItem('einvites-force-reloaded');\n                console.log('Cleared force reload flag');\n            }\n        }\n    }[\"EInvitesPage.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"opacity-0\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n            lineNumber: 53,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen bg-white w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_2__.TopNavigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HomeDashboard_Navigation__WEBPACK_IMPORTED_MODULE_2__.SideNavigation, {\n                        expanded: sidebarExpanded,\n                        onExpand: ()=>setSidebarExpanded(true),\n                        onCollapse: ()=>setSidebarExpanded(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 bg-white mt-20 \".concat(sidebarExpanded ? \"md:ml-48\" : \"md:ml-20\"),\n                        style: {\n                            transition: \"all 300ms ease-in-out\",\n                            overflowY: \"auto\",\n                            overflowX: \"hidden\",\n                            width: \"100%\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full mx-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EInvites__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                setError: setError,\n                                setSuccessMessage: setSuccessMessage,\n                                setLoading: setLoading,\n                                loading: loading,\n                                error: error,\n                                successMessage: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\page.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_s(EInvitesPage, \"jqfuBXpjR9LqvnUCB/Psq2JkFwY=\");\n_c = EInvitesPage;\nvar _c;\n$RefreshReg$(_c, \"EInvitesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9ob21lL2UtaW52aXRlcy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUNtRDtBQUlHO0FBQ1Q7QUFFOUIsU0FBU007O0lBQ3RCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNRLGlCQUFpQkMsbUJBQW1CLEdBQUdULCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBVTtJQUNoRCxNQUFNLENBQUNZLE9BQU9DLFNBQVMsR0FBR2IsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ2MsZ0JBQWdCQyxrQkFBa0IsR0FBR2YsK0NBQVFBLENBQWdCO0lBRXBFQyxnREFBU0E7a0NBQUM7WUFDUk0sWUFBWTtZQUNaUyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDQyxPQUFPQyxRQUFRLENBQUNDLElBQUk7WUFDaEVKLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUNDLE9BQU9DLFFBQVEsQ0FBQ0UsUUFBUTtZQUV6RSw4QkFBOEI7WUFDOUIsSUFBSSxJQUE2QixFQUFFO2dCQUNqQyw4REFBOEQ7Z0JBQzlELE1BQU1DLE9BQU9DLE9BQU9ELElBQUksQ0FBQ0U7Z0JBQ3pCRixLQUFLRyxPQUFPOzhDQUFDQyxDQUFBQTt3QkFDWCxNQUFNQyxRQUFRSCxhQUFhSSxPQUFPLENBQUNGO3dCQUNuQyxJQUFJQyxTQUFVQSxDQUFBQSxNQUFNRSxRQUFRLENBQUMsZUFBZUYsTUFBTUUsUUFBUSxDQUFDLGVBQWVGLE1BQU1FLFFBQVEsQ0FBQyxVQUFTLEdBQUk7NEJBQ3BHYixRQUFRQyxHQUFHLENBQUMseUJBQXlCUzs0QkFDckNGLGFBQWFNLFVBQVUsQ0FBQ0o7d0JBQzFCO29CQUNGOztnQkFFQSxnRUFBZ0U7Z0JBQ2hFLE1BQU1LLGNBQWNSLE9BQU9ELElBQUksQ0FBQ1U7Z0JBQ2hDRCxZQUFZTixPQUFPOzhDQUFDQyxDQUFBQTt3QkFDbEIsTUFBTUMsUUFBUUssZUFBZUosT0FBTyxDQUFDRjt3QkFDckMsSUFBSUMsU0FBVUEsQ0FBQUEsTUFBTUUsUUFBUSxDQUFDLGVBQWVGLE1BQU1FLFFBQVEsQ0FBQyxlQUFlRixNQUFNRSxRQUFRLENBQUMsVUFBUyxHQUFJOzRCQUNwR2IsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ1M7NEJBQzdDTSxlQUFlRixVQUFVLENBQUNKO3dCQUM1QjtvQkFDRjs7WUFDRjtZQUVBLDBEQUEwRDtZQUMxRCxNQUFNTyxjQUFjRCxlQUFlSixPQUFPLENBQUM7WUFDM0MsSUFBSUssYUFBYTtnQkFDZkQsZUFBZUYsVUFBVSxDQUFDO2dCQUMxQmQsUUFBUUMsR0FBRyxDQUFDO1lBQ2Q7UUFDRjtpQ0FBRyxFQUFFO0lBRUwsSUFBSSxDQUFDWCxVQUFVO1FBQ2IscUJBQU8sOERBQUM0QjtZQUFJQyxXQUFVO3NCQUFZOzs7Ozs7SUFDcEM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNqQywrRUFBYUE7Ozs7OzBCQUVkLDhEQUFDZ0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaEMsZ0ZBQWNBO3dCQUNiaUMsVUFBVTVCO3dCQUNWNkIsVUFBVSxJQUFNNUIsbUJBQW1CO3dCQUNuQzZCLFlBQVksSUFBTTdCLG1CQUFtQjs7Ozs7O2tDQUl2Qyw4REFBQzhCO3dCQUNDSixXQUFXLDZCQUVWLE9BREMzQixrQkFBa0IsYUFBYTt3QkFFakNnQyxPQUFPOzRCQUNMQyxZQUFZOzRCQUNaQyxXQUFXOzRCQUNYQyxXQUFXOzRCQUNYQyxPQUFPO3dCQUNUO2tDQUVBLDRFQUFDVjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQy9CLDREQUFRQTtnQ0FDUFMsVUFBVUE7Z0NBQ1ZFLG1CQUFtQkE7Z0NBQ25CSixZQUFZQTtnQ0FDWkQsU0FBU0E7Z0NBQ1RFLE9BQU9BO2dDQUNQRSxnQkFBZ0JBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzlCO0dBckZ3QlQ7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXGFwcFxcaG9tZVxcZS1pbnZpdGVzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBUb3BOYXZpZ2F0aW9uLFxuICBTaWRlTmF2aWdhdGlvbixcbn0gZnJvbSBcIi4uLy4uLy4uL2NvbXBvbmVudHMvSG9tZURhc2hib2FyZC9OYXZpZ2F0aW9uXCI7XG5pbXBvcnQgRUludml0ZXMgZnJvbSBcIi4vY29tcG9uZW50cy9FSW52aXRlc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFSW52aXRlc1BhZ2UoKSB7XG4gIGNvbnN0IFtpc0NsaWVudCwgc2V0SXNDbGllbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2lkZWJhckV4cGFuZGVkLCBzZXRTaWRlYmFyRXhwYW5kZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdWNjZXNzTWVzc2FnZSwgc2V0U3VjY2Vzc01lc3NhZ2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRJc0NsaWVudCh0cnVlKTtcbiAgICBjb25zb2xlLmxvZygnRS1JbnZpdGVzIHBhZ2UgbG9hZGVkIC0gVVJMOicsIHdpbmRvdy5sb2NhdGlvbi5ocmVmKTtcbiAgICBjb25zb2xlLmxvZygnRS1JbnZpdGVzIHBhZ2UgbG9hZGVkIC0gcGF0aG5hbWU6Jywgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lKTtcblxuICAgIC8vIEZPUkNFIENMRUFSIEFMTCBDQUNIRUQgREFUQVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgLy8gQ2xlYXIgQUxMIGxvY2FsU3RvcmFnZSB0aGF0IG1pZ2h0IGNvbnRhaW4gb2xkIHRlbXBsYXRlIGRhdGFcbiAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpO1xuICAgICAga2V5cy5mb3JFYWNoKGtleSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KTtcbiAgICAgICAgaWYgKHZhbHVlICYmICh2YWx1ZS5pbmNsdWRlcygnZGVmYXVsdC0nKSB8fCB2YWx1ZS5pbmNsdWRlcygndGVtcGxhdGUnKSB8fCB2YWx1ZS5pbmNsdWRlcygnZWludml0ZScpKSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdSZW1vdmluZyBjYWNoZWQgZGF0YTonLCBrZXkpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICAvLyBDbGVhciBBTEwgc2Vzc2lvblN0b3JhZ2UgdGhhdCBtaWdodCBjb250YWluIG9sZCB0ZW1wbGF0ZSBkYXRhXG4gICAgICBjb25zdCBzZXNzaW9uS2V5cyA9IE9iamVjdC5rZXlzKHNlc3Npb25TdG9yYWdlKTtcbiAgICAgIHNlc3Npb25LZXlzLmZvckVhY2goa2V5ID0+IHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICAgIGlmICh2YWx1ZSAmJiAodmFsdWUuaW5jbHVkZXMoJ2RlZmF1bHQtJykgfHwgdmFsdWUuaW5jbHVkZXMoJ3RlbXBsYXRlJykgfHwgdmFsdWUuaW5jbHVkZXMoJ2VpbnZpdGUnKSkpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnUmVtb3ZpbmcgY2FjaGVkIHNlc3Npb24gZGF0YTonLCBrZXkpO1xuICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8gQ2xlYXIgZm9yY2UgcmVsb2FkIGZsYWcgaWYgaXQgZXhpc3RzIChubyBsb25nZXIgbmVlZGVkKVxuICAgIGNvbnN0IGhhc1JlbG9hZGVkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnZWludml0ZXMtZm9yY2UtcmVsb2FkZWQnKTtcbiAgICBpZiAoaGFzUmVsb2FkZWQpIHtcbiAgICAgIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ2VpbnZpdGVzLWZvcmNlLXJlbG9hZGVkJyk7XG4gICAgICBjb25zb2xlLmxvZygnQ2xlYXJlZCBmb3JjZSByZWxvYWQgZmxhZycpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGlmICghaXNDbGllbnQpIHtcbiAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJvcGFjaXR5LTBcIj5Mb2FkaW5nLi4uPC9kaXY+O1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuIGJnLXdoaXRlIHctZnVsbFwiPlxuICAgICAgey8qIFRvcCBOYXZpZ2F0aW9uIEJhciAqL31cbiAgICAgIDxUb3BOYXZpZ2F0aW9uIC8+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LTEgYmctd2hpdGVcIj5cbiAgICAgICAgPFNpZGVOYXZpZ2F0aW9uXG4gICAgICAgICAgZXhwYW5kZWQ9e3NpZGViYXJFeHBhbmRlZH1cbiAgICAgICAgICBvbkV4cGFuZD17KCkgPT4gc2V0U2lkZWJhckV4cGFuZGVkKHRydWUpfVxuICAgICAgICAgIG9uQ29sbGFwc2U9eygpID0+IHNldFNpZGViYXJFeHBhbmRlZChmYWxzZSl9XG4gICAgICAgIC8+XG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgICAgPG1haW5cbiAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcC00IGJnLXdoaXRlIG10LTIwICR7XG4gICAgICAgICAgICBzaWRlYmFyRXhwYW5kZWQgPyBcIm1kOm1sLTQ4XCIgOiBcIm1kOm1sLTIwXCJcbiAgICAgICAgICB9YH1cbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMzAwbXMgZWFzZS1pbi1vdXRcIixcbiAgICAgICAgICAgIG92ZXJmbG93WTogXCJhdXRvXCIsXG4gICAgICAgICAgICBvdmVyZmxvd1g6IFwiaGlkZGVuXCIsXG4gICAgICAgICAgICB3aWR0aDogXCIxMDAlXCJcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbXgtYXV0byBtYXgtdy1mdWxsXCI+XG4gICAgICAgICAgICA8RUludml0ZXNcbiAgICAgICAgICAgICAgc2V0RXJyb3I9e3NldEVycm9yfVxuICAgICAgICAgICAgICBzZXRTdWNjZXNzTWVzc2FnZT17c2V0U3VjY2Vzc01lc3NhZ2V9XG4gICAgICAgICAgICAgIHNldExvYWRpbmc9e3NldExvYWRpbmd9XG4gICAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGVycm9yPXtlcnJvcn1cbiAgICAgICAgICAgICAgc3VjY2Vzc01lc3NhZ2U9e3N1Y2Nlc3NNZXNzYWdlfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlRvcE5hdmlnYXRpb24iLCJTaWRlTmF2aWdhdGlvbiIsIkVJbnZpdGVzIiwiRUludml0ZXNQYWdlIiwiaXNDbGllbnQiLCJzZXRJc0NsaWVudCIsInNpZGViYXJFeHBhbmRlZCIsInNldFNpZGViYXJFeHBhbmRlZCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3NNZXNzYWdlIiwic2V0U3VjY2Vzc01lc3NhZ2UiLCJjb25zb2xlIiwibG9nIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwicGF0aG5hbWUiLCJrZXlzIiwiT2JqZWN0IiwibG9jYWxTdG9yYWdlIiwiZm9yRWFjaCIsImtleSIsInZhbHVlIiwiZ2V0SXRlbSIsImluY2x1ZGVzIiwicmVtb3ZlSXRlbSIsInNlc3Npb25LZXlzIiwic2Vzc2lvblN0b3JhZ2UiLCJoYXNSZWxvYWRlZCIsImRpdiIsImNsYXNzTmFtZSIsImV4cGFuZGVkIiwib25FeHBhbmQiLCJvbkNvbGxhcHNlIiwibWFpbiIsInN0eWxlIiwidHJhbnNpdGlvbiIsIm92ZXJmbG93WSIsIm92ZXJmbG93WCIsIndpZHRoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/page.tsx\n"));

/***/ })

});