"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx":
/*!*********************************************************!*\
  !*** ./app/home/<USER>/components/EInviteEditor.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst EInviteEditor = (param)=>{\n    let { templates, einvite, onSave, onCancel, loading } = param;\n    var _einvite_design_settings;\n    _s();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.title) || \"Our Wedding E-Invite\");\n    const [weddingDate, setWeddingDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_date) || \"\");\n    const [weddingLocation, setWeddingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_location) || \"\");\n    const [aboutCouple, setAboutCouple] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.about_couple) || \"\");\n    const [coupleNames, setCoupleNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.couple_names) || (einvite === null || einvite === void 0 ? void 0 : (_einvite_design_settings = einvite.design_settings) === null || _einvite_design_settings === void 0 ? void 0 : _einvite_design_settings.couple_names) || \"\");\n    const [designSettings, setDesignSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.design_settings) || {\n        colors: {\n            primary: \"#B31B1E\",\n            secondary: \"#333333\",\n            background: \"#FFFFFF\",\n            text: \"#000000\"\n        },\n        fonts: {\n            heading: \"Playfair Display\",\n            body: \"Open Sans\",\n            coupleNames: \"Dancing Script\",\n            date: \"Open Sans\",\n            location: \"Open Sans\",\n            aboutCouple: \"Open Sans\"\n        },\n        customImage: \"\",\n        type: \"einvite\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('content');\n    const [uploadingImage, setUploadingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchingImages, setSearchingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Find the selected template based on the einvite's template_id\n    const selectedTemplate = templates.find((t)=>t.template_id === (einvite === null || einvite === void 0 ? void 0 : einvite.template_id)) || templates[0];\n    const fontOptions = [\n        \"Arial\",\n        \"Helvetica\",\n        \"Times New Roman\",\n        \"Georgia\",\n        \"Verdana\",\n        \"Playfair Display\",\n        \"Open Sans\",\n        \"Lato\",\n        \"Roboto\",\n        \"Dancing Script\",\n        \"Great Vibes\",\n        \"Pacifico\",\n        \"Lobster\",\n        \"Montserrat\",\n        \"Poppins\"\n    ];\n    const handleColorChange = (colorType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                colors: {\n                    ...prev.colors,\n                    [colorType]: value\n                }\n            }));\n    };\n    const handleFontChange = (fontType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                fonts: {\n                    ...prev.fonts,\n                    [fontType]: value\n                }\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        setUploadingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', file);\n            const response = await fetch('/api/upload-image', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setDesignSettings((prev)=>({\n                        ...prev,\n                        customImage: data.imageUrl\n                    }));\n            } else {\n                console.error('Failed to upload image');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n        } finally{\n            setUploadingImage(false);\n        }\n    };\n    const searchImages = async ()=>{\n        if (!searchQuery.trim()) return;\n        setSearchingImages(true);\n        try {\n            const response = await fetch(\"/api/search-images?q=\".concat(encodeURIComponent(searchQuery)));\n            if (response.ok) {\n                const data = await response.json();\n                setSearchResults(data.images || []);\n            }\n        } catch (error) {\n            console.error('Error searching images:', error);\n        } finally{\n            setSearchingImages(false);\n        }\n    };\n    const selectSearchImage = (imageUrl)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                customImage: imageUrl\n            }));\n        setSearchResults([]);\n        setSearchQuery(\"\");\n    };\n    // Function to render template with image-based text overlay\n    const renderTemplate = ()=>{\n        var _designSettings_colors, _designSettings_colors1, _designSettings_fonts, _designSettings_colors2, _designSettings_fonts1, _designSettings_colors3, _designSettings_fonts2, _designSettings_colors4, _designSettings_fonts3, _designSettings_colors5, _designSettings_fonts4;\n        // Check if this is the Indian Traditional template\n        if ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.template_id) === 'indian-traditional-001' || (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) === 'Indian Traditional') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/templates/indian-traditional-template.jpg\",\n                        alt: \"Indian Traditional Template\",\n                        className: \"w-full h-full object-cover absolute inset-0\",\n                        style: {\n                            zIndex: 1\n                        },\n                        onError: (e)=>{\n                            // Fallback if image not found\n                            const target = e.target;\n                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjQjMxQjFFIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMjAwIiBmaWxsPSIjRkZGRkZGIiBmb250LXNpemU9IjE2IiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SW5kaWFuIFRyYWRpdGlvbmFsPC90ZXh0Pgo8L3N2Zz4=';\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        style: {\n                            zIndex: 2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[0]) || 'Navneet',\n                                onChange: (e)=>{\n                                    const secondName = (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[1]) || 'Suknya';\n                                    setCoupleNames(\"\".concat(e.target.value, \" & \").concat(secondName));\n                                },\n                                className: \"absolute bg-transparent border-none outline-none text-center font-bold text-red-800\",\n                                style: {\n                                    top: '45%',\n                                    left: '20%',\n                                    width: '25%',\n                                    fontSize: '18px',\n                                    color: '#B31B1E',\n                                    fontFamily: 'Playfair Display, serif'\n                                },\n                                placeholder: \"Partner 1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[1]) || 'Suknya',\n                                onChange: (e)=>{\n                                    const firstName = (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[0]) || 'Navneet';\n                                    setCoupleNames(\"\".concat(firstName, \" & \").concat(e.target.value));\n                                },\n                                className: \"absolute bg-transparent border-none outline-none text-center font-bold text-red-800\",\n                                style: {\n                                    top: '45%',\n                                    right: '20%',\n                                    width: '25%',\n                                    fontSize: '18px',\n                                    color: '#B31B1E',\n                                    fontFamily: 'Playfair Display, serif'\n                                },\n                                placeholder: \"Partner 2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: weddingDate || 'Monday, 22th Aug 2022',\n                                onChange: (e)=>setWeddingDate(e.target.value),\n                                className: \"absolute bg-transparent border-none outline-none text-center text-white\",\n                                style: {\n                                    top: '65%',\n                                    left: '50%',\n                                    transform: 'translateX(-50%)',\n                                    width: '60%',\n                                    fontSize: '14px',\n                                    color: '#FFD700'\n                                },\n                                placeholder: \"Wedding Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',\n                                onChange: (e)=>setWeddingLocation(e.target.value),\n                                className: \"absolute bg-transparent border-none outline-none text-center text-white resize-none\",\n                                style: {\n                                    top: '75%',\n                                    left: '50%',\n                                    transform: 'translateX(-50%)',\n                                    width: '80%',\n                                    height: '15%',\n                                    fontSize: '12px',\n                                    color: 'white'\n                                },\n                                placeholder: \"Wedding Venue\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Default template rendering for other templates\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n            style: {\n                backgroundImage: designSettings.customImage ? \"url(\".concat(designSettings.customImage, \")\") : 'none',\n                backgroundColor: ((_designSettings_colors = designSettings.colors) === null || _designSettings_colors === void 0 ? void 0 : _designSettings_colors.background) || '#FFFFFF',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 p-6 flex flex-col justify-center items-center text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors1 = designSettings.colors) === null || _designSettings_colors1 === void 0 ? void 0 : _designSettings_colors1.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts = designSettings.fonts) === null || _designSettings_fonts === void 0 ? void 0 : _designSettings_fonts.heading) || 'serif'\n                                },\n                                children: \"You are invited to celebrate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors2 = designSettings.colors) === null || _designSettings_colors2 === void 0 ? void 0 : _designSettings_colors2.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts1 = designSettings.fonts) === null || _designSettings_fonts1 === void 0 ? void 0 : _designSettings_fonts1.coupleNames) || 'serif'\n                                },\n                                children: designSettings.couple_names || coupleNames || 'Couple Names'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold mb-1\",\n                                style: {\n                                    color: ((_designSettings_colors3 = designSettings.colors) === null || _designSettings_colors3 === void 0 ? void 0 : _designSettings_colors3.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts2 = designSettings.fonts) === null || _designSettings_fonts2 === void 0 ? void 0 : _designSettings_fonts2.date) || 'serif'\n                                },\n                                children: weddingDate || 'Wedding Date'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                style: {\n                                    color: ((_designSettings_colors4 = designSettings.colors) === null || _designSettings_colors4 === void 0 ? void 0 : _designSettings_colors4.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts3 = designSettings.fonts) === null || _designSettings_fonts3 === void 0 ? void 0 : _designSettings_fonts3.location) || 'sans-serif'\n                                },\n                                children: weddingLocation || 'Wedding Location'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined),\n                    aboutCouple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs italic\",\n                        style: {\n                            color: ((_designSettings_colors5 = designSettings.colors) === null || _designSettings_colors5 === void 0 ? void 0 : _designSettings_colors5.text) || '#000000',\n                            fontFamily: ((_designSettings_fonts4 = designSettings.fonts) === null || _designSettings_fonts4 === void 0 ? void 0 : _designSettings_fonts4.aboutCouple) || 'sans-serif'\n                        },\n                        children: aboutCouple\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n            lineNumber: 279,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleSave = ()=>{\n        var _templates_;\n        console.log('Save button clicked');\n        // Process colors to ensure they're valid hex codes\n        const processedColors = {\n            ...designSettings.colors\n        };\n        Object.keys(processedColors).forEach((key)=>{\n            const color = processedColors[key];\n            if (color && !color.startsWith('#')) {\n                processedColors[key] = \"#\".concat(color);\n            }\n        });\n        // Process fonts to ensure they're valid\n        const processedFonts = {\n            ...designSettings.fonts\n        };\n        Object.keys(processedFonts).forEach((key)=>{\n            if (!processedFonts[key]) {\n                processedFonts[key] = fontOptions[0];\n            }\n        });\n        // Get template_id and REJECT any \"default-\" IDs\n        let templateId = (einvite === null || einvite === void 0 ? void 0 : einvite.template_id) || ((_templates_ = templates[0]) === null || _templates_ === void 0 ? void 0 : _templates_.template_id);\n        // REJECT any \"default-\" template IDs completely\n        if (templateId && templateId.includes('default-')) {\n            var _templates_find;\n            console.error('REJECTING invalid template ID in editor:', templateId);\n            templateId = (_templates_find = templates.find((t)=>!t.template_id.includes('default-'))) === null || _templates_find === void 0 ? void 0 : _templates_find.template_id;\n        }\n        if (!templateId) {\n            alert('No valid template available. Please refresh the page.');\n            return;\n        }\n        // Prepare the data - EXACTLY like website editor\n        const einviteData = {\n            title,\n            template_id: templateId,\n            wedding_date: weddingDate || null,\n            wedding_location: weddingLocation,\n            about_couple: aboutCouple,\n            couple_names: coupleNames,\n            design_settings: {\n                colors: processedColors,\n                fonts: processedFonts,\n                // Use only the custom image from upload or search\n                customImage: designSettings.customImage || '',\n                // Store couple_names in design_settings for backward compatibility\n                couple_names: coupleNames,\n                // Add type marker for e-invites\n                type: 'einvite'\n            },\n            is_published: true\n        };\n        console.log('Saving e-invite data:', einviteData);\n        onSave(einviteData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"p-2 hover:bg-gray-100 rounded-md transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gray-400 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-black\",\n                                    children: [\n                                        \"Our Most Trending \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#B31B1E]\",\n                                            children: \"Invites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto mb-6\",\n                        children: renderTemplate()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(activeTab === 'design' ? 'content' : 'design'),\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    activeTab === 'design' ? 'Hide Settings' : 'Colors & Background'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Resize\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Undo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: \"Back to Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: loading,\n                                className: \"flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    \"Save & Continue Editing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === 'design' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: Object.entries(designSettings.colors || {}).map((param)=>{\n                                                let [colorType, colorValue] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-black mb-2 capitalize\",\n                                                            children: colorType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"color\",\n                                                                    value: colorValue || '#000000',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: colorValue || '',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black\",\n                                                                    placeholder: \"#000000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, colorType, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Background Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleImageUpload,\n                                                    className: \"hidden\",\n                                                    id: \"image-upload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"image-upload\",\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        uploadingImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        designSettings.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-black mb-2\",\n                                                    children: \"Current Background:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-20 bg-gray-200 rounded overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: designSettings.customImage,\n                                                        alt: \"Background\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n        lineNumber: 405,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInviteEditor, \"77H3G4EMQD+TN3xJ6g+tspSlyR4=\");\n_c = EInviteEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInviteEditor);\nvar _c;\n$RefreshReg$(_c, \"EInviteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\n"));

/***/ })

});