"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction MyApp({ Component, pageProps }) {\n    // Clean up browser extension attributes on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MyApp.useEffect\": ()=>{\n            const cleanupExtensionAttributes = {\n                \"MyApp.useEffect.cleanupExtensionAttributes\": ()=>{\n                    // Target common extension attributes\n                    const attributesToRemove = [\n                        'bis_skin_checked',\n                        '__processed_',\n                        'data-bis-'\n                    ];\n                    // Get all elements\n                    const allElements = document.querySelectorAll('*');\n                    // Remove attributes from each element\n                    allElements.forEach({\n                        \"MyApp.useEffect.cleanupExtensionAttributes\": (el)=>{\n                            for(let i = 0; i < el.attributes.length; i++){\n                                const attr = el.attributes[i];\n                                for (const badAttr of attributesToRemove){\n                                    if (attr.name.includes(badAttr)) {\n                                        el.removeAttribute(attr.name);\n                                        // Adjust index since we removed an attribute\n                                        i--;\n                                        break;\n                                    }\n                                }\n                            }\n                        }\n                    }[\"MyApp.useEffect.cleanupExtensionAttributes\"]);\n                }\n            }[\"MyApp.useEffect.cleanupExtensionAttributes\"];\n            // Run immediately\n            cleanupExtensionAttributes();\n            // Also run after a short delay to catch any late additions\n            const timeoutId = setTimeout(cleanupExtensionAttributes, 0);\n            // Run again when DOM changes\n            const observer = new MutationObserver({\n                \"MyApp.useEffect\": function(mutations) {\n                    cleanupExtensionAttributes();\n                }\n            }[\"MyApp.useEffect\"]);\n            // Start observing the document\n            observer.observe(document.documentElement, {\n                childList: true,\n                subtree: true,\n                attributes: true\n            });\n            // Cleanup\n            return ({\n                \"MyApp.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    observer.disconnect();\n                }\n            })[\"MyApp.useEffect\"];\n        }\n    }[\"MyApp.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_app.tsx\",\n        lineNumber: 59,\n        columnNumber: 10\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(pages-dir-node)/./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();