import json
import uuid
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, date
from codebase.auth import validate_token
from codebase.database import get_db_connection

def get_user_einvites(event):
    """Get all e-invites for a user (websites marked as type='einvite')"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Join with templates to get template information
        # Filter for e-invites by checking design_settings->type = 'einvite'
        cursor.execute('''
            SELECT w.*, t.name as template_name, t.thumbnail_url as template_thumbnail
            FROM user_websites w
            LEFT JOIN website_templates t ON w.template_id = t.template_id
            WHERE w.user_id = %s 
            AND (w.design_settings->>'type' = 'einvite' OR w.design_settings->'type' IS NULL)
            ORDER BY w.updated_at DESC
        ''', (user_id,))

        einvites = cursor.fetchall()

        # Convert to serializable format
        serializable_einvites = []
        for einvite in einvites:
            einvite_dict = dict(einvite)
            for key, value in einvite_dict.items():
                if isinstance(value, (datetime, date)):
                    einvite_dict[key] = value.isoformat()
            serializable_einvites.append(einvite_dict)

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'einvites': serializable_einvites})
        }

    except Exception as e:
        print(f"Error fetching e-invites: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Internal server error'})
        }
    finally:
        if conn:
            conn.close()

def create_einvite(event):
    """Create a new e-invite"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        body = json.loads(event.get('body', '{}'))
        
        # Extract data from request
        title = body.get('title', 'Our Wedding E-Invite')
        couple_names = body.get('couple_names', '')
        template_id = body.get('template_id')
        wedding_date = body.get('wedding_date')
        wedding_location = body.get('wedding_location', '')
        about_couple = body.get('about_couple', '')
        design_settings = body.get('design_settings', {})
        is_published = body.get('is_published', True)

        # Ensure this is marked as an e-invite
        design_settings['type'] = 'einvite'

        if not template_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'template_id is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Generate a unique e-invite ID
            einvite_id = str(uuid.uuid4())

            # Generate an auth-protected URL for e-invites
            # This will require authentication to view
            deployed_url = f"/e-invite/{einvite_id}"

            # Check if the couple_names column exists
            cursor.execute(
                "SELECT column_name FROM information_schema.columns WHERE table_name='user_websites' AND column_name='couple_names'"
            )
            column_exists = cursor.fetchone()

            # If the column exists, include it in the INSERT
            if column_exists:
                cursor.execute(
                    '''INSERT INTO user_websites
                    (website_id, user_id, title, couple_names, template_id, wedding_date, wedding_location,
                     about_couple, design_settings, deployed_url, is_published)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING *''',
                    (einvite_id, user_id, title, couple_names, template_id, wedding_date, wedding_location,
                     about_couple, json.dumps(design_settings), deployed_url, is_published)
                )
            else:
                # If the column doesn't exist, store couple_names in the design_settings JSON
                if couple_names:
                    design_settings['couple_names'] = couple_names

                cursor.execute(
                    '''INSERT INTO user_websites
                    (website_id, user_id, title, template_id, wedding_date, wedding_location,
                     about_couple, design_settings, deployed_url, is_published)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING *''',
                    (einvite_id, user_id, title, template_id, wedding_date, wedding_location,
                     about_couple, json.dumps(design_settings), deployed_url, is_published)
                )

            new_einvite = cursor.fetchone()
            conn.commit()

            # Convert to serializable format
            einvite_dict = dict(new_einvite)
            for key, value in einvite_dict.items():
                if isinstance(value, (datetime, date)):
                    einvite_dict[key] = value.isoformat()

            return {
                'statusCode': 201,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'einvite': einvite_dict})
            }

        except Exception as e:
            print(f"Database error: {str(e)}")
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, POST',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Failed to create e-invite'})
            }
        finally:
            if conn:
                conn.close()

    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, POST',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Invalid JSON in request body'})
        }

def update_einvite(event):
    """Update an existing e-invite"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        body = json.loads(event.get('body', '{}'))
        
        # Extract data from request
        einvite_id = body.get('website_id')
        title = body.get('title')
        couple_names = body.get('couple_names')
        template_id = body.get('template_id')
        wedding_date = body.get('wedding_date')
        wedding_location = body.get('wedding_location')
        about_couple = body.get('about_couple')
        design_settings = body.get('design_settings')
        is_published = body.get('is_published')

        if not einvite_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'website_id is required'})
            }

        # Ensure this remains marked as an e-invite
        if design_settings:
            design_settings['type'] = 'einvite'

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)

            # Generate a new deployed URL with timestamp for cache busting
            deployed_url = f"/e-invite/{einvite_id}?v={int(datetime.now().timestamp())}"

            # Build the update query dynamically based on provided fields
            update_fields = []
            update_values = []

            if title is not None:
                update_fields.append("title = %s")
                update_values.append(title)

            # Check if the couple_names column exists
            cursor.execute(
                "SELECT column_name FROM information_schema.columns WHERE table_name='user_websites' AND column_name='couple_names'"
            )
            column_exists = cursor.fetchone()

            if couple_names is not None:
                if column_exists:
                    update_fields.append("couple_names = %s")
                    update_values.append(couple_names)
                else:
                    # Store in design_settings if column doesn't exist
                    if design_settings is None:
                        design_settings = {}
                    design_settings['couple_names'] = couple_names

            if template_id is not None:
                update_fields.append("template_id = %s")
                update_values.append(template_id)

            if wedding_date is not None:
                update_fields.append("wedding_date = %s")
                update_values.append(wedding_date if wedding_date else None)

            if wedding_location is not None:
                update_fields.append("wedding_location = %s")
                update_values.append(wedding_location)

            if about_couple is not None:
                update_fields.append("about_couple = %s")
                update_values.append(about_couple)

            if design_settings is not None:
                update_fields.append("design_settings = %s")
                update_values.append(json.dumps(design_settings))

            if is_published is not None:
                update_fields.append("is_published = %s")
                update_values.append(is_published)

            # Always update the deployed URL and updated_at timestamp
            update_fields.append("deployed_url = %s")
            update_values.append(deployed_url)

            update_fields.append("updated_at = NOW()")

            # Add the einvite_id and user_id to the values list
            update_values.append(einvite_id)
            update_values.append(user_id)

            # Execute the update query
            cursor.execute(
                f'''UPDATE user_websites
                SET {', '.join(update_fields)}
                WHERE website_id = %s AND user_id = %s
                RETURNING *''',
                update_values
            )

            updated_einvite = cursor.fetchone()

            if not updated_einvite:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'E-Invite not found or access denied'})
                }

            conn.commit()

            # Convert to serializable format
            einvite_dict = dict(updated_einvite)
            for key, value in einvite_dict.items():
                if isinstance(value, (datetime, date)):
                    einvite_dict[key] = value.isoformat()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'einvite': einvite_dict})
            }

        except Exception as e:
            print(f"Database error: {str(e)}")
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Failed to update e-invite'})
            }
        finally:
            if conn:
                conn.close()

    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, PUT',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Invalid JSON in request body'})
        }

def delete_einvite(event):
    """Delete an e-invite"""
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    try:
        body = json.loads(event.get('body', '{}'))
        einvite_id = body.get('website_id')

        if not einvite_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'website_id is required'})
            }

        conn = None
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Delete the e-invite (only if it belongs to the user)
            cursor.execute(
                '''DELETE FROM user_websites 
                WHERE website_id = %s AND user_id = %s
                RETURNING website_id''',
                (einvite_id, user_id)
            )

            deleted_einvite = cursor.fetchone()

            if not deleted_einvite:
                return {
                    'statusCode': 404,
                    'headers': {
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    },
                    'body': json.dumps({'error': 'E-Invite not found or access denied'})
                }

            conn.commit()

            return {
                'statusCode': 200,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'message': 'E-Invite deleted successfully'})
            }

        except Exception as e:
            print(f"Database error: {str(e)}")
            return {
                'statusCode': 500,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'Failed to delete e-invite'})
            }
        finally:
            if conn:
                conn.close()

    except json.JSONDecodeError:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Invalid JSON in request body'})
        }

def get_public_einvite(event):
    """Get a public view of an e-invite (requires authentication)"""
    # For e-invites, we still require authentication
    user_id, error_response = validate_token(event.get('headers', {}))
    if error_response:
        return error_response

    # Get einvite_id from query parameters
    query_params = event.get('queryStringParameters', {}) or {}
    einvite_id = query_params.get('einvite_id')

    if not einvite_id:
        return {
            'statusCode': 400,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'einvite_id is required'})
        }

    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Get the e-invite with template information
        cursor.execute('''
            SELECT w.*, t.name as template_name, t.thumbnail_url as template_thumbnail
            FROM user_websites w
            LEFT JOIN website_templates t ON w.template_id = t.template_id
            WHERE w.website_id = %s 
            AND (w.design_settings->>'type' = 'einvite' OR w.design_settings->'type' IS NULL)
        ''', (einvite_id,))

        einvite = cursor.fetchone()

        if not einvite:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'OPTIONS, GET',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                },
                'body': json.dumps({'error': 'E-Invite not found'})
            }

        # Convert to serializable format
        einvite_dict = dict(einvite)
        for key, value in einvite_dict.items():
            if isinstance(value, (datetime, date)):
                einvite_dict[key] = value.isoformat()

        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'einvite': einvite_dict})
        }

    except Exception as e:
        print(f"Error fetching public e-invite: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'OPTIONS, GET',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps({'error': 'Internal server error'})
        }
    finally:
        if conn:
            conn.close()
