"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '600px',\n            width: '100%',\n            background: colors.background,\n            borderRadius: '20px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        headerDecoration: {\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            height: '8px',\n            width: '100%'\n        },\n        marigoldBorder: {\n            background: \"url(\\\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='\".concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='30' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='50' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='70' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='90' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/></svg>\\\") repeat-x\"),\n            height: '20px',\n            width: '100%'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '30px 20px 20px',\n            background: \"linear-gradient(180deg, \".concat(colors.background, \" 0%, #FFF8E7 100%)\")\n        },\n        ganeshSymbol: {\n            fontSize: '48px',\n            color: colors.primary,\n            marginBottom: '10px'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '24px',\n            color: colors.primary,\n            fontWeight: '700',\n            marginBottom: '20px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '42px',\n            fontWeight: '700',\n            color: colors.primary,\n            textAlign: 'center',\n            margin: '20px 0',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '32px',\n            color: colors.secondary,\n            margin: '0 15px'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});