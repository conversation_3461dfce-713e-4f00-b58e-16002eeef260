#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the Indian Wedding Template to the database
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import uuid
import json
import os
from datetime import datetime

def get_db_connection():
    """Get database connection"""
    try:
        # Database connection parameters
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'wedzat'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', 'password'),
            port=os.getenv('DB_PORT', '5432')
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def add_indian_wedding_template():
    """Add the Indian Wedding Template to the database"""
    
    conn = get_db_connection()
    if not conn:
        print("Failed to connect to database")
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Template data
        template_id = str(uuid.uuid4())
        template_data = {
            'template_id': template_id,
            'name': 'Indian Traditional',
            'thumbnail_url': 'https://via.placeholder.com/300x400/B31B1E/FFFFFF?text=Indian+Traditional',
            'description': 'A beautiful traditional Indian wedding invitation with Ganesh blessings, marigold decorations, and vibrant colors.',
            'default_colors': json.dumps({
                'primary': '#B31B1E',      # Deep red
                'secondary': '#FFD700',    # Gold
                'background': '#FFFFFF',   # White
                'text': '#000000',         # Black
                'accent': '#FF8C00',       # Orange
                'border': '#DAA520'        # Golden rod
            }),
            'default_fonts': json.dumps({
                'heading': ['Playfair Display', 'Georgia', 'serif'],
                'body': ['Roboto', 'Arial', 'sans-serif'],
                'decorative': ['Dancing Script', 'cursive']
            })
        }
        
        # Check if template already exists
        cursor.execute(
            "SELECT template_id FROM website_templates WHERE name = %s",
            (template_data['name'],)
        )
        existing = cursor.fetchone()
        
        if existing:
            print(f"Template '{template_data['name']}' already exists with ID: {existing['template_id']}")
            return True
        
        # Insert the template
        cursor.execute(
            '''INSERT INTO website_templates 
               (template_id, name, thumbnail_url, description, default_colors, default_fonts, created_at, updated_at)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s)''',
            (
                template_data['template_id'],
                template_data['name'],
                template_data['thumbnail_url'],
                template_data['description'],
                template_data['default_colors'],
                template_data['default_fonts'],
                datetime.now(),
                datetime.now()
            )
        )
        
        conn.commit()
        print(f"Successfully added Indian Wedding Template with ID: {template_id}")
        
        # Verify the insertion
        cursor.execute("SELECT * FROM website_templates WHERE template_id = %s", (template_id,))
        result = cursor.fetchone()
        if result:
            print("Template verified in database:")
            print(f"  - Name: {result['name']}")
            print(f"  - Description: {result['description']}")
            print(f"  - Created: {result['created_at']}")
        
        return True
        
    except Exception as e:
        print(f"Error adding template: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def list_all_templates():
    """List all templates in the database"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("SELECT template_id, name, description, created_at FROM website_templates ORDER BY created_at")
        templates = cursor.fetchall()
        
        print("\nAll Templates in Database:")
        print("-" * 50)
        for template in templates:
            print(f"ID: {template['template_id']}")
            print(f"Name: {template['name']}")
            print(f"Description: {template['description']}")
            print(f"Created: {template['created_at']}")
            print("-" * 50)
            
    except Exception as e:
        print(f"Error listing templates: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("Adding Indian Wedding Template to database...")
    success = add_indian_wedding_template()
    
    if success:
        print("\n✅ Template added successfully!")
        list_all_templates()
    else:
        print("\n❌ Failed to add template")
