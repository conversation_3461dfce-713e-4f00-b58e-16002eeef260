"use client";
// 'use client';

// import { useEffect, useState } from 'react';
// import { useSearchParams } from 'next/navigation';
// import axios from 'axios';

// export default function RsvpResponsePage() {
//   const searchParams = useSearchParams();
//   const token = searchParams?.get('token');
//   const response = searchParams?.get('response');

//   const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
//   const [message, setMessage] = useState<string>('Processing your response...');
//   const [guestName, setGuestName] = useState<string>('');
//   const [responseType, setResponseType] = useState<string>('');

//   useEffect(() => {
//     const processResponse = async () => {
//       if (!token || !response) {
//         setStatus('error');
//         setMessage('Invalid invitation link. Missing token or response.');
//         return;
//       }

//       try {
//         // Call the backend API directly instead of using our local API
//         const result = await axios.get(
//           `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/track-invitation?token=${token}&response=${response}`
//         );

//         // Extract guest name and message from the HTML response
//         const htmlResponse = result.data;
//         let extractedName = '';
//         let extractedMessage = '';

//         // Simple parsing of the HTML response to extract information
//         if (typeof htmlResponse === 'string') {
//           // Extract guest name
//           const nameMatch = htmlResponse.match(/Guest:\s*([^<]+)<\/p>/i);
//           if (nameMatch && nameMatch[1]) {
//             extractedName = nameMatch[1].trim();
//           }

//           // Extract message
//           const messageMatch = htmlResponse.match(/<div class="message">([^<]+)<\/div>/i);
//           if (messageMatch && messageMatch[1]) {
//             extractedMessage = messageMatch[1].trim();
//           }
//         }

//         // Update state with the extracted data
//         setStatus('success');
//         setMessage(extractedMessage || 'Thank you for your response!');
//         setGuestName(extractedName || '');
//         setResponseType(response);
//       } catch (error) {
//         console.error('Error processing RSVP:', error);
//         setStatus('error');

//         // Try to extract error message from HTML response if available
//         if (axios.isAxiosError(error) && error.response?.data) {
//           const htmlError = error.response.data;
//           if (typeof htmlError === 'string') {
//             const errorMatch = htmlError.match(/<div class="message">([^<]+)<\/div>/i);
//             if (errorMatch && errorMatch[1]) {
//               setMessage(errorMatch[1].trim());
//               return;
//             }
//           }
//         }

//         setMessage('Failed to process your response. Please contact the couple directly.');
//       }
//     };

//     if (token && response) {
//       processResponse();
//     } else {
//       setStatus('error');
//       setMessage('Invalid invitation link. Missing token or response.');
//     }
//   }, [token, response]);

//   return (
//     <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
//       <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
//         <div className="text-center mb-6">
//           <h1 className="text-2xl font-bold text-gray-800">Wedding RSVP</h1>
//           {status === 'loading' && (
//             <div className="mt-4">
//               <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-700 mx-auto"></div>
//               <p className="mt-4 text-gray-600">{message}</p>
//             </div>
//           )}

//           {status === 'success' && (
//             <div className="mt-4">
//               <div className={`rounded-full h-16 w-16 flex items-center justify-center mx-auto ${
//                 responseType === 'attending' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
//               }`}>
//                 {responseType === 'attending' ? (
//                   <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
//                   </svg>
//                 ) : (
//                   <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                   </svg>
//                 )}
//               </div>

//               <h2 className="mt-4 text-xl font-semibold">
//                 {responseType === 'attending' ? 'You\'re attending!' : 'You\'ve declined'}
//               </h2>

//               <div className={`mt-4 p-3 rounded ${
//                 responseType === 'attending' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
//               }`}>
//                 {message}
//               </div>

//               {guestName && (
//                 <p className="mt-4 text-gray-600">
//                   Response recorded for: <span className="font-semibold">{guestName}</span>
//                 </p>
//               )}

//               <p className="mt-6 text-sm text-gray-500">
//                 If you need to change your response, please contact the couple directly.
//               </p>
//             </div>
//           )}

//           {status === 'error' && (
//             <div className="mt-4">
//               <div className="rounded-full h-16 w-16 flex items-center justify-center mx-auto bg-red-100 text-red-600">
//                 <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
//                 </svg>
//               </div>

//               <h2 className="mt-4 text-xl font-semibold text-red-600">Error</h2>
//               <p className="mt-2 text-gray-600">{message}</p>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

'use client';

import { useEffect, useState, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import axios from "axios";

// Create a client component that uses useSearchParams
function RsvpResponseContent() {
  const searchParams = useSearchParams();
  const token = searchParams?.get("token");
  const response = searchParams?.get("response");

  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState<string>("Processing your response...");
  const [guestName, setGuestName] = useState<string>("");
  const [responseType, setResponseType] = useState<string>("");

  useEffect(() => {
    // Only run in the browser
    if (typeof window === 'undefined') {
      return; // Don't execute during SSR
    }
    
    // Get parameters directly from URL instead of using hooks
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const response = urlParams.get('response');
    
    const processResponse = async () => {
      if (!token || !response) {
        setStatus("error");
        setMessage("Invalid invitation link. Missing token or response.");
        return;
      }


      try {
        // Call our API to process the response
        const result = await axios.post("/api/rsvp-response", {
          token,
          response,
        });

        // Update state with the response data
        setStatus("success");
        setMessage(result.data.message);
        setGuestName(result.data.guestName || "");
        setResponseType(result.data.response || response);
      } catch (error) {
        console.error("Error processing RSVP:", error);
        setStatus("error");
        if (axios.isAxiosError(error) && error.response?.data?.message) {
          setMessage(error.response.data.message);
        } else {
          setMessage(
            "Failed to process your response. Please contact the couple directly."
          );
        }
      }
    };


    if (token && response) {
      processResponse();
    } else {
      setStatus("error");
      setMessage("Invalid invitation link. Missing token or response.");
    }
  }, [token, response]);

  return (
    <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Wedding RSVP</h1>
        {status === "loading" && (
          <div className="mt-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-700 mx-auto"></div>
            <p className="mt-4 text-gray-600">{message}</p>
          </div>
        )}

        {status === "success" && (
          <div className="mt-4">
            <div
              className={`rounded-full h-16 w-16 flex items-center justify-center mx-auto ${
                responseType === "attending"
                  ? "bg-green-100 text-green-600"
                  : "bg-red-100 text-red-600"
              }`}
            >
              {responseType === "attending" ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              )}
            </div>

            <h2 className="mt-4 text-xl font-semibold">
              {responseType === "attending"
                ? "You're attending!"
                : "You've declined"}
            </h2>

            <div
              className={`mt-4 p-3 rounded ${
                responseType === "attending"
                  ? "bg-green-50 text-green-800"
                  : "bg-red-50 text-red-800"
              }`}
            >
              {message}
            </div>

            {guestName && (
              <p className="mt-4 text-gray-600">
                Response recorded for:{" "}
                <span className="font-semibold">{guestName}</span>
              </p>
            )}

            <p className="mt-6 text-sm text-gray-500">
              If you need to change your response, please contact the couple
              directly.
            </p>
          </div>
        )}

        {status === "error" && (
          <div className="mt-4">
            <div className="rounded-full h-16 w-16 flex items-center justify-center mx-auto bg-red-100 text-red-600">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>

            <h2 className="mt-4 text-xl font-semibold text-red-600">Error</h2>
            <p className="mt-2 text-gray-600">{message}</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Create a loading fallback component
function LoadingFallback() {
  return (
    <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Wedding RSVP</h1>
        <div className="mt-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-700 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading response page...</p>
        </div>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function RsvpResponsePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <Suspense fallback={<LoadingFallback />}>
        <RsvpResponseContent />
      </Suspense>
    </div>
  );
}