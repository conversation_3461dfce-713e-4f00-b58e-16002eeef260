/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7f0c2e771c3d0513b092fb3afac3f2d5bdc16428db\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__.invalidateCacheAction),\n/* harmony export */   \"7f9c875515fc1ea696b6367e4cde05e2f7b9f31040\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.createOrReadKeylessAction),\n/* harmony export */   \"7f9cdf2d5df654d5952abb8b624fdd122273b42576\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.syncKeylessConfigAction),\n/* harmony export */   \"7fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.deleteKeylessAction)\n/* harmony export */ });\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\final\\WEDZAT_\\frontend\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NpdmFzJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDZmluYWwlNUMlNUNXRURaQVRfJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SUFBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNpdmFzXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcZmluYWxcXFxcV0VEWkFUX1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d6ffbd6cb74b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNmZmYmQ2Y2I3NGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_GlobalUploadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/GlobalUploadManager */ \"(ssr)/./contexts/GlobalUploadManager.tsx\");\n/* harmony import */ var _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/LocationContext */ \"(ssr)/./contexts/LocationContext.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n// Metadata is now in metadata.ts\nfunction RootLayout({ children }) {\n    // Create a client\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        \"RootLayout.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 5 * 60 * 1000,\n                        gcTime: 30 * 60 * 1000,\n                        retry: 2,\n                        refetchOnWindowFocus: true\n                    }\n                }\n            })\n    }[\"RootLayout.useState\"]);\n    // Use client-side only rendering for the body content to avoid hydration mismatches\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"RootLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_8__.ClerkProvider, {\n        appearance: {\n            // Add custom appearance options to avoid hydration issues\n            variables: {\n                colorPrimary: \"#b31b1e\"\n            },\n            elements: {\n                // Add data-hydration-safe attributes to avoid hydration issues\n                rootBox: {\n                    attributes: {\n                        \"data-hydration-safe\": \"true\"\n                    }\n                },\n                card: {\n                    attributes: {\n                        \"data-hydration-safe\": \"true\"\n                    }\n                }\n            }\n        },\n        // Use the new redirect props format\n        redirectUrl: \"/auth/callback\",\n        signInUrl: \"/\",\n        signUpUrl: \"/\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        suppressHydrationWarning: true,\n                        children: [\n                            !mounted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col justify-center items-center h-screen\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600 mt-4\",\n                                        children: \"Loading your account...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this) : null,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: mounted ? \"block\" : \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.QueryClientProvider, {\n                                    client: queryClient,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LocationContext__WEBPACK_IMPORTED_MODULE_4__.LocationProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_GlobalUploadManager__WEBPACK_IMPORTED_MODULE_3__.GlobalUploadProvider, {\n                                                children: [\n                                                    children,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_12__.ReactQueryDevtools, {\n                                                        initialIsOpen: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"sync-token-to-cookie\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              function syncTokenToCookie() {\n                try {\n                  const token = localStorage.getItem('wedzat_token') || localStorage.getItem('token') || localStorage.getItem('jwt_token');\n                  if (token) {\n                    document.cookie = 'wedzat_token=' + token + '; path=/; max-age=86400; SameSite=Lax';\n                    console.log('Token synced to cookie for middleware');\n                  }\n\n                  // Also sync vendor flag\n                  const isVendor = localStorage.getItem('is_vendor');\n                  if (isVendor === 'true') {\n                    document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';\n                    console.log('Vendor flag synced to cookie for middleware');\n                  }\n                } catch (e) {\n                  console.error('Error syncing token to cookie:', e);\n                }\n              }\n\n              // Run on page load\n              syncTokenToCookie();\n\n              // Also run when localStorage changes\n              window.addEventListener('storage', syncTokenToCookie);\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"cleanup-extension-attrs\",\n                        strategy: \"beforeInteractive\",\n                        children: `\n              (function() {\n                // Run immediately to clean up before React hydration\n                function cleanupExtensionAttributes() {\n                  // Target common extension attributes\n                  const attributesToRemove = [\n                    'bis_skin_checked',\n                    '__processed_',\n                    'data-bis-'\n                  ];\n\n                  // Get all elements\n                  const allElements = document.querySelectorAll('*');\n\n                  // Remove attributes from each element\n                  allElements.forEach(el => {\n                    for (let i = 0; i < el.attributes.length; i++) {\n                      const attr = el.attributes[i];\n                      for (const badAttr of attributesToRemove) {\n                        if (attr.name.includes(badAttr)) {\n                          el.removeAttribute(attr.name);\n                          // Adjust index since we removed an attribute\n                          i--;\n                          break;\n                        }\n                      }\n                    }\n                  });\n                }\n\n                // Run immediately\n                cleanupExtensionAttributes();\n\n                // Also run after a short delay to catch any late additions\n                setTimeout(cleanupExtensionAttributes, 0);\n              })();\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"init-camera\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              // This script helps ensure the camera is never locked\n              // by creating a persistent camera stream\n              console.log('Camera initialization script loaded');\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        src: \"https://cdn.botpress.cloud/webchat/v2.3/inject.js\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        src: \"https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"botpress-helper\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              // Create a global function to open the Botpress webchat\n              window.openBotpressChat = function() {\n                // Check if the Botpress webchat is available\n                if (window.botpressWebChat) {\n                  // Try to send the show event\n                  try {\n                    window.botpressWebChat.sendEvent({ type: 'show' });\n                    console.log('Botpress webchat opened successfully');\n                  } catch (error) {\n                    console.error('Error opening Botpress webchat:', error);\n                    // Fallback: Try to click the webchat button\n                    try {\n                      const botpressButton = document.querySelector('.bp-widget-button');\n                      if (botpressButton) {\n                        botpressButton.click();\n                        console.log('Clicked Botpress button as fallback');\n                      } else {\n                        console.error('Could not find Botpress button');\n                      }\n                    } catch (fallbackError) {\n                      console.error('Error with fallback method:', fallbackError);\n                    }\n                  }\n                } else {\n                  console.error('Botpress webchat not initialized');\n                  // Set a flag to open the chat when it becomes available\n                  window.openBotpressChatWhenReady = true;\n\n                  // Check periodically if the webchat becomes available\n                  const checkInterval = setInterval(() => {\n                    if (window.botpressWebChat) {\n                      window.botpressWebChat.sendEvent({ type: 'show' });\n                      console.log('Botpress webchat opened after delay');\n                      clearInterval(checkInterval);\n                      window.openBotpressChatWhenReady = false;\n                    }\n                  }, 500);\n\n                  // Clear the interval after 10 seconds to avoid infinite checking\n                  setTimeout(() => clearInterval(checkInterval), 10000);\n                }\n              };\n\n              // Check if the webchat is loaded and if we need to open it\n              document.addEventListener('DOMContentLoaded', () => {\n                // Wait a bit for the webchat to initialize\n                setTimeout(() => {\n                  if (window.openBotpressChatWhenReady && window.botpressWebChat) {\n                    window.botpressWebChat.sendEvent({ type: 'show' });\n                    console.log('Botpress webchat opened on load');\n                    window.openBotpressChatWhenReady = false;\n                  }\n                }, 1000);\n              });\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api */ \"(ssr)/./services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isSignedIn, isLoaded: isClerkLoaded, user: clerkUser } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [authInitialized, setAuthInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Function to initialize authentication\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Check if we have a token (including vendor token)\n                        const token = localStorage.getItem(\"token\") || localStorage.getItem(\"jwt_token\");\n                        console.log(`Auth context initialization: Token ${token ? \"found\" : \"not found\"}`);\n                        console.log(`Clerk authentication: ${isClerkLoaded ? isSignedIn ? \"signed in\" : \"not signed in\" : \"loading\"}`);\n                        // Check if authenticated with Clerk\n                        if (isClerkLoaded && isSignedIn) {\n                            console.log(\"User is authenticated with Clerk\");\n                            setIsAuthenticated(true);\n                            // If we don't have a token but have Clerk auth, try to get one\n                            if (!token && clerkUser) {\n                                try {\n                                    // This assumes you have an endpoint to convert Clerk session to JWT\n                                    console.log(\"Getting token from Clerk session\");\n                                    const sessions = await clerkUser.getSessions();\n                                    const clerkToken = sessions[0]?.id;\n                                    if (clerkToken) {\n                                        // Exchange Clerk token for your JWT\n                                        // Extract user info to send along with the token\n                                        const userEmail = clerkUser?.primaryEmailAddress?.emailAddress || \"\";\n                                        const userName = clerkUser?.fullName || \"\";\n                                        const userId = clerkUser?.id || \"\";\n                                        console.log(\"Sending user info to backend:\", {\n                                            email: userEmail,\n                                            name: userName,\n                                            id: userId\n                                        });\n                                        const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.clerkAuth({\n                                            clerk_token: clerkToken,\n                                            user_type: \"customer\",\n                                            user_email: userEmail,\n                                            user_name: userName,\n                                            user_id: userId\n                                        });\n                                        if (response && response.token) {\n                                            localStorage.setItem(\"token\", response.token);\n                                            localStorage.setItem(\"wedzat_token\", response.token); // Also save as wedzat_token for consistency\n                                            console.log(\"Token obtained from Clerk session\");\n                                        } else {\n                                            // If backend auth fails, store the Clerk token as a fallback\n                                            console.log(\"No token received from backend, using Clerk token as fallback\");\n                                            // Use the token we already have\n                                            localStorage.setItem(\"token\", clerkToken);\n                                            localStorage.setItem(\"wedzat_token\", clerkToken);\n                                        }\n                                    }\n                                } catch (error) {\n                                    console.error(\"Error getting token from Clerk session:\", error);\n                                    // If there's an error with the backend, use the Clerk token as a fallback\n                                    // Get the token again if needed\n                                    try {\n                                        const sessions = await clerkUser.getSessions();\n                                        const fallbackToken = sessions[0]?.id;\n                                        if (fallbackToken) {\n                                            console.log(\"Using Clerk token as fallback due to backend error\");\n                                            localStorage.setItem(\"token\", fallbackToken);\n                                            localStorage.setItem(\"wedzat_token\", fallbackToken);\n                                        }\n                                    } catch (tokenError) {\n                                        console.error(\"Error getting fallback token:\", tokenError);\n                                    }\n                                }\n                            }\n                            setIsLoading(false);\n                            setAuthInitialized(true);\n                            return;\n                        }\n                        if (!token) {\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            setIsLoading(false);\n                            setAuthInitialized(true);\n                            // If we're on a protected route, redirect to login\n                            if (false) {}\n                            return;\n                        }\n                        // Attempt to get user profile\n                        try {\n                            console.log(\"Fetching user profile\");\n                            // Check if this is a vendor token\n                            const isVendorToken = localStorage.getItem(\"is_vendor\") === \"true\";\n                            if (isVendorToken) {\n                                console.log(\"Detected vendor token, setting authenticated state\");\n                                // For vendors, we don't need to fetch a profile, just set authenticated\n                                setIsAuthenticated(true);\n                                setUserProfile(null); // No regular user profile for vendors\n                            } else {\n                                // For regular users, fetch the profile\n                                const profile = await _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails();\n                                setUserProfile(profile);\n                                setIsAuthenticated(true);\n                            }\n                        } catch (error) {\n                            console.error(\"Error fetching user profile:\", error);\n                            // Check if this might be a vendor token\n                            try {\n                                // If we're on a vendor page, this might be a vendor token\n                                const pathname = window.location.pathname;\n                                if (pathname.includes(\"/vendor/\")) {\n                                    console.log(\"On vendor page with possible vendor token, setting authenticated\");\n                                    localStorage.setItem(\"is_vendor\", \"true\");\n                                    setIsAuthenticated(true);\n                                    setUserProfile(null);\n                                    return;\n                                }\n                            } catch (vendorCheckError) {\n                                console.error(\"Error checking for vendor token:\", vendorCheckError);\n                            }\n                            // Invalid or expired token\n                            _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            // Redirect to login if on a protected page\n                            if (false) {}\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        setIsAuthenticated(false);\n                        setUserProfile(null);\n                    } finally{\n                        setIsLoading(false);\n                        setAuthInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            // Only run initAuth when Clerk has loaded\n            if (isClerkLoaded) {\n                initAuth();\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isClerkLoaded,\n        isSignedIn,\n        clerkUser,\n        router\n    ]);\n    // Add a backup timeout to prevent infinite loading state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const loadingTimeoutId = setTimeout({\n                \"AuthProvider.useEffect.loadingTimeoutId\": ()=>{\n                    if (isLoading && !authInitialized) {\n                        console.log(\"Auth initialization timed out, resetting loading state\");\n                        setIsLoading(false);\n                        setAuthInitialized(true);\n                        // Check if we have a token before redirecting\n                        const token = localStorage.getItem(\"token\") || localStorage.getItem(\"jwt_token\") || localStorage.getItem(\"wedzat_token\");\n                        // Only redirect if no token is found\n                        if (!token && \"undefined\" !== \"undefined\") {}\n                    }\n                }\n            }[\"AuthProvider.useEffect.loadingTimeoutId\"], 10000); // Increased to 10 seconds for more time to initialize\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearTimeout(loadingTimeoutId)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isLoading,\n        authInitialized,\n        router\n    ]);\n    // Add listener for storage events to handle token removal in other tabs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"token\" || e.key === \"jwt_token\") {\n                        // If token was removed\n                        if (!e.newValue) {\n                            console.log(\"Token removed in another tab/window\");\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            // If on a protected route, redirect to login\n                            if (false) {}\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"AuthProvider.useEffect\": ()=>window.removeEventListener(\"storage\", handleStorageChange)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        router\n    ]);\n    const login = (token)=>{\n        console.log(\"Storing token and setting authenticated state\");\n        localStorage.setItem(\"token\", token);\n        localStorage.setItem(\"jwt_token\", token); // Store in both keys for compatibility\n        setIsAuthenticated(true);\n        // Fetch user profile after login\n        _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails().then((profile)=>{\n            console.log(\"User profile fetched:\", profile);\n            setUserProfile(profile);\n        }).catch((error)=>console.error(\"Error fetching user profile:\", error));\n    };\n    const logout = ()=>{\n        console.log(\"Logging out\");\n        _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        setIsAuthenticated(false);\n        setUserProfile(null);\n        router.push(\"/\");\n    };\n    const updateProfile = (profile)=>{\n        console.log(\"Updating user profile\", profile);\n        setUserProfile(profile);\n    };\n    // Refresh user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Only refresh if already authenticated\n            if (isAuthenticated && !isLoading) {\n                const refreshInterval = setInterval({\n                    \"AuthProvider.useEffect.refreshInterval\": ()=>{\n                        console.log(\"Refreshing user profile data\");\n                        _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails().then({\n                            \"AuthProvider.useEffect.refreshInterval\": (profile)=>{\n                                console.log(\"Refreshed user profile:\", profile);\n                                setUserProfile(profile);\n                            }\n                        }[\"AuthProvider.useEffect.refreshInterval\"]).catch({\n                            \"AuthProvider.useEffect.refreshInterval\": (error)=>{\n                                console.error(\"Error refreshing user profile:\", error);\n                            // Don't log out on profile refresh errors\n                            // This prevents unnecessary redirects\n                            }\n                        }[\"AuthProvider.useEffect.refreshInterval\"]);\n                    }\n                }[\"AuthProvider.useEffect.refreshInterval\"], 300000); // Refresh every 5 minutes instead of every minute\n                return ({\n                    \"AuthProvider.useEffect\": ()=>clearInterval(refreshInterval)\n                })[\"AuthProvider.useEffect\"];\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            userProfile,\n            isLoading,\n            login,\n            logout,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/GlobalUploadManager.tsx":
/*!******************************************!*\
  !*** ./contexts/GlobalUploadManager.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalUploadProvider: () => (/* binding */ GlobalUploadProvider),\n/* harmony export */   useGlobalUpload: () => (/* binding */ useGlobalUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// contexts/GlobalUploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ GlobalUploadProvider,useGlobalUpload auto */ \n\nconst GlobalUploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst GlobalUploadProvider = ({ children })=>{\n    const [isUploadModalOpen, setIsUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialType, setInitialType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [onUploadComplete, setOnUploadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const openUploadModal = (type, uploadCompleteCallback)=>{\n        setInitialType(type);\n        setOnUploadComplete(()=>uploadCompleteCallback);\n        setIsUploadModalOpen(true);\n    };\n    const closeUploadModal = ()=>{\n        setIsUploadModalOpen(false);\n        setInitialType(undefined);\n        setOnUploadComplete(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlobalUploadContext.Provider, {\n        value: {\n            isUploadModalOpen,\n            openUploadModal,\n            closeUploadModal\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\GlobalUploadManager.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\nconst useGlobalUpload = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GlobalUploadContext);\n    if (!context) {\n        throw new Error('useGlobalUpload must be used within a GlobalUploadProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/GlobalUploadManager.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LocationContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LocationContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationProvider: () => (/* binding */ LocationProvider),\n/* harmony export */   useLocation: () => (/* binding */ useLocation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LocationProvider,useLocation auto */ \n\nconst LocationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LocationProvider = ({ children })=>{\n    const [selectedLocation, setSelectedLocationState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [reloadKey, setReloadKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const setSelectedLocation = (location)=>{\n        setSelectedLocationState(location);\n        // Trigger homepage reload when location changes\n        setReloadKey((prev)=>prev + 1);\n    };\n    const reloadHomepage = ()=>{\n        setReloadKey((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LocationContext.Provider, {\n        value: {\n            selectedLocation,\n            setSelectedLocation,\n            reloadHomepage\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: children\n        }, reloadKey, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\LocationContext.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\LocationContext.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLocation = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LocationContext);\n    if (context === undefined) {\n        throw new Error('useLocation must be used within a LocationProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LocationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(ssr)/./app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NpdmFzJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDZmluYWwlNUMlNUNXRURaQVRfJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SUFBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNpdmFzXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcZmluYWxcXFxcV0VEWkFUX1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   uploadService: () => (/* binding */ uploadService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n// services/api.ts\n\n// Use environment variable or fallback to localhost for development\nconst BASE_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n// Log the API URL being used\n// console.log(`API Service using base URL: ${BASE_URL}`);\n// For development, use a CORS proxy if needed\nconst API_URL = BASE_URL;\nif (true) {\n    // Uncomment the line below to use a CORS proxy in development if needed\n    // API_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\n    // Log the API URL for debugging\n    console.log('Development mode detected, using API URL:', API_URL);\n}\n// Log the API URL being used\nconsole.log(`API Service using base URL: ${API_URL}`);\nconst TOKEN_KEY = 'token'; // Keep using your existing token key\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 60000,\n    withCredentials: false // Helps with CORS issues\n});\n// Helper to get token from storage (checking both keys)\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(TOKEN_KEY);\n};\n// Helper to save token to storage (using both keys for compatibility)\nconst saveToken = (token)=>{\n    if (true) return;\n    console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\n};\n// Request interceptor to add auth token to all requests\napi.interceptors.request.use((config)=>{\n    const token = getToken();\n    if (token && config.headers) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Authentication services\nconst authService = {\n    // Register a new user\n    signup: async (signupData)=>{\n        try {\n            console.log('Attempting signup with data:', {\n                ...signupData,\n                password: signupData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${BASE_URL}/signup`, signupData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Signup response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in signup response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error(\"Signup error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred during signup\"\n            };\n        }\n    },\n    // Register a new vendor\n    registerVendor: async (vendorData)=>{\n        try {\n            console.log('Attempting vendor registration with data:', {\n                ...vendorData,\n                password: vendorData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${BASE_URL}/register-vendor`, vendorData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Vendor registration response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in vendor registration response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error(\"Vendor registration error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred during vendor registration\"\n            };\n        }\n    },\n    // Get business types\n    getBusinessTypes: async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${BASE_URL}/business-types`);\n            return response.data.business_types;\n        } catch (error) {\n            console.error(\"Get business types error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred while fetching business types\"\n            };\n        }\n    },\n    // Get vendor profile\n    getVendorProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${BASE_URL}/vendor-profile`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data.profile;\n        } catch (error) {\n            console.error(\"Get vendor profile error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred while fetching vendor profile\"\n            };\n        }\n    },\n    // Login with email/mobile and password\n    // In services/api.ts - login function\n    login: async (credentials)=>{\n        try {\n            console.log('Attempting login with:', {\n                email: credentials.email,\n                mobile_number: credentials.mobile_number,\n                password: '********'\n            });\n            const response = await api.post('/login', credentials);\n            console.log('Login response received:', {\n                success: true,\n                hasToken: !!response.data.token,\n                tokenPreview: response.data.token ? `${response.data.token.substring(0, 10)}...` : 'none'\n            });\n            // Save token to localStorage with explicit console logs\n            if (response.data.token) {\n                console.log('Saving token to localStorage...');\n                localStorage.setItem('token', response.data.token);\n                // Verify token was saved\n                const savedToken = localStorage.getItem('token');\n                console.log(`Token verification: ${savedToken ? 'Successfully saved' : 'Failed to save'}`);\n            } else {\n                console.warn('No token received in login response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Login error:', error);\n            throw error.response?.data || {\n                error: 'Invalid credentials'\n            };\n        }\n    },\n    // Authenticate with Clerk\n    clerkAuth: async (clerkData)=>{\n        try {\n            console.log('Attempting Clerk authentication');\n            const response = await api.post('/clerk_auth', clerkData);\n            console.log('Clerk auth response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            // Save token to localStorage\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in Clerk auth response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Clerk authentication error:', error);\n            throw error.response?.data || {\n                error: 'Clerk authentication failed'\n            };\n        }\n    },\n    // Check if user profile is complete\n    checkProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Checking user profile');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${BASE_URL}/check-profile`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Profile check error:', error);\n            throw error.response?.data || {\n                error: 'Failed to check profile'\n            };\n        }\n    },\n    // Get the current token\n    getToken: ()=>{\n        return getToken();\n    },\n    // Check if the user is authenticated\n    isAuthenticated: ()=>{\n        return !!getToken();\n    },\n    // Logout - clear token from localStorage\n    logout: ()=>{\n        console.log('Logging out and removing tokens');\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n};\n// User services\nconst userService = {\n    // Get user details - uses GET method with explicit token in header\n    getUserDetails: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Fetching user details');\n            // Set the correct Authorization format (Bearer + token)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${BASE_URL}/user-details`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching user details:\", error);\n            throw error.response?.data || {\n                error: 'Failed to fetch user details'\n            };\n        }\n    },\n    // Update user details\n    updateUser: async (userData)=>{\n        try {\n            console.log('Updating user details');\n            const response = await api.put('/update-user', userData);\n            return response.data;\n        } catch (error) {\n            console.error('Update user error:', error);\n            throw error.response?.data || {\n                error: 'Failed to update user'\n            };\n        }\n    }\n};\n// Helper to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (true) {\n        return false; // For server-side rendering\n    }\n    const token = getToken();\n    return !!token; // Return true if token exists, false otherwise\n};\n// Upload services\nconst uploadService = {\n    /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */ verifyFace: async (faceImage)=>{\n        try {\n            console.log('Sending face verification request');\n            const token = getToken();\n            // Create the request payload\n            const payload = {\n                face_image: faceImage\n            };\n            const payloadSize = JSON.stringify(payload).length;\n            console.log('Request payload size:', payloadSize);\n            // Check if payload is too large\n            if (payloadSize > 1000000) {\n                console.warn('Warning: Payload is very large, which may cause issues with the API');\n            }\n            // No mock implementation - always use the real API\n            console.log('Using real face verification API');\n            // Make the real API call\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/verify-face`, payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 60000,\n                withCredentials: false\n            });\n            console.log('Face verification response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('Error verifying face:', error);\n            throw error.response?.data || {\n                error: 'Face verification failed'\n            };\n        }\n    },\n    /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */ getPresignedUrl: async (request)=>{\n        try {\n            console.log('Getting presigned URL with request:', request);\n            // Ensure we have a valid token\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when getting presigned URL');\n            }\n            // Make the API call with explicit headers\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/get-upload-url`, request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 30000 // 30 seconds timeout\n            });\n            console.log('Presigned URL response:', response.data);\n            // Validate the response\n            if (!response.data.media_id || !response.data.upload_urls || !response.data.upload_urls.main) {\n                throw new Error('Invalid response from get-upload-url API');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Error getting presigned URL:', error);\n            if (error.response?.status === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw error.response?.data || {\n                error: 'Failed to get upload URL'\n            };\n        }\n    },\n    /**\r\n   * Complete the upload process by notifying the backend\r\n   */ completeUpload: async (request)=>{\n        try {\n            console.log('Completing upload with request:', JSON.stringify(request, null, 2));\n            // Validate the request\n            if (!request.media_id) {\n                console.error('Missing media_id in completeUpload request');\n                throw new Error('Missing media_id in request');\n            }\n            const token = getToken();\n            let payload = {\n                ...request\n            };\n            if (request.media_type === 'video' && Array.isArray(request.vendor_details)) {\n                const vendorArr = request.vendor_details;\n                const vendorObj = {};\n                const keys = [\n                    'venue',\n                    'photographer',\n                    'makeup_artist',\n                    'decoration',\n                    'caterer'\n                ];\n                keys.forEach((k, i)=>{\n                    if (vendorArr[i]) {\n                        vendorObj[`${k}_name`] = vendorArr[i].name || '';\n                        vendorObj[`${k}_contact`] = vendorArr[i].phone || '';\n                    }\n                });\n                for(let i = 5; i < vendorArr.length; i++){\n                    vendorObj[`additional_vendor_${i - 4}_name`] = vendorArr[i].name || '';\n                    vendorObj[`additional_vendor_${i - 4}_contact`] = vendorArr[i].phone || '';\n                }\n                payload = {\n                    ...payload,\n                    vendor_details: vendorObj\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_URL}/complete-upload`, payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 60000\n            });\n            if (!response.data.message) {\n                throw new Error('Invalid response from complete-upload API');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Error completing upload:', error);\n            if (error.response?.status === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw error.response?.data || {\n                error: 'Failed to complete upload'\n            };\n        }\n    },\n    /**\r\n   * Upload a file to a presigned URL with optimized performance\r\n   */ uploadToPresignedUrl: async (url, file, onProgress)=>{\n        try {\n            console.log('Starting simple direct upload for file:', file.name);\n            console.log('File type:', file.type);\n            console.log('File size:', (file.size / (1024 * 1024)).toFixed(2) + ' MB');\n            console.log('Upload URL:', url);\n            // Report initial progress\n            if (onProgress) onProgress(10);\n            // Start timing the upload\n            const startTime = Date.now();\n            let lastProgressUpdate = 0;\n            // Use simple direct upload for all files\n            console.log('Using simple direct upload');\n            // Create simple headers for direct upload\n            const headers = {\n                'Content-Type': file.type,\n                'Content-Length': file.size.toString()\n            };\n            // Perform simple direct upload to the presigned URL\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, file, {\n                headers,\n                timeout: 3600000,\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                onUploadProgress: (progressEvent)=>{\n                    if (onProgress && progressEvent.total) {\n                        // Calculate raw percentage\n                        const rawPercent = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                        // Update UI in 5% increments for smoother progress\n                        if (rawPercent >= lastProgressUpdate + 5 || rawPercent === 100) {\n                            lastProgressUpdate = rawPercent;\n                            // Map to 10-95% range for UI\n                            const percentCompleted = 10 + Math.floor(rawPercent * 85 / 100);\n                            onProgress(percentCompleted);\n                            // Calculate and log upload speed\n                            const elapsedSeconds = (Date.now() - startTime) / 1000;\n                            if (elapsedSeconds > 0) {\n                                const speedMBps = (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2);\n                                console.log(`Upload progress: ${percentCompleted}% at ${speedMBps}MB/s`);\n                            }\n                        }\n                    }\n                }\n            });\n            // Calculate final stats\n            const endTime = Date.now();\n            const elapsedSeconds = (endTime - startTime) / 1000;\n            const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);\n            console.log(`Upload completed in ${elapsedSeconds.toFixed(2)}s at ${uploadSpeed}MB/s`);\n            console.log('Response status: 200 (success)');\n            // Report completion\n            if (onProgress) onProgress(100);\n            console.log('File uploaded successfully');\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            // Provide more detailed error information\n            let errorMessage = 'Failed to upload file';\n            if (error.message) {\n                if (error.message.includes('Network Error') || error.message.includes('CORS')) {\n                    errorMessage = 'Network error or CORS issue. Please try again or contact support.';\n                } else {\n                    errorMessage = `Upload error: ${error.message}`;\n                }\n            }\n            if (error.response) {\n                console.error('Response status:', error.response.status);\n                console.error('Response headers:', error.response.headers);\n                console.error('Response data:', error.response.data);\n                if (error.response.status === 403) {\n                    errorMessage = 'Permission denied. The upload URL may have expired.';\n                }\n            }\n            throw {\n                error: errorMessage\n            };\n        }\n    },\n    /**\r\n   * Handle the complete upload process\r\n   */ handleUpload: async (file, mediaType, category, description, tags, details, duration, thumbnail, onProgress)=>{\n        try {\n            console.log('API SERVICE - handleUpload called with params:', {\n                fileName: file?.name,\n                fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\n                mediaType,\n                category,\n                description,\n                tagsCount: tags?.length,\n                detailsCount: details ? Object.keys(details).length : 0,\n                videoCategory: details?.video_category || 'Not set',\n                duration,\n                hasThumbnail: !!thumbnail\n            });\n            // Log the video_category from details\n            if (mediaType === 'video') {\n                console.log('API SERVICE - Video category from details:', details?.video_category);\n                console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\n            }\n            if (!file) {\n                throw new Error('No file provided');\n            }\n            // Log the file size and selected category without overriding the user's selection\n            if (mediaType === 'video') {\n                const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[category] || 250;\n                console.log(`API SERVICE - File size: ${fileSizeMB} MB, Selected category: ${category}`);\n                console.log(`API SERVICE - Size limit for ${category}: ${sizeLimit} MB`);\n            // Warning is now handled in the presignedUrlRequest section\n            }\n            console.log('API SERVICE - Starting upload process for:', file.name);\n            // Update progress to 10%\n            if (onProgress) onProgress(10);\n            // Step 1: Get presigned URLs\n            console.log('API SERVICE - Creating presigned URL request with category:', category);\n            // Ensure we're using the correct backend category names\n            // Frontend: flashes, glimpses, movies, moments\n            // Backend: flash, glimpse, movie, story\n            let backendCategory = mediaType === 'photo' ? category === 'story' ? 'story' : 'post' // For photos: either 'story' or 'post'\n             : category; // For videos: keep the original category\n            // Double-check that we have a valid category\n            const validCategories = [\n                'story',\n                'flash',\n                'glimpse',\n                'movie',\n                'post'\n            ];\n            if (!validCategories.includes(backendCategory)) {\n                console.log(`API SERVICE - WARNING: Invalid category '${backendCategory}'. Using 'flash' as fallback.`);\n                console.log(`API SERVICE - Valid categories are: ${validCategories.join(', ')}`);\n                console.log(`API SERVICE - Category type: ${typeof backendCategory}`);\n                console.log(`API SERVICE - Category value: '${backendCategory}'`);\n                // Use 'flash' as the default for videos instead of 'glimpse'\n                backendCategory = 'flash';\n            }\n            console.log(`API SERVICE - Original category from context: ${category}`);\n            console.log(`API SERVICE - Using backend category: ${backendCategory}`);\n            // Log the file size to help with debugging\n            const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n            console.log(`API SERVICE - File size: ${fileSizeMB} MB`);\n            // Log the category limits\n            const categoryLimits = {\n                'story': 50,\n                'flash': 100,\n                'glimpse': 250,\n                'movie': 2000\n            };\n            const sizeLimit = categoryLimits[backendCategory] || 250;\n            console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB`);\n            // Log a warning if the file size exceeds the limit\n            if (fileSizeMB > sizeLimit) {\n                console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\n                console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\n            }\n            // When constructing presignedUrlRequest for photo, set media_subtype to null\n            const presignedUrlRequest = {\n                media_type: mediaType,\n                media_subtype: mediaType === 'photo' ? null : backendCategory,\n                filename: file.name,\n                content_type: file.type,\n                file_size: file.size\n            };\n            // Log the presigned URL request\n            console.log(`API SERVICE - Sending presigned URL request with media_subtype: ${backendCategory}`);\n            // Add video_category for videos\n            if (mediaType === 'video') {\n                // Get the video_category from details - no default value\n                console.log('API SERVICE - Details object:', details);\n                console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\n                const videoCategory = details?.video_category;\n                console.log('API SERVICE - Video category from details:', videoCategory);\n                // Make sure we're using a valid video_category\n                const allowedCategories = [\n                    'my_wedding',\n                    'wedding_influencer',\n                    'friends_family_video'\n                ];\n                // If no video_category is provided, use a default one\n                if (!videoCategory) {\n                    console.error(`API SERVICE - Missing video_category. Using default 'my_wedding'.`);\n                    // Use 'my_wedding' as the default video_category\n                    presignedUrlRequest.video_category = 'my_wedding';\n                    console.log('API SERVICE - Using default video_category: my_wedding');\n                } else {\n                    // Map the UI category to the backend category if needed\n                    let backendVideoCategory = videoCategory;\n                    // If the category is in UI format, map it to backend format\n                    if (videoCategory === 'friends_family_videos') {\n                        backendVideoCategory = 'friends_family_video';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    } else if (videoCategory === 'my_wedding_videos') {\n                        backendVideoCategory = 'my_wedding';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    }\n                    // If the video_category is not allowed, throw an error\n                    if (!allowedCategories.includes(backendVideoCategory)) {\n                        console.error(`API SERVICE - Invalid video_category: ${backendVideoCategory}. Must be one of ${JSON.stringify(allowedCategories)}`);\n                        throw new Error(`Invalid video category. Must be one of ${JSON.stringify(allowedCategories)}`);\n                    }\n                    // Use the provided video_category\n                    presignedUrlRequest.video_category = backendVideoCategory;\n                    console.log('API SERVICE - Using video_category:', backendVideoCategory);\n                    console.log(`API SERVICE - Final video category: ${backendVideoCategory}`);\n                    console.log(`API SERVICE - Complete presigned URL request:`, JSON.stringify(presignedUrlRequest, null, 2));\n                }\n                // Log the category and file size limits\n                console.log(`API SERVICE - Original category from UI: ${category}`);\n                console.log(`API SERVICE - Using backend media subtype: ${backendCategory}`);\n                // Log size limits based on category without overriding the user's selection\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[backendCategory] || 250;\n                console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB (file size: ${fileSizeMB} MB)`);\n                // Log a warning if the file size exceeds the limit for the selected category\n                if (fileSizeMB > sizeLimit) {\n                    console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\n                    console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\n                }\n                // Add duration if available\n                if (duration) {\n                    console.log(`Video duration: ${duration} seconds`);\n                // You could add this to the request if the API supports it\n                // presignedUrlRequest.duration = duration;\n                }\n            }\n            const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\n            // Update progress to 20%\n            if (onProgress) onProgress(20);\n            // Step 2: Upload the file to the presigned URL (20-70% of progress)\n            await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.main, file, (uploadProgress)=>{\n                // Map the upload progress from 0-100 to 20-70 in our overall progress\n                if (onProgress) onProgress(20 + uploadProgress * 0.5);\n            });\n            // Step 3: Upload thumbnail if available (70-90% of progress)\n            if (thumbnail && presignedUrlResponse.upload_urls.thumbnail) {\n                console.log('Uploading thumbnail:', thumbnail.name);\n                // Update progress to 70%\n                if (onProgress) onProgress(70);\n                await uploadService.uploadToPresignedUrl(presignedUrlResponse.upload_urls.thumbnail, thumbnail, (uploadProgress)=>{\n                    // Map the upload progress from 0-100 to 70-90 in our overall progress\n                    if (onProgress) onProgress(70 + uploadProgress * 0.2);\n                });\n            }\n            // Update progress to 90%\n            if (onProgress) onProgress(90);\n            // Step 4: Complete the upload (90-100% of progress)\n            try {\n                // When constructing completeRequest for photo, do not include title\n                const completeRequest = {\n                    media_id: presignedUrlResponse.media_id,\n                    media_type: mediaType,\n                    media_subtype: mediaType === 'photo' ? null : backendCategory,\n                    description: description || '',\n                    tags: tags || []\n                };\n                console.log(`API SERVICE - Setting media_subtype in completeRequest: ${backendCategory}`);\n                // Log the description being sent\n                console.log('Sending description:', description || '');\n                // Add duration for videos if available\n                if (mediaType === 'video' && duration) {\n                    completeRequest.duration = duration;\n                }\n                // Extract personal details from the details object\n                const personalDetails = {\n                    caption: details?.caption || '',\n                    life_partner: details?.personal_life_partner || details?.lifePartner || '',\n                    wedding_style: details?.personal_wedding_style || description || '',\n                    place: details?.personal_place || details?.location || ''\n                };\n                // Extract vendor details from the details object\n                const vendorDetails = {};\n                // Process vendor details from the details object\n                if (details) {\n                    console.log('API SERVICE - Processing vendor details from details object');\n                    console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\n                    // Count vendor fields in the details object\n                    let vendorFieldCount = 0;\n                    Object.keys(details).forEach((key)=>{\n                        if (key.startsWith('vendor_')) {\n                            vendorFieldCount++;\n                        }\n                    });\n                    console.log(`API SERVICE - Found ${vendorFieldCount} vendor-related fields in details object`);\n                    // Keep track of which vendor types we've already processed\n                    const processedVendors = new Set();\n                    Object.entries(details).forEach(([key, value])=>{\n                        if (key.startsWith('vendor_') && value) {\n                            const parts = key.split('_');\n                            if (parts.length >= 3) {\n                                const vendorType = parts[1];\n                                const fieldType = parts.slice(2).join('_');\n                                // Normalize vendor type to avoid duplicates\n                                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                console.log(`API SERVICE - Processing vendor field: ${key} = ${value}`, {\n                                    vendorType,\n                                    fieldType,\n                                    normalizedType,\n                                    alreadyProcessed: processedVendors.has(normalizedType)\n                                });\n                                // Skip if we've already processed this vendor type\n                                if (processedVendors.has(normalizedType)) {\n                                    console.log(`API SERVICE - Skipping ${key} as ${normalizedType} is already processed`);\n                                    return;\n                                }\n                                if (fieldType === 'name') {\n                                    vendorDetails[`${normalizedType}_name`] = value;\n                                    console.log(`API SERVICE - Set ${normalizedType}_name = ${value}`);\n                                    // Also set the contact if available\n                                    const contactKey = `vendor_${vendorType}_contact`;\n                                    if (details[contactKey]) {\n                                        vendorDetails[`${normalizedType}_contact`] = details[contactKey];\n                                        console.log(`API SERVICE - Also set ${normalizedType}_contact = ${details[contactKey]}`);\n                                        processedVendors.add(normalizedType);\n                                        console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\n                                    }\n                                } else if (fieldType === 'contact') {\n                                    vendorDetails[`${normalizedType}_contact`] = value;\n                                    console.log(`API SERVICE - Set ${normalizedType}_contact = ${value}`);\n                                    // Also set the name if available\n                                    const nameKey = `vendor_${vendorType}_name`;\n                                    if (details[nameKey]) {\n                                        vendorDetails[`${normalizedType}_name`] = details[nameKey];\n                                        console.log(`API SERVICE - Also set ${normalizedType}_name = ${details[nameKey]}`);\n                                        processedVendors.add(normalizedType);\n                                        console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\n                                    }\n                                }\n                            }\n                        }\n                    });\n                    // Log the processed vendor details\n                    // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\n                    // Final check to ensure we have both name and contact for each vendor\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    Object.keys(vendorDetails).forEach((key)=>{\n                        if (key.endsWith('_name')) {\n                            vendorNames.add(key.replace('_name', ''));\n                        } else if (key.endsWith('_contact')) {\n                            vendorContacts.add(key.replace('_contact', ''));\n                        }\n                    });\n                    // Count complete pairs\n                    vendorNames.forEach((name)=>{\n                        if (vendorContacts.has(name)) {\n                            completeVendorCount++;\n                        } else {\n                            // If we have a name but no contact, add a default contact\n                            console.log(`API SERVICE - WARNING: Vendor ${name} has name but no contact, adding default contact`);\n                            vendorDetails[`${name}_contact`] = '0000000000';\n                            completeVendorCount++;\n                        }\n                    });\n                    // Check for contacts without names\n                    vendorContacts.forEach((contact)=>{\n                        if (!vendorNames.has(contact)) {\n                            // If we have a contact but no name, add a default name\n                            console.log(`API SERVICE - WARNING: Vendor ${contact} has contact but no name, adding default name`);\n                            if (typeof contact === 'string') {\n                                vendorDetails[`${contact}_name`] = `Vendor ${contact.charAt(0).toUpperCase() + contact.slice(1)}`;\n                            }\n                            completeVendorCount++;\n                        }\n                    });\n                    console.log(`API SERVICE - Final check: Found ${completeVendorCount} complete vendor pairs`);\n                    console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\n                }\n                // Ensure we have the required vendor fields for wedding videos\n                if (mediaType === 'video' && details?.video_category && [\n                    'my_wedding',\n                    'wedding_influencer',\n                    'friends_family_video'\n                ].includes(details.video_category)) {\n                    // Try to get vendor details from localStorage if they're missing from the details object\n                    const initialVendorKeys = Object.keys(vendorDetails);\n                    const vendorCount = initialVendorKeys.filter((key)=>key.endsWith('_name')).length;\n                    console.log(`API SERVICE - Initial vendor count: ${vendorCount} name fields found`);\n                    console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // Always check localStorage for vendor details to ensure we have the most complete set\n                    console.log('API SERVICE - Checking localStorage for vendor details');\n                    try {\n                        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                        if (storedVendorDetails) {\n                            const parsedVendorDetails = JSON.parse(storedVendorDetails);\n                            console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\n                            // Track how many complete vendor details we've added\n                            let completeVendorCount = 0;\n                            // Process vendor details from localStorage\n                            Object.entries(parsedVendorDetails).forEach(([vendorType, details])=>{\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to vendorDetails\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    vendorDetails[`${normalizedType}_name`] = details.name;\n                                    vendorDetails[`${normalizedType}_contact`] = details.mobileNumber;\n                                    console.log(`API SERVICE - Added vendor ${normalizedType} with name: ${details.name} and contact: ${details.mobileNumber}`);\n                                    completeVendorCount++;\n                                    // Also add the original type if it's different\n                                    if (normalizedType !== vendorType) {\n                                        vendorDetails[`${vendorType}_name`] = details.name;\n                                        vendorDetails[`${vendorType}_contact`] = details.mobileNumber;\n                                        console.log(`API SERVICE - Also added original vendor ${vendorType}`);\n                                    }\n                                }\n                            });\n                            console.log(`API SERVICE - Added ${completeVendorCount} complete vendor details from localStorage`);\n                            console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\n                            // Force update the state with these vendor details\n                            try {\n                                // This is a direct state update to ensure the vendor details are available for validation\n                                if (window && window.dispatchEvent) {\n                                    const vendorUpdateEvent = new CustomEvent('vendor-details-update', {\n                                        detail: parsedVendorDetails\n                                    });\n                                    window.dispatchEvent(vendorUpdateEvent);\n                                    console.log('API SERVICE - Dispatched vendor-details-update event');\n                                }\n                            } catch (eventError) {\n                                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\n                            }\n                        } else {\n                            console.log('API SERVICE - No vendor details found in localStorage');\n                        }\n                    } catch (error) {\n                        console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\n                    }\n                    // Make sure we have at least 4 vendor details with both name and contact\n                    // Define required vendor types for fallback if needed\n                    const requiredVendorTypes = [\n                        'venue',\n                        'photographer',\n                        'makeup_artist',\n                        'decoration',\n                        'caterer'\n                    ];\n                    // Count all vendor details, including additional ones\n                    let validVendorCount = 0;\n                    // Count all vendor details where both name and contact are provided\n                    console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\n                    const allVendorKeys = Object.keys(vendorDetails);\n                    console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\n                    // Keep track of which vendors we've already counted to avoid duplicates\n                    const countedVendors = new Set();\n                    // First pass: Check for standard vendor types\n                    for(let i = 0; i < allVendorKeys.length; i++){\n                        const key = allVendorKeys[i];\n                        if (key.endsWith('_name')) {\n                            const baseKey = key.replace('_name', '');\n                            const contactKey = `${baseKey}_contact`;\n                            // Normalize the key to handle both frontend and backend naming\n                            const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey === 'decorations' ? 'decoration' : baseKey;\n                            // Skip if we've already counted this vendor\n                            if (countedVendors.has(normalizedKey)) {\n                                continue;\n                            }\n                            console.log(`Checking vendor ${baseKey}:`, {\n                                name: vendorDetails[key],\n                                contact: vendorDetails[contactKey]\n                            });\n                            if (vendorDetails[key] && vendorDetails[contactKey]) {\n                                validVendorCount++;\n                                countedVendors.add(normalizedKey);\n                                console.log(`Valid vendor found: ${baseKey} with name: ${vendorDetails[key]} and contact: ${vendorDetails[contactKey]}`);\n                            }\n                        }\n                    }\n                    console.log(`Total valid vendor count after first pass: ${validVendorCount}`);\n                    // Second pass: Check for additional vendors with different naming patterns\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = `additional${i}_name`;\n                        const contactKey = `additional${i}_contact`;\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(`additional${i}`)) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(`additional${i}`);\n                            console.log(`Valid additional vendor found: additional${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\n                        }\n                    }\n                    // Third pass: Check for additionalVendor pattern (used in some parts of the code)\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = `additionalVendor${i}_name`;\n                        const contactKey = `additionalVendor${i}_contact`;\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(`additionalVendor${i}`)) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(`additionalVendor${i}`);\n                            console.log(`Valid additionalVendor found: additionalVendor${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\n                        }\n                    }\n                    console.log(`Total valid vendor count after all passes: ${validVendorCount}`);\n                    // Map additional vendors to the predefined vendor types if needed\n                    // This ensures the backend will count them correctly\n                    if (validVendorCount < 4) {\n                        console.log('Need to map additional vendors to predefined types');\n                        // First, collect all additional vendors from all patterns\n                        const additionalVendors = [];\n                        // Collect all additional vendors with 'additional' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = `additional${i}_name`;\n                            const contactKey = `additional${i}_contact`;\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(`additional${i}`)) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: `additional${i}`\n                                });\n                                countedVendors.add(`additional${i}`);\n                                console.log(`Found additional vendor ${i}: ${vendorDetails[nameKey]}`);\n                            }\n                        }\n                        // Collect all additional vendors with 'additionalVendor' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = `additionalVendor${i}_name`;\n                            const contactKey = `additionalVendor${i}_contact`;\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(`additionalVendor${i}`)) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: `additionalVendor${i}`\n                                });\n                                countedVendors.add(`additionalVendor${i}`);\n                                console.log(`Found additionalVendor ${i}: ${vendorDetails[nameKey]}`);\n                            }\n                        }\n                        console.log(`Found ${additionalVendors.length} additional vendors to map`);\n                        // Map additional vendors to empty predefined vendor slots\n                        let additionalIndex = 0;\n                        for (const type of requiredVendorTypes){\n                            // Check if this type is already filled\n                            const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                            // Skip if we've already counted this vendor type\n                            if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                console.log(`Skipping ${type} as it's already counted`);\n                                continue;\n                            }\n                            if (additionalIndex < additionalVendors.length) {\n                                // Map this additional vendor to this predefined type\n                                vendorDetails[`${type}_name`] = additionalVendors[additionalIndex].name;\n                                vendorDetails[`${type}_contact`] = additionalVendors[additionalIndex].contact;\n                                console.log(`Mapped additional vendor ${additionalVendors[additionalIndex].key} to ${type}: ${additionalVendors[additionalIndex].name}`);\n                                additionalIndex++;\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                if (validVendorCount >= 4) {\n                                    console.log(`Reached 4 valid vendors after mapping, no need to continue`);\n                                    break;\n                                }\n                            }\n                        }\n                        // If we still don't have enough, add placeholders\n                        if (validVendorCount < 4) {\n                            console.log('Still need more vendors, adding placeholders');\n                            for (const type of requiredVendorTypes){\n                                // Check if this type is already filled\n                                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                                // Skip if we've already counted this vendor type\n                                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                    console.log(`Skipping ${type} for placeholder as it's already counted`);\n                                    continue;\n                                }\n                                // Add placeholder for this vendor type\n                                vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                                vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                console.log(`Added placeholder for ${type}: ${vendorDetails[`${type}_name`]}`);\n                                if (validVendorCount >= 4) {\n                                    console.log(`Reached 4 valid vendors after adding placeholders`);\n                                    break;\n                                }\n                            }\n                            // Final check - if we still don't have 4, force add the remaining required types\n                            if (validVendorCount < 4) {\n                                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\n                                for (const type of requiredVendorTypes){\n                                    if (!vendorDetails[`${type}_name`] || !vendorDetails[`${type}_contact`]) {\n                                        vendorDetails[`${type}_name`] = `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                                        vendorDetails[`${type}_contact`] = '0000000000';\n                                        validVendorCount++;\n                                        console.log(`Force added placeholder for ${type}`);\n                                        if (validVendorCount >= 4) break;\n                                    }\n                                }\n                            }\n                        }\n                        // Final log of vendor count\n                        console.log(`Final valid vendor count: ${validVendorCount}`);\n                    }\n                }\n                // Add the details to the request\n                completeRequest.personal_details = personalDetails;\n                completeRequest.vendor_details = vendorDetails;\n                // Final check before sending - ensure we have at least 4 complete vendor details\n                if (mediaType === 'video') {\n                    // Count complete vendor details (with both name and contact)\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    if (vendorDetails) {\n                        Object.keys(vendorDetails).forEach((key)=>{\n                            if (key.endsWith('_name')) {\n                                vendorNames.add(key.replace('_name', ''));\n                            } else if (key.endsWith('_contact')) {\n                                vendorContacts.add(key.replace('_contact', ''));\n                            }\n                        });\n                        // Count complete pairs\n                        vendorNames.forEach((name)=>{\n                            if (vendorContacts.has(name)) {\n                                completeVendorCount++;\n                            }\n                        });\n                    }\n                    console.log(`API SERVICE - FINAL CHECK: Found ${completeVendorCount} complete vendor pairs before sending request`);\n                    // If we don't have enough vendors, add placeholders to reach 4\n                    if (completeVendorCount < 4) {\n                        console.log(`API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors`);\n                        const requiredVendorTypes = [\n                            'venue',\n                            'photographer',\n                            'makeup_artist',\n                            'decoration',\n                            'caterer'\n                        ];\n                        for (const type of requiredVendorTypes){\n                            // Skip if this vendor is already complete\n                            if (vendorDetails[`${type}_name`] && vendorDetails[`${type}_contact`]) {\n                                continue;\n                            }\n                            // Add placeholder for this vendor\n                            vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                            vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\n                            completeVendorCount++;\n                            console.log(`API SERVICE - FINAL CHECK: Added placeholder for ${type}`);\n                            if (completeVendorCount >= 4) {\n                                break;\n                            }\n                        }\n                        // Update the request with the new vendor details\n                        completeRequest.vendor_details = vendorDetails;\n                        console.log(`API SERVICE - FINAL CHECK: Now have ${completeVendorCount} complete vendor pairs`);\n                    }\n                }\n                console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\n                // Complete the upload\n                const completeResponse = await uploadService.completeUpload(completeRequest);\n                // Log the complete response\n                console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\n                // Update progress to 100%\n                if (onProgress) onProgress(100);\n                console.log('Upload process completed successfully');\n                return completeResponse;\n            } catch (completeError) {\n                console.error('Error in complete upload step:', completeError);\n                // Handle other errors\n                const errorMessage = completeError.response?.data?.error || completeError.message || 'Failed to complete the upload process';\n                console.error('Throwing error:', errorMessage);\n                throw {\n                    error: errorMessage\n                };\n            }\n        } catch (error) {\n            console.error('Error in upload process:', error);\n            // If error is already formatted correctly, just pass it through\n            if (error.error) {\n                throw error;\n            }\n            // Otherwise, format the error\n            throw error.response?.data || {\n                error: 'Upload process failed'\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    authService,\n    userService,\n    uploadService,\n    isAuthenticated\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/swr","vendor-chunks/tslib","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/cookie","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/dequal","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/@swc","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/es-set-tostringtag","vendor-chunks/lower-case","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/snakecase-keys","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();