import React from 'react';

interface IndianTraditionalTemplateProps {
  data: {
    partner1_name?: string;
    partner2_name?: string;
    wedding_date?: string;
    wedding_venue?: string;
    muhurtam_time?: string;
    reception_date?: string;
    reception_venue?: string;
    reception_time?: string;
    couple_photo?: string;
    custom_message?: string;
    colors?: {
      primary?: string;
      secondary?: string;
      background?: string;
      text?: string;
      accent?: string;
    };
    fonts?: {
      heading?: string | string[];
      body?: string | string[];
      decorative?: string | string[];
    };
  };
}

const IndianTraditionalTemplate: React.FC<IndianTraditionalTemplateProps> = ({ data }) => {
  // Helper function to handle font values (can be string or array)
  const getFontFamily = (fontValue: string | string[] | undefined, fallback: string): string => {
    if (!fontValue) return fallback;
    if (Array.isArray(fontValue)) return fontValue.join(', ');
    return fontValue;
  };

  const {
    partner1_name = "Navneet",
    partner2_name = "Suknya",
    wedding_date = "Monday, 22th Aug 2022",
    wedding_venue = "Unitech Unihomes Plots Sec -Mu Greater Noida",
    muhurtam_time = "7:00 Pm",
    reception_date = "Monday, 23th Aug 2022",
    reception_venue = "Unitech Unihomes Plots Sec -Mu Greater Noida",
    reception_time = "10:00 To 11:00 Am",
    couple_photo = "",
    custom_message = "May your love story be as magical and charming as in fairy tales!",
    colors = {
      primary: '#B31B1E',
      secondary: '#FFD700',
      background: '#FFFFFF',
      text: '#000000',
      accent: '#FF8C00'
    },
    fonts = {
      heading: ['Playfair Display', 'Georgia', 'serif'],
      body: ['Roboto', 'Arial', 'sans-serif'],
      decorative: ['Dancing Script', 'cursive']
    }
  } = data;

  const styles = {
    container: {
      maxWidth: '600px',
      width: '100%',
      background: colors.background,
      borderRadius: '20px',
      overflow: 'hidden',
      boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
      position: 'relative' as const,
      margin: '0 auto',
      fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')
    },
    headerDecoration: {
      background: `linear-gradient(90deg, ${colors.secondary}, ${colors.accent}, ${colors.secondary})`,
      height: '8px',
      width: '100%'
    },
    marigoldBorder: {
      background: `url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='${encodeURIComponent(colors.secondary || '#FFD700')}'/><circle cx='30' cy='10' r='8' fill='${encodeURIComponent(colors.accent || '#FF8C00')}'/><circle cx='50' cy='10' r='8' fill='${encodeURIComponent(colors.secondary || '#FFD700')}'/><circle cx='70' cy='10' r='8' fill='${encodeURIComponent(colors.accent || '#FF8C00')}'/><circle cx='90' cy='10' r='8' fill='${encodeURIComponent(colors.secondary || '#FFD700')}'/></svg>") repeat-x`,
      height: '20px',
      width: '100%'
    },
    ganeshSection: {
      textAlign: 'center' as const,
      padding: '30px 20px 20px',
      background: `linear-gradient(180deg, ${colors.background} 0%, #FFF8E7 100%)`
    },
    ganeshSymbol: {
      fontSize: '48px',
      color: colors.primary,
      marginBottom: '10px'
    },
    ganeshText: {
      fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),
      fontSize: '24px',
      color: colors.primary,
      fontWeight: '700',
      marginBottom: '20px'
    },
    coupleNames: {
      fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),
      fontSize: '42px',
      fontWeight: '700',
      color: colors.primary,
      textAlign: 'center' as const,
      margin: '20px 0',
      textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
    },
    andSymbol: {
      fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),
      fontSize: '32px',
      color: colors.secondary,
      margin: '0 15px'
    },
    invitationText: {
      textAlign: 'center' as const,
      padding: '20px 30px',
      fontSize: '18px',
      color: colors.text,
      lineHeight: '1.6'
    },
    eventDetails: {
      background: `linear-gradient(135deg, ${colors.primary} 0%, #8B0000 100%)`,
      color: 'white',
      padding: '30px',
      textAlign: 'center' as const
    },
    eventTitle: {
      fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),
      fontSize: '28px',
      fontWeight: '700',
      marginBottom: '15px',
      color: colors.secondary
    },
    eventInfo: {
      margin: '15px 0',
      fontSize: '16px'
    },
    eventDate: {
      fontSize: '24px',
      fontWeight: '500',
      margin: '20px 0',
      color: colors.secondary
    },
    decorativeDivider: {
      width: '100px',
      height: '3px',
      background: `linear-gradient(90deg, ${colors.secondary}, ${colors.accent}, ${colors.secondary})`,
      margin: '20px auto',
      borderRadius: '2px'
    },
    couplePhoto: {
      textAlign: 'center' as const,
      padding: '30px',
      background: '#FFF8E7'
    },
    photoPlaceholder: {
      width: '200px',
      height: '200px',
      borderRadius: '50%',
      background: `linear-gradient(135deg, ${colors.secondary}, ${colors.accent})`,
      margin: '0 auto 20px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '18px',
      color: colors.primary,
      fontWeight: '500',
      border: `5px solid ${colors.primary}`,
      backgroundImage: couple_photo ? `url(${couple_photo})` : 'none',
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    },
    blessingText: {
      textAlign: 'center' as const,
      padding: '20px',
      fontStyle: 'italic',
      color: '#666',
      fontSize: '14px'
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.headerDecoration}></div>
      <div style={styles.marigoldBorder}></div>
      
      <div style={styles.ganeshSection}>
        <div style={styles.ganeshSymbol}>🕉️</div>
        <div style={styles.ganeshText}>|| Shree Ganesh Namah ||</div>
      </div>
      
      <div style={styles.coupleNames}>
        {partner1_name}<span style={styles.andSymbol}>&</span>{partner2_name}
      </div>
      
      <div style={styles.invitationText}>
        We Invite You to Share in our Joy And Request<br />
        Your Presence At the Reception
      </div>
      
      <div style={styles.eventDetails}>
        <div style={styles.eventTitle}>✦ Muhurtam ✦</div>
        <div style={styles.eventInfo}>Time {muhurtam_time}</div>
        <div style={styles.eventInfo}>{wedding_venue}</div>
        
        <div style={styles.decorativeDivider}></div>
        
        <div style={styles.eventDate}>On {wedding_date}</div>
        
        <div style={styles.decorativeDivider}></div>
        
        <div style={styles.eventTitle}>✦ Reception ✦</div>
        <div style={styles.eventInfo}>Time {reception_time}</div>
        <div style={styles.eventInfo}>{reception_venue}</div>
        
        <div style={styles.eventDate}>On {reception_date}</div>
      </div>
      
      <div style={styles.couplePhoto}>
        <div style={styles.photoPlaceholder}>
          {!couple_photo && "Couple Photo"}
        </div>
      </div>
      
      <div style={styles.marigoldBorder}></div>
      
      <div style={styles.blessingText}>
        "{custom_message}"
      </div>
      
      <div style={styles.marigoldBorder}></div>
      <div style={styles.headerDecoration}></div>
    </div>
  );
};

export default IndianTraditionalTemplate;
