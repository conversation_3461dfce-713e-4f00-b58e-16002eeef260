"use client";

import React, { useState, useEffect } from "react";
import { Heart, MessageCircle, Share2, MoreVertical, Plus, Bookmark } from "lucide-react";
import axiosInstance from "../services/axiosConfig";
import UserAvatar from "./HomeDashboard/UserAvatar";
import { useRouter } from "next/navigation";
import { saveContentToMyWedding, removeContentFromMyWedding, checkContentSavedStatus } from '../services/myWeddingService';

interface VideoInteractionBarProps {
  username: string;
  uploadDate: string;
  viewCount: number;
  description?: string;
  userId: string;
  contentId?: string;
  contentType?: string;
  isOwnContent?: boolean; // Add this prop
  isFollowing?: boolean; // NEW: controlled admire state from parent
}

const VideoInteractionBar: React.FC<VideoInteractionBarProps> = ({
  username,
  uploadDate,
  viewCount,
  description,
  userId,
  contentId,
  contentType,
  isOwnContent = false, // Default to false
  isFollowing, // NEW: controlled admire state from parent
}) => {
  const router = useRouter();
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isAdmiring, setIsAdmiring] = useState(!!isFollowing);
  const [isAdmiringLoading, setIsAdmiringLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [toast, setToast] = useState<string | null>(null);

  // Like/Unlike API integration using axios instance (with auth header)
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);

  // Add view count API call for glimpses and movies
  useEffect(() => {
    if (contentId && contentType && typeof window !== 'undefined') {
      // Only send view event once per mount
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) return;
      fetch(`${apiBaseUrl}/view`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content_id: contentId, content_type: contentType }),
      }).catch((err) => console.warn('Failed to record view:', err));
    }
  }, [contentId, contentType]);

  // Like/Unlike handlers for glimpses and movies
  const handleLikeToggle = async () => {
    if (!contentId) return;
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) return;
    try {
      if (isLiked) {
        await fetch(`${apiBaseUrl}/unlike`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ content_id: contentId, content_type: contentType }),
        });
        setIsLiked(false);
        setLikeCount((prev) => Math.max(0, prev - 1));
      } else {
        await fetch(`${apiBaseUrl}/like`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ content_id: contentId, content_type: contentType }),
        });
        setIsLiked(true);
        setLikeCount((prev) => prev + 1);
      }
    } catch (err) {
      console.error('Failed to toggle like:', err);
      alert('Failed to update like. Please try again.');
    }
  };

  // Debug: Log props on mount
  useEffect(() => {
    console.log('VideoInteractionBar props:', { userId, contentId, contentType });
  }, [userId, contentId, contentType]);

  // Format view count
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Handle message button click
  const handleMessageClick = async () => {
    if (isCreatingChat) return;

    try {
      setIsCreatingChat(true);

      // Get token from localStorage - use userToken as in the chat page
      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");

      if (!token) {
        console.error("No authentication token found");
        alert("Please log in to send messages");
        setIsCreatingChat(false);
        return;
      }

      // Use the hardcoded URL from the messages page
      const apiUrl = process.env.NEXT_PUBLIC_API_URL;

      // If userId is 'default' or missing, use a fallback ID for testing
      // In a real app, you would handle this differently
      const participantId =
        !userId || userId === "default"
          ? username.toLowerCase().replace(/\s+/g, "_") + "_id"
          : userId;

      console.log(`Creating conversation with user ID: ${participantId}`);
      console.log(`Using API URL: ${apiUrl}/conversations`);
      console.log(`Authorization token: ${token.substring(0, 10)}...`);

      try {
        // Create or get existing conversation
        console.log(
          "Request payload:",
          JSON.stringify({ participants: [participantId] })
        );

        const response = await fetch(`${apiUrl}/conversations`, {
          method: "POST",
          headers:
            {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          body: JSON.stringify({
            participants: [participantId],
          }),
          // Add these options to help with CORS issues
          mode: "cors",
          credentials: "same-origin",
        });

        console.log("Response status:", response.status);
        console.log("Response headers:", [...response.headers.entries()]);

        if (!response.ok) {
          throw new Error(`Failed to create conversation: ${response.status}`);
        }

        const data = await response.json();
        console.log("Conversation created/retrieved:", data);

        // Handle different response formats
        let conversationId = null;

        // Check if the response has a direct conversation_id property
        if (data.conversation_id) {
          conversationId = data.conversation_id;
        }
        // Check if the response has a body property that might contain the conversation_id
        else if (data.body && typeof data.body === "string") {
          try {
            const bodyData = JSON.parse(data.body);
            if (bodyData.conversation_id) {
              conversationId = bodyData.conversation_id;
              console.log("Found conversation ID in body:", conversationId);
            }
          } catch (e) {
            console.error("Error parsing body data:", e);
          }
        }

        if (!conversationId) {
          console.error("No conversation ID found in any format", data);
          throw new Error("Invalid response from server");
        }

        // Navigate to the chat page
        router.push(`/messages/${conversationId}`);
      } catch (innerError) {
        console.error("Inner fetch error:", innerError);
        throw innerError;
      }
    } catch (error) {
      console.error("Error creating conversation:", error);

      // Provide more specific error messages based on the error
      if (
        error instanceof TypeError &&
        error.message.includes("Failed to fetch")
      ) {
        alert(
          "Network error: Please check your internet connection and try again."
        );
      } else if (error instanceof Error && error.message.includes("401")) {
        alert("Authentication error: Please log in again.");
      } else if (error instanceof Error && error.message.includes("403")) {
        alert(
          "Permission denied: You don't have permission to message this user."
        );
      } else {
        alert("Failed to start conversation. Please try again.");
      }
    } finally {
      setIsCreatingChat(false);
    }
  };

  // Function to navigate to user profile
  const navigateToUserProfile = () => {
    if (userId) {
      // Get the token from localStorage
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');

      if (!token) {
        console.warn('No authentication token found');
        return;
      }

      // Navigate to the profile page with the userId
      router.push(`/profile/${userId}`);
    }
  };

  // Sync isAdmiring with isFollowing prop from parent
  useEffect(() => {
    if (typeof isFollowing === 'boolean') {
      setIsAdmiring(isFollowing);
    }
  }, [isFollowing]);

  // Function to handle admire/unadmire
  const handleAdmireToggle = async () => {
    if (isAdmiringLoading || !userId) return;

    try {
      setIsAdmiringLoading(true);
      const token = localStorage.getItem('token') ||
        localStorage.getItem('jwt_token') ||
        localStorage.getItem('wedzat_token');
      if (!token) {
        console.warn('No authentication token found');
        alert('Please log in to admire this user');
        setIsAdmiringLoading(false);
        return;
      }
      // Optimistically update UI state immediately for better user experience
      const newAdmiringState = !isAdmiring;
      setIsAdmiring(newAdmiringState);
      // Make API call to follow/unfollow user
      const endpoint = isAdmiring
        ? `${apiBaseUrl}/unfollow`
        : `${apiBaseUrl}/follow`;
      try {
        await axiosInstance.post(
          endpoint,
          { target_user_id: userId },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
        // No localStorage update here; parent should update isFollowing prop from API response
      } catch (apiError: any) {
        setIsAdmiring(!newAdmiringState); // revert on error
      }
    } catch (error: any) {
      setIsAdmiring(!isAdmiring);
    } finally {
      setIsAdmiringLoading(false);
    }
  };

  // Get the logged-in user's ID from localStorage/sessionStorage, fallback to prop
  const getLoggedInUserId = () => {
    if (typeof window === 'undefined') return null;
    let id = localStorage.getItem('user_id') || sessionStorage.getItem('user_id');
    if (id) return id;
    // Try to parse user object
    let userObj = localStorage.getItem('user') || sessionStorage.getItem('user') || localStorage.getItem('profile') || sessionStorage.getItem('profile');
    if (userObj) {
      try {
        const parsed = JSON.parse(userObj);
        if (parsed && (parsed.id || parsed._id || parsed.user_id)) {
          return parsed.id || parsed._id || parsed.user_id;
        }
      } catch {}
    }
    // Fallback to prop (for SSR or legacy)
    return userId || null;
  };

  // Check saved status on mount (always use logged-in user)
  useEffect(() => {
    const loggedInUserId = getLoggedInUserId();
    if (!loggedInUserId) return;
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) return;
    const content_id = contentId || '';
    const content_type = contentType || '';
    if (!content_id || !content_type) return;
    checkContentSavedStatus({ user_id: loggedInUserId, content_id })
      .then(res => setIsSaved(res.data.is_saved))
      .catch(() => setIsSaved(false));
  }, [contentId, contentType]);

  const handleSaveToggle = async () => {
    const loggedInUserId = getLoggedInUserId();
    if (!loggedInUserId) { console.warn('No logged-in userId'); return; }
    const content_id = contentId || '';
    const content_type = contentType || '';
    if (!content_id || !content_type) {
      console.warn('Missing content_id or content_type', { content_id, content_type });
      return;
    }
    setIsSaving(true);
    try {
      if (isSaved) {
        // UNSAVE
        await removeContentFromMyWedding({ user_id: loggedInUserId, content_id });
        setToast('Removed from My Weddings');
      } else {
        // SAVE
        await saveContentToMyWedding({ user_id: loggedInUserId, content_id, content_type });
        setToast('Video saved to My Weddings');
      }
      // Always re-check saved status from backend for this user/content
      const res = await checkContentSavedStatus({ user_id: loggedInUserId, content_id });
      setIsSaved(res.data.is_saved);
    } catch (e) {
      setToast('Something went wrong');
      console.error('Save/remove API error', e);
    } finally {
      setIsSaving(false);
      setTimeout(() => setToast(null), 2000);
    }
  };

  // If contentId is provided, fetch like status and count from props or API if needed
  // For now, use props if available
  useEffect(() => {
    if (typeof window !== 'undefined' && contentId) {
      // Optionally, fetch like status/count from API here
      // setIsLiked(...)
      // setLikeCount(...)
    }
  }, [contentId]);

  return (
    <div className="w-full bg-white p-2 sm:p-3 md:p-4 rounded-b-xl border border-gray-200 border-t-0">
      <div className="flex items-center justify-between mb-3 md:mb-4">
        <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3">
          <div className="flex space-x-1 sm:space-x-2">
            <button
              className={`flex items-center justify-center rounded-full p-1.5 sm:p-2 transition-colors ${isLiked ? 'bg-red-200' : 'bg-red-100 hover:bg-red-200'}`}
              onClick={handleLikeToggle}
              aria-label={isLiked ? 'Unlike' : 'Like'}
              type="button"
            >
              <Heart
                className={`w-4 h-4 sm:w-5 sm:h-5 ${isLiked ? 'text-red-600' : 'text-red-600'}`}
                fill={isLiked ? '#dc2626' : 'none'}
              />
              <span className="ml-1 text-xs text-gray-700">{likeCount}</span>
            </button>
            <button
              className={`flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors ${isSaved ? 'bg-yellow-200' : ''}`}
              onClick={handleSaveToggle}
              disabled={isSaving}
              title={isSaved ? 'Remove from My Weddings' : 'Save to My Weddings'}
            >
              <Bookmark className={`w-4 h-4 sm:w-5 sm:h-5 ${isSaved ? 'text-yellow-600' : 'text-gray-700'}`} fill={isSaved ? '#FFD700' : 'none'} />
            </button>
            <button className="flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full p-1.5 sm:p-2 transition-colors">
              <Share2 className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm text-gray-700">
            <span className="font-semibold">{formatViewCount(viewCount)}</span>{" "}
            views
          </div>
          <div className="relative menu-container">
            <button
              onClick={() => setShowMenu(!showMenu)}
              className="p-1.5 rounded-full hover:bg-gray-100"
            >
              <MoreVertical size={18} className="text-gray-700" />
            </button>

            {showMenu && (
              <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div className="py-1">
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Report content
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="cursor-pointer" onClick={navigateToUserProfile}>
              <UserAvatar username={username || "Anonymous"} size="md" />
            </div>
            <div>
              <div
                className="font-medium text-black cursor-pointer hover:underline"
                onClick={navigateToUserProfile}
              >
                {username}
              </div>
              <div className="text-xs text-gray-500">Uploaded {uploadDate || 'recently'}</div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {!isOwnContent && (
              <>
                <button
                  onClick={handleMessageClick}
                  disabled={isCreatingChat}
                  className="flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-full text-sm transition-colors"
                  title="Send a message"
                >
                  <MessageCircle className="w-4 h-4" />
                  <span>{isCreatingChat ? "Opening..." : "Message"}</span>
                </button>

                <button
                  onClick={handleAdmireToggle}
                  disabled={isAdmiringLoading}
                  className="flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-colors bg-[#B31B1E] text-white hover:bg-red-700"
                  title={isAdmiring ? "Admiring" : "Admire this user"}
                >
                  {isAdmiringLoading ? (
                    <span className="animate-pulse">...</span>
                  ) : (
                    <>
                      {isAdmiring ? 'Admiring' : 'Admire'}
                      {!isAdmiring && <Plus size={16} />}
                    </>
                  )}
                </button>
              </>
            )}
          </div>
        </div>

        {description && (
          <p className="text-black mt-2 text-xs sm:text-sm line-clamp-3 sm:line-clamp-none">
            {description}
          </p>
        )}
      </div>

      {/* Toast message */}
      {toast && (
        <div style={{ position: 'fixed', bottom: 24, right: 24, zIndex: 9999 }} className="bg-black text-white px-4 py-2 rounded shadow-lg animate-fade-in">
          {toast}
        </div>
      )}
    </div>
  );
};

export default VideoInteractionBar;
