"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    var _fonts_body, _fonts_decorative, _fonts_heading, _fonts_decorative1, _fonts_heading1;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '600px',\n            width: '100%',\n            background: colors.background,\n            borderRadius: '20px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: ((_fonts_body = fonts.body) === null || _fonts_body === void 0 ? void 0 : _fonts_body.join(', ')) || 'Roboto, Arial, sans-serif'\n        },\n        headerDecoration: {\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            height: '8px',\n            width: '100%'\n        },\n        marigoldBorder: {\n            background: \"url(\\\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='\".concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='30' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='50' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='70' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='90' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/></svg>\\\") repeat-x\"),\n            height: '20px',\n            width: '100%'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '30px 20px 20px',\n            background: \"linear-gradient(180deg, \".concat(colors.background, \" 0%, #FFF8E7 100%)\")\n        },\n        ganeshSymbol: {\n            fontSize: '48px',\n            color: colors.primary,\n            marginBottom: '10px'\n        },\n        ganeshText: {\n            fontFamily: ((_fonts_decorative = fonts.decorative) === null || _fonts_decorative === void 0 ? void 0 : _fonts_decorative.join(', ')) || 'Dancing Script, cursive',\n            fontSize: '24px',\n            color: colors.primary,\n            fontWeight: '700',\n            marginBottom: '20px'\n        },\n        coupleNames: {\n            fontFamily: ((_fonts_heading = fonts.heading) === null || _fonts_heading === void 0 ? void 0 : _fonts_heading.join(', ')) || 'Playfair Display, serif',\n            fontSize: '42px',\n            fontWeight: '700',\n            color: colors.primary,\n            textAlign: 'center',\n            margin: '20px 0',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        andSymbol: {\n            fontFamily: ((_fonts_decorative1 = fonts.decorative) === null || _fonts_decorative1 === void 0 ? void 0 : _fonts_decorative1.join(', ')) || 'Dancing Script, cursive',\n            fontSize: '32px',\n            color: colors.secondary,\n            margin: '0 15px'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: ((_fonts_heading1 = fonts.heading) === null || _fonts_heading1 === void 0 ? void 0 : _fonts_heading1.join(', ')) || 'Playfair Display, serif',\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});