"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Loader2, Plus, Edit, Trash2, Share2, Eye } from "lucide-react";
import EInviteEditor from "./EInviteEditor";

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  deployed_url: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  template_name: string;
  template_thumbnail: string;
  design_settings?: {
    colors?: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts?: {
      heading: string;
      body: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
    };
    customImage?: string;
    couple_names?: string;
    type?: string; // 'einvite' to distinguish from regular websites
  };
}

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface EInvitesProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const EInvites: React.FC<EInvitesProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  const [einvites, setEInvites] = useState<EInvite[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingEInvite, setEditingEInvite] = useState<EInvite | null>(null);
  const [loadingEInvites, setLoadingEInvites] = useState(true);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  useEffect(() => {
    fetchEInvites();
    fetchTemplates();
  }, []);

  const getAuthToken = () => {
    return localStorage.getItem('authToken');
  };

  const fetchEInvites = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        // If no token, just set empty e-invites and show templates
        setEInvites([]);
        setLoadingEInvites(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.websites) {
        // Filter for e-invites (websites marked with type='einvite' in design_settings)
        const einvites = response.data.websites.filter((website: any) =>
          website.design_settings?.type === 'einvite'
        );

        setEInvites(einvites);
      }
      setLoadingEInvites(false);
    } catch (error) {
      console.error('Error fetching e-invites:', error);
      // Don't show error for e-invites, just show empty and let templates load
      setEInvites([]);
      setLoadingEInvites(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      // Try without authentication first, then with authentication if needed
      let response;
      const token = getAuthToken();

      try {
        response = await axios.get(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates'
        );
      } catch (error) {
        // If that fails and we have a token, try with authentication
        if (token) {
          response = await axios.get(
            'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates',
            {
              headers: { Authorization: `Bearer ${token}` }
            }
          );
        } else {
          throw error;
        }
      }

      if (response.data && response.data.templates) {
        const processedTemplates = response.data.templates.map((template: any) => ({
          ...template,
          thumbnail_url: template.thumbnail_url || '/placeholder-template.jpg'
        }));

        setTemplates(processedTemplates);
      } else {
        // If no templates from API, create some default ones
        const defaultTemplates = [
          {
            template_id: 'default-1',
            name: 'Classic Elegance',
            thumbnail_url: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=600&fit=crop',
            description: 'Elegant wedding invitation template'
          },
          {
            template_id: 'default-2',
            name: 'Floral Romance',
            thumbnail_url: 'https://images.unsplash.com/photo-1606800052052-a08af7148866?w=400&h=600&fit=crop',
            description: 'Beautiful floral wedding invitation'
          },
          {
            template_id: 'default-3',
            name: 'Modern Minimalist',
            thumbnail_url: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop',
            description: 'Clean and modern design'
          },
          {
            template_id: 'default-4',
            name: 'Traditional Gold',
            thumbnail_url: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400&h=600&fit=crop',
            description: 'Traditional golden invitation'
          }
        ];
        setTemplates(defaultTemplates);
      }
      setLoadingTemplates(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      // Set default templates even if API fails
      const defaultTemplates = [
        {
          template_id: 'default-1',
          name: 'Classic Elegance',
          thumbnail_url: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=600&fit=crop',
          description: 'Elegant wedding invitation template'
        },
        {
          template_id: 'default-2',
          name: 'Floral Romance',
          thumbnail_url: 'https://images.unsplash.com/photo-1606800052052-a08af7148866?w=400&h=600&fit=crop',
          description: 'Beautiful floral wedding invitation'
        },
        {
          template_id: 'default-3',
          name: 'Modern Minimalist',
          thumbnail_url: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop',
          description: 'Clean and modern design'
        },
        {
          template_id: 'default-4',
          name: 'Traditional Gold',
          thumbnail_url: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400&h=600&fit=crop',
          description: 'Traditional golden invitation'
        }
      ];
      setTemplates(defaultTemplates);
      setLoadingTemplates(false);
    }
  };

  const handleCreateEInvite = (template?: Template) => {
    if (template) {
      // Create a new e-invite based on the selected template
      const newEInvite = {
        website_id: '',
        title: 'Our Wedding E-Invite',
        couple_names: 'Eiyana & Warlin',
        template_id: template.template_id,
        wedding_date: '',
        wedding_location: 'THE LITTLE HOTEL, 888, Glenport Road, Australia',
        about_couple: '',
        deployed_url: '',
        is_published: false,
        created_at: '',
        updated_at: '',
        template_name: template.name,
        template_thumbnail: template.thumbnail_url,
        design_settings: {
          colors: {
            primary: "#B31B1E",
            secondary: "#333333",
            background: "#FFFFFF",
            text: "#000000"
          },
          fonts: {
            heading: "Playfair Display",
            body: "Open Sans",
            coupleNames: "Dancing Script",
            date: "Open Sans",
            location: "Open Sans",
            aboutCouple: "Open Sans"
          },
          customImage: template.thumbnail_url,
          couple_names: 'Eiyana & Warlin',
          type: 'einvite'
        }
      };
      setEditingEInvite(newEInvite as any);
    } else {
      setEditingEInvite(null);
    }
    setShowEditor(true);
  };

  const handleEditEInvite = (einvite: EInvite) => {
    setEditingEInvite(einvite);
    setShowEditor(true);
  };

  const handleSaveEInvite = async (einviteData: any) => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        setError("Authentication required");
        setLoading(false);
        return;
      }

      // Mark this as an e-invite
      const dataWithType = {
        ...einviteData,
        design_settings: {
          ...einviteData.design_settings,
          type: 'einvite'
        }
      };

      let response;
      if (editingEInvite) {
        // Update existing e-invite using website API
        response = await axios.put(
          `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website`,
          {
            website_id: editingEInvite.website_id,
            ...dataWithType
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      } else {
        // Create new e-invite using website API
        response = await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website',
          dataWithType,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }

      if (response.data) {
        setSuccessMessage(editingEInvite ? "E-Invite updated successfully!" : "E-Invite created successfully!");
      }

      // Refresh the e-invites list
      fetchEInvites();

      // Close the editor
      setShowEditor(false);
      setEditingEInvite(null);
      setLoading(false);
    } catch (error) {
      console.error('Error saving e-invite:', error);
      setError("Failed to save e-invite. Please try again.");
      setLoading(false);
    }
  };

  const handleDeleteEInvite = async (einviteId: string) => {
    if (!window.confirm("Are you sure you want to delete this e-invite?")) {
      return;
    }

    try {
      setDeletingId(einviteId);
      const token = getAuthToken();

      if (!token) {
        setError("Authentication required");
        setDeletingId(null);
        return;
      }

      await axios.delete(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website`,
        {
          headers: { Authorization: `Bearer ${token}` },
          data: { website_id: einviteId }
        }
      );

      setSuccessMessage("E-Invite deleted successfully!");
      fetchEInvites();
      setDeletingId(null);
    } catch (error) {
      console.error('Error deleting e-invite:', error);
      setError("Failed to delete e-invite. Please try again.");
      setDeletingId(null);
    }
  };

  const handleShareEInvite = async (einvite: EInvite) => {
    try {
      // Generate sharing link (viewable by anyone, but encourages login for RSVP)
      const shareUrl = `${window.location.origin}/e-invite/${einvite.website_id}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(shareUrl);
      setSuccessMessage("E-Invite link copied to clipboard! Anyone can view it, and they'll be encouraged to join Wedzat to RSVP.");
    } catch (error) {
      console.error('Error sharing e-invite:', error);
      setError("Failed to copy link. Please try again.");
    }
  };

  const handlePreviewEInvite = (einvite: EInvite) => {
    // Open preview in new tab
    const previewUrl = `/e-invite/${einvite.website_id}`;
    window.open(previewUrl, '_blank');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (showEditor) {
    return (
      <EInviteEditor
        templates={templates}
        einvite={editingEInvite as any}
        onSave={handleSaveEInvite}
        onCancel={() => {
          setShowEditor(false);
          setEditingEInvite(null);
        }}
        loading={loading}
      />
    );
  }

  return (
    <div className="bg-white">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
            <div className="w-4 h-4 bg-gray-400 rounded"></div>
          </div>
          <h2 className="text-2xl font-bold text-black">
            Our Most Trending <span className="text-[#B31B1E]">Invites</span>
          </h2>
        </div>
      </div>

      {loadingEInvites || loadingTemplates ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 size={32} className="animate-spin text-[#B31B1E]" />
        </div>
      ) : (
        <>
          {/* Templates Grid - Show templates if no e-invites exist */}
          {einvites.length === 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              {templates.length > 0 ? templates.slice(0, 8).map((template) => (
                <div
                  key={template.template_id}
                  className="relative group cursor-pointer"
                  onClick={() => handleCreateEInvite(template)}
                >
                  <div className="aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative">
                    <img
                      src={template.thumbnail_url || '/placeholder-template.jpg'}
                      alt={template.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = '/placeholder-template.jpg';
                      }}
                    />

                    {/* Play button overlay */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                        <div className="w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                      </div>
                    </div>

                    {/* Hover overlay with "Customize" button */}
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2">
                        <Edit size={16} />
                        Customise
                      </button>
                    </div>
                  </div>
                </div>
              )) : (
                <div className="col-span-full text-center py-8">
                  <p className="text-gray-600 mb-4">Loading templates...</p>
                  <button
                    onClick={() => handleCreateEInvite()}
                    className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    Create E-Invite
                  </button>
                </div>
              )}
            </div>
          )}

          {/* User's E-Invites */}
          {einvites.length > 0 && (
            <>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-black">Your E-Invites</h3>
                <button
                  onClick={() => handleCreateEInvite()}
                  className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                  disabled={loading}
                >
                  <Plus size={20} />
                  Create New
                </button>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {einvites.map((einvite) => (
                  <div
                    key={einvite.website_id}
                    className="relative group cursor-pointer"
                    onMouseEnter={() => setHoveredCard(einvite.website_id)}
                    onMouseLeave={() => setHoveredCard(null)}
                  >
                    <div className="aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative">
                      <img
                        src={einvite.design_settings?.customImage || einvite.template_thumbnail || '/placeholder-template.jpg'}
                        alt={einvite.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = '/placeholder-template.jpg';
                        }}
                      />

                      {/* Play button overlay */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg">
                          <div className="w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                        </div>
                      </div>

                      {/* Hover overlay with "Customize" button */}
                      {hoveredCard === einvite.website_id && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <button
                            onClick={() => handleEditEInvite(einvite)}
                            className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                          >
                            <Edit size={16} />
                            Customise
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Action buttons on hover */}
                    {hoveredCard === einvite.website_id && (
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShareEInvite(einvite);
                          }}
                          className="p-1 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Share E-Invite"
                        >
                          <Share2 size={14} className="text-gray-600" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEInvite(einvite.website_id);
                          }}
                          className="p-1 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                          title="Delete E-Invite"
                          disabled={deletingId === einvite.website_id}
                        >
                          {deletingId === einvite.website_id ? (
                            <Loader2 size={14} className="animate-spin text-gray-600" />
                          ) : (
                            <Trash2 size={14} className="text-gray-600" />
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}
        </>
      )}

      {/* Error message */}
      {error && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default EInvites;
