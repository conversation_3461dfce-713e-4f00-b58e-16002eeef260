<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{couple_names}} - Wedding Invitation</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Dancing+Script:wght@400;700&family=Roboto:wght@300;400;500&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #B31B1E 0%, #8B0000 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .invitation-container {
            max-width: 600px;
            width: 100%;
            background: #FFFFFF;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .header-decoration {
            background: linear-gradient(90deg, #FFD700, #FF8C00, #FFD700);
            height: 8px;
            width: 100%;
        }
        
        .marigold-border {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="10" cy="10" r="8" fill="%23FFD700"/><circle cx="30" cy="10" r="8" fill="%23FF8C00"/><circle cx="50" cy="10" r="8" fill="%23FFD700"/><circle cx="70" cy="10" r="8" fill="%23FF8C00"/><circle cx="90" cy="10" r="8" fill="%23FFD700"/></svg>') repeat-x;
            height: 20px;
            width: 100%;
        }
        
        .ganesh-section {
            text-align: center;
            padding: 30px 20px 20px;
            background: linear-gradient(180deg, #FFFFFF 0%, #FFF8E7 100%);
        }
        
        .ganesh-symbol {
            font-size: 48px;
            color: #B31B1E;
            margin-bottom: 10px;
        }
        
        .ganesh-text {
            font-family: 'Dancing Script', cursive;
            font-size: 24px;
            color: #B31B1E;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .couple-names {
            font-family: 'Playfair Display', serif;
            font-size: 42px;
            font-weight: 700;
            color: #B31B1E;
            text-align: center;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .couple-names .and {
            font-family: 'Dancing Script', cursive;
            font-size: 32px;
            color: #FFD700;
            margin: 0 15px;
        }
        
        .invitation-text {
            text-align: center;
            padding: 20px 30px;
            font-size: 18px;
            color: #333;
            line-height: 1.6;
        }
        
        .event-details {
            background: linear-gradient(135deg, #B31B1E 0%, #8B0000 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .event-title {
            font-family: 'Playfair Display', serif;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #FFD700;
        }
        
        .event-info {
            margin: 15px 0;
            font-size: 16px;
        }
        
        .event-date {
            font-size: 24px;
            font-weight: 500;
            margin: 20px 0;
            color: #FFD700;
        }
        
        .decorative-divider {
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #FFD700, #FF8C00, #FFD700);
            margin: 20px auto;
            border-radius: 2px;
        }
        
        .couple-photo {
            text-align: center;
            padding: 30px;
            background: #FFF8E7;
        }
        
        .photo-placeholder {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #B31B1E;
            font-weight: 500;
            border: 5px solid #B31B1E;
        }
        
        .footer-decoration {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 30"><path d="M0,15 Q25,5 50,15 T100,15" stroke="%23FFD700" stroke-width="3" fill="none"/><circle cx="20" cy="15" r="4" fill="%23FF8C00"/><circle cx="50" cy="15" r="4" fill="%23FFD700"/><circle cx="80" cy="15" r="4" fill="%23FF8C00"/></svg>') repeat-x;
            height: 30px;
            width: 100%;
        }
        
        .blessing-text {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .couple-names {
                font-size: 32px;
            }
            
            .couple-names .and {
                font-size: 24px;
            }
            
            .ganesh-symbol {
                font-size: 36px;
            }
            
            .event-title {
                font-size: 24px;
            }
            
            .invitation-text {
                font-size: 16px;
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="header-decoration"></div>
        <div class="marigold-border"></div>
        
        <div class="ganesh-section">
            <div class="ganesh-symbol">🕉️</div>
            <div class="ganesh-text">|| Shree Ganesh Namah ||</div>
        </div>
        
        <div class="couple-names">
            {{partner1_name}}<span class="and">&</span>{{partner2_name}}
        </div>
        
        <div class="invitation-text">
            We Invite You to Share in our Joy And Request<br>
            Your Presence At the Reception
        </div>
        
        <div class="event-details">
            <div class="event-title">✦ Muhurtam ✦</div>
            <div class="event-info">Time {{muhurtam_time}}</div>
            <div class="event-info">{{wedding_venue}}</div>
            
            <div class="decorative-divider"></div>
            
            <div class="event-date">On {{wedding_date}}</div>
            
            <div class="decorative-divider"></div>
            
            <div class="event-title">✦ Reception ✦</div>
            <div class="event-info">Time {{reception_time}}</div>
            <div class="event-info">{{reception_venue}}</div>
            
            <div class="event-date">On {{reception_date}}</div>
        </div>
        
        <div class="couple-photo">
            <div class="photo-placeholder">
                {{couple_photo}}
            </div>
        </div>
        
        <div class="footer-decoration"></div>
        
        <div class="blessing-text">
            "May your love story be as magical and charming as in fairy tales!"
        </div>
        
        <div class="marigold-border"></div>
        <div class="header-decoration"></div>
    </div>
</body>
</html>
