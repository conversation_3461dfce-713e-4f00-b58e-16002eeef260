"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import EmojiPicker, { EmojiClickData } from 'emoji-picker-react';
import useIsMobile from '../../../hooks/useIsMobile';
import { TopNavigation } from '../../../components/HomeDashboard/Navigation';

// Types
type Message = {
  message_id: string;
  content: string;
  sent_at: string;
  is_mine: boolean;
  sender_name: string;
  sender_avatar?: string | null;
};

type Conversation = {
  conversation_id: string;
  name: string;
  is_group: boolean;
  other_user?: {
    user_id: string;
    name: string;
    profile_picture: string | null;
  };
};

export default function MobileChatPage() {
  const isMobile = useIsMobile();
  const router = useRouter();
  const params = useParams();
  const chatId = params?.chatId as string;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State management
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [conversationName, setConversationName] = useState('Chat');
  const [conversationAvatar, setConversationAvatar] = useState<string | null>(null);

  useEffect(() => {
    if (isMobile === false && chatId) {
      router.replace(`/messages/${chatId}`);
    }
  }, [isMobile, chatId, router]);

  useEffect(() => {
    const fetchConversationAndMessages = async () => {
      try {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found');
          setLoading(false);
          return;
        }

        // Fetch conversation details
        try {
          const conversationResponse = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/conversations/${chatId}`,
            {
              method: 'GET',
              headers: { 
                Authorization: `Bearer ${token}`, 
                'Content-Type': 'application/json' 
              },
            }
          );
          
          if (conversationResponse.ok) {
            const conversationData = await conversationResponse.json();
            let conversation: Conversation;
            
            if (conversationData.body && typeof conversationData.body === 'string') {
              conversation = JSON.parse(conversationData.body);
            } else {
              conversation = conversationData;
            }
            
            if (conversation) {
              if (!conversation.is_group && conversation.other_user) {
                setConversationName(conversation.other_user.name || 'User');
                setConversationAvatar(conversation.other_user.profile_picture);
              } else {
                setConversationName(conversation.name || 'Chat');
              }
            }
          }
        } catch (e) {
          console.error('Error fetching conversation:', e);
        }

        // Fetch messages
        const messagesResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/messages?conversation_id=${chatId}`,
          {
            method: 'GET',
            headers: { 
              Authorization: `Bearer ${token}`, 
              'Content-Type': 'application/json' 
            },
          }
        );
        
        if (!messagesResponse.ok) {
          throw new Error('Failed to fetch messages');
        }
        
        const responseData = await messagesResponse.json();
        let messagesArray: Message[] = [];
        
        if (responseData.body && typeof responseData.body === 'string') {
          messagesArray = JSON.parse(responseData.body);
        } else if (Array.isArray(responseData)) {
          messagesArray = responseData;
        } else if (responseData.data && Array.isArray(responseData.data)) {
          messagesArray = responseData.data;
        }
        
        const validMessages = Array.isArray(messagesArray) ? messagesArray : [];
        const sortedMessages = [...validMessages].sort(
          (a, b) => new Date(a.sent_at).getTime() - new Date(b.sent_at).getTime()
        );
        
        setMessages(sortedMessages);
        
        // Set conversation name from messages if not already set
        if (conversationName === 'Chat' && sortedMessages.length > 0) {
          const otherUserMessage = sortedMessages.find((msg) => !msg.is_mine);
          if (otherUserMessage) {
            setConversationName(otherUserMessage.sender_name);
            setConversationAvatar(otherUserMessage.sender_avatar || null);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    if (chatId) {
      fetchConversationAndMessages();
      
      // Set up polling
      const interval = setInterval(() => {
        if (chatId) fetchConversationAndMessages();
      }, 10000);
      
      return () => clearInterval(interval);
    }
  }, [chatId, conversationName]);

  useEffect(() => {
    if (!loading && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, loading]);

  async function handleSendMessage(e: React.FormEvent) {
    e.preventDefault();
    if (!newMessage.trim() || !chatId) return;
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found');
        return;
      }
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/messages`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation_id: chatId, content: newMessage }),
      });
      if (!response.ok) throw new Error('Failed to send message');
      const responseData = await response.json();
      let sentMessage = null;
      if (responseData.body && typeof responseData.body === 'string') {
        sentMessage = JSON.parse(responseData.body);
      } else {
        sentMessage = responseData;
      }
      if (sentMessage) setMessages((prev) => [...prev, sentMessage]);
      setNewMessage('');
    } catch (error) {
      alert('Failed to send message. Please try again.');
    }
  }

  if (isMobile === null) return null; // Wait for hydration
  if (isMobile === false) return null; // Redirecting to desktop

  if (!isMobile) {
    return <div className="flex items-center justify-center h-screen text-gray-500">This page is for mobile only.</div>;
  }
  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }
  if (error) {
    return <div className="flex items-center justify-center h-screen text-red-500">{error}</div>;
  }

  return (
    <div className="flex flex-col h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md border-b border-gray-200">
        <TopNavigation />
      </div>

      {/* Main Chat Content - Account for fixed navbar */}
      <div className="flex flex-col flex-1 bg-gray-100" style={{ marginTop: '84px' }}>
        {/* Chat header */}
        <div 
          className="p-4 border-b border-gray-300 flex items-center bg-white shadow-sm"
          style={{
            background: "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
          }}
        >
              {/* Back button */}
              <button 
                onClick={() => router.push('/mobile-messages')}
                className="mr-3 p-1 rounded-full hover:bg-gray-100"
              >
                <svg className="w-6 h-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              {/* Avatar */}
              {conversationAvatar ? (
                <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                  <Image src={conversationAvatar} alt={conversationName} fill className="object-cover" sizes="48px" />
                </div>
              ) : (
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-gray-500 font-medium">{conversationName.charAt(0).toUpperCase()}</span>
                </div>
              )}
              
          {/* Name and status */}
          <div className="flex-1 min-w-0 ml-3">
            <h1 className="text-lg font-semibold text-black truncate">{conversationName}</h1>
            <p className="text-xs text-gray-500">Active now</p>
          </div>
        </div>
        
        {/* Messages Container */}
        <div 
          className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50"
          style={{ 
            height: "calc(100vh - 84px - 80px - 80px)", // navbar + header + input form
            maxHeight: "calc(100vh - 84px - 80px - 80px)"
          }}
        >
        {messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <svg
                className="w-20 h-20 mx-auto text-gray-300 mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
              <h2 className="text-xl font-semibold mb-2">Start the conversation</h2>
              <p className="max-w-md mx-auto">
                Send a message to begin chatting with {conversationName}.
              </p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.message_id} className={`flex ${message.is_mine ? 'justify-end' : 'justify-start'}`}>
              {!message.is_mine && (
                <div className="flex-shrink-0 mr-2">
                  {message.sender_avatar ? (
                    <div className="relative w-8 h-8 rounded-full overflow-hidden">
                      <Image src={message.sender_avatar} alt={message.sender_name} fill className="object-cover" sizes="32px" />
                    </div>
                  ) : (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-500 font-medium">{message.sender_name.charAt(0).toUpperCase()}</span>
                    </div>
                  )}
                </div>
              )}
              <div className={`text-black ${message.is_mine ? 'rounded-tl-2xl rounded-tr-2xl rounded-bl-2xl' : 'rounded-tr-2xl rounded-br-2xl rounded-bl-2xl border border-[#D9D9D9]'}`} style={{ width: message.is_mine ? '230px' : '260px', padding: '12px 18px 6px 18px', gap: '4px', background: message.is_mine ? '#FFC74766' : '#E3E3F7' }}>
                {message.content}
                <div className={`text-xs mt-1 ${message.is_mine ? 'text-black' : 'text-gray-500'}`}>{formatMessageTime(message.sent_at)}</div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
        </div>
        
        {/* Message input - Fixed at bottom */}
        <div className="border-t border-gray-300 p-4 bg-white">
          <form onSubmit={handleSendMessage}>
        <div className="relative">
          {showEmojiPicker && (
            <div className="absolute bottom-14 left-0 z-10">
              <EmojiPicker 
                onEmojiClick={(emojiData) => { 
                  setNewMessage((prev) => prev + emojiData.emoji); 
                  setShowEmojiPicker(false); 
                }} 
                width={300} 
                height={400} 
              />
            </div>
          )}
          <div className="flex space-x-2">
            <button 
              type="button" 
              onClick={() => setShowEmojiPicker(!showEmojiPicker)} 
              className="bg-gray-200 text-gray-600 rounded-full p-2 w-10 h-10 flex items-center justify-center hover:bg-gray-300"
            >
              <span className="text-xl">😊</span>
            </button>
            <input 
              type="text" 
              value={newMessage} 
              onChange={(e) => setNewMessage(e.target.value)} 
              placeholder="Type a message..." 
              className="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 text-black bg-white" 
            />
            <button 
              type="submit" 
              disabled={!newMessage.trim()} 
              className="bg-green-500 text-white rounded-full p-2 w-10 h-10 flex items-center justify-center disabled:opacity-50 hover:bg-green-600"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M12 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
          </form>
        </div>
      </div>
    </div>
  );
}

function formatMessageTime(timeString: string): string {
  try {
    const date = new Date(timeString);
    const now = new Date();
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
    return date.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });
  } catch (e) {
    return timeString;
  }
}