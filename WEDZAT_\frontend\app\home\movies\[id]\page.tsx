"use client";
import React, { useState, useEffect } from "react";
import { TopNavigation, SideNavigation } from "../../../../components/HomeDashboard/Navigation";
import axios from "../../../../services/axiosConfig";
import Image from "next/image";
import { useParams } from "next/navigation";
import VideoInteractionBar from "../../../../components/VideoInteractionBar";
import VideoVendorDetails from "../../../../components/VideoVendorDetails";
import { MdFavorite, MdFavoriteBorder } from 'react-icons/md';
import useIsMobile from "../../../../hooks/useIsMobile";

// Import or create the content section components
import Glimpses from "../../../../components/HomeDashboard/Glimpses";
import Flashes from "../../../../components/HomeDashboard/Flashes";
import WeddingVideosSection from "../../../../components/HomeDashboard/WeddingVideos";
import Photos from "../../../../components/HomeDashboard/Photos";
import DetailPageLazyLoad from "../../../../components/DetailPageLazyLoad";

// Define interface for movie video items (updated for new API response)
interface MovieVideo {
  media_id: string;
  media_url: string;
  thumbnail_url: string;
  caption: string;
  location: string;
  event_type: string;
  video_type: string;
  partner: string;
  wedding_style: string;
  budget: string;
  created_at: string;
  user_id: string;
  user_name: string;
  user_avatar: string | null;
  is_own_content: boolean;
  followers_count: number;
  following_count: number;
  is_following: boolean;
  views_count: number;
  likes_count: number;
  is_liked: boolean;
}

interface RecommendedVideo {
  media_id: string;
  caption: string;
  thumbnail_url: string;
  user_name: string;
  created_at: string;
  views_count: number;
  duration?: number; // Add duration if available
}

export default function MovieDetailPage() {
  const params = useParams();
  const movieId = params?.id as string;

  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [movie, setMovie] = useState<MovieVideo | null>(null);
  const [recommendedVideos, setRecommendedVideos] = useState<RecommendedVideo[]>([]);
  const [filteredVideos, setFilteredVideos] = useState<RecommendedVideo[]>([]);
  const [activeFilter, setActiveFilter] = useState<'all' | 'creator'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const fetchMovieDetails = async () => {
      try {
        setLoading(true);

        // Get token from localStorage - try multiple possible keys
        const tokenKey = localStorage.getItem('token') ? 'token' :
                        localStorage.getItem('jwt_token') ? 'jwt_token' :
                        localStorage.getItem('auth_token') ? 'auth_token' : null;

        const token = tokenKey ? localStorage.getItem(tokenKey) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          return;
        }

        // First try to find the movie in the main movies endpoint
        const response = await axios.get(`/movies?page=1&limit=10`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.movies) {
          // Find the specific movie by ID
          const foundMovie = response.data.movies.find((m: MovieVideo) => m.media_id === movieId);

          if (foundMovie) {
            console.log('Found movie:', foundMovie);
            setMovie(foundMovie);

            // Filter out the current movie for recommendations
            const recommendations = response.data.movies
              .filter((m: MovieVideo) => m.media_id !== movieId)
              .slice(0, 7); // Limit to 7 recommendations

            setRecommendedVideos(recommendations);
          } else {
            console.warn(`Movie with ID ${movieId} not found in API response`);
            setError('Movie not found');
          }
        } else {
          console.warn('Unexpected API response format:', response.data);
          setError('Failed to load movie details');
        }
      } catch (err) {
        console.error('Error fetching movie details:', err);
        setError('Failed to load movie details');

        // Fallback to mock data for development/testing
        if (process.env.NODE_ENV === 'development') {
          console.log('Using fallback mock data for development');

          // Mock data for the current movie
          const mockMovie: MovieVideo = {
            media_id: movieId,
            media_url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            thumbnail_url: "/pics/placeholder.svg",
            caption: "This beautiful wedding ceremony took place in Paris with over 200 guests. The couple had been planning this day for over a year, and everything came together perfectly. The venue was decorated with thousands of flowers, and the ceremony was held outdoors under a beautiful arch.",
            location: "Paris, France",
            event_type: "Wedding",
            video_type: "Highlight",
            partner: "WeddingPlannersInc",
            wedding_style: "Classic",
            budget: "$$$$",
            created_at: new Date().toISOString(),
            user_id: "marcus123",
            user_name: "Marcus Levin",
            user_avatar: null,
            is_own_content: false,
            followers_count: 1200,
            following_count: 180,
            is_following: false,
            views_count: 9000000,
            likes_count: 741000,
            is_liked: false
          };

          setMovie(mockMovie);

          // Mock data for recommended videos
          const mockRecommendedVideos: RecommendedVideo[] = [
            {
              media_id: "L_LUpnjgPso",
              caption: "Beautiful Beach Wedding",
              thumbnail_url: "/pics/placeholder.svg",
              user_name: "James Gouse",
              created_at: "3 years ago",
              views_count: 1400000
            },
            {
              media_id: "9bZkp7q19f0",
              caption: "Mountain Wedding Ceremony",
              thumbnail_url: "/pics/placeholder.svg",
              user_name: "James Gouse",
              created_at: "3 years ago",
              views_count: 1400000
            },
            {
              media_id: "jNQXAC9IVRw",
              caption: "Garden Wedding Highlights",
              thumbnail_url: "/pics/placeholder.svg",
              user_name: "James Gouse",
              created_at: "3 years ago",
              views_count: 1400000
            },
            {
              media_id: "kJQP7kiw5Fk",
              caption: "Destination Wedding in Italy",
              thumbnail_url: "/pics/placeholder.svg",
              user_name: "James Gouse",
              created_at: "3 years ago",
              views_count: 1400000
            }
          ];

          setRecommendedVideos(mockRecommendedVideos);
          setError(null); // Clear error since we're using fallback data
        }
      } finally {
        setLoading(false);
      }
    };

    if (movieId) {
      fetchMovieDetails();
    }
  }, [movieId]);

  // Like, Unlike, and View API integration (copied from WeddingVideos.tsx)
  const likeContent = async (mediaId: string) => {
    try {
      await axios.post(`/like`, { content_id: mediaId });
    } catch (err) {
      console.error('Failed to like content:', err);
    }
  };

  const unlikeContent = async (mediaId: string) => {
    try {
      await axios.post(`/unlike`, { content_id: mediaId });
    } catch (err) {
      console.error('Failed to unlike content:', err);
    }
  };

  const viewContent = async (mediaId: string) => {
    try {
      await axios.post(`/view`, { content_id: mediaId });
    } catch (err) {
      console.error('Failed to record view:', err);
    }
  };

  // Like button handler
  const handleLikeToggle = async () => {
    if (!movie) return;
    if (movie.is_liked) {
      await unlikeContent(movie.media_id);
      setMovie({ ...movie, is_liked: false, likes_count: movie.likes_count - 1 });
    } else {
      await likeContent(movie.media_id);
      setMovie({ ...movie, is_liked: true, likes_count: movie.likes_count + 1 });
    }
  };

  // Call viewContent when video is played (for native video and YouTube)
  const handleVideoPlay = () => {
    if (movie) {
      viewContent(movie.media_id);
    }
  };

  // Format view count
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Format duration to MM:SS
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Filter videos based on active filter
  useEffect(() => {
    if (recommendedVideos.length > 0 && movie) {
      if (activeFilter === 'all') {
        setFilteredVideos(recommendedVideos);
      } else if (activeFilter === 'creator') {
        setFilteredVideos(recommendedVideos.filter(video =>
          video.user_name === movie.user_name
        ));
      }
    } else {
      setFilteredVideos(recommendedVideos);
    }
  }, [recommendedVideos, activeFilter, movie]);

  function getYoutubeEmbedUrl(video_url: string): string {
    // Extract the YouTube video ID from various possible URL formats
    // Supports: https://www.youtube.com/watch?v=VIDEO_ID, https://youtu.be/VIDEO_ID, etc.
    const youtubeRegex =
      /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/;
    const match = video_url.match(youtubeRegex);
    const videoId = match ? match[1] : null;
    if (!videoId) {
      // fallback: try to extract v= param
      const url = new URL(video_url, "https://youtube.com");
      const v = url.searchParams.get("v");
      if (v && v.length === 11) {
        return `https://www.youtube.com/embed/${v}`;
      }
      // fallback to original url if all else fails
      return video_url;
    }
    return `https://www.youtube.com/embed/${videoId}`;
  }

  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <div className="hidden md:block fixed left-0 top-[80px] h-[calc(100vh-80px)] z-30">
          <SideNavigation
            expanded={sidebarExpanded}
            onExpand={() => setSidebarExpanded(true)}
            onCollapse={() => setSidebarExpanded(false)}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex flex-col lg:flex-row flex-1 mt-[80px] w-full">
          {/* Video and Details Section */}
          <main
            className={`flex-1 bg-white flex flex-col w-full transition-all duration-300 ease-in-out ${
              isMobile 
                ? "p-0" 
                : `p-4 md:ml-[70px] ${sidebarExpanded ? "md:ml-60" : "md:ml-20"}`
            }`}
            style={{
              minHeight: "calc(100vh - 80px)",
              paddingBottom: isMobile ? "20px" : "40px",
              overflowY: "auto",
              overflowX: "hidden",
              position: "relative",
            }}
          >
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-600"></div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-600">{error}</div>
              </div>
            ) : movie ? (
              <div 
                className={`flex flex-col gap-2 w-full mx-auto ${
                  isMobile ? "px-0" : "sm:gap-4 md:gap-8 px-1 sm:px-2 md:px-4"
                }`}
                style={{ maxWidth: "100%" }}
              >
                {/* Video Player and Info Section */}
                <div 
                  className={`flex flex-col w-full items-center justify-center ${
                    isMobile ? "py-0" : "py-2 sm:py-3"
                  }`} 
                  style={{ maxWidth: "100%", margin: "0 auto" }}
                >
                  {/* Video Player */}
                  <div 
                    className={`w-full bg-black overflow-hidden relative ${
                      isMobile 
                        ? "rounded-none shadow-none border-none" 
                        : "rounded-lg sm:rounded-xl shadow-xl border border-gray-200"
                    }`}
                    style={{
                      maxWidth: "100%",
                      width: "100%",
                      height: "auto",
                    }}
                  >
                    {/* Video Title Overlay */}
                    <div className={`absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent z-10 ${
                      isMobile ? "p-3" : "p-2 sm:p-4"
                    }`}>
                      <h1 className={`text-white font-medium line-clamp-2 ${
                        isMobile ? "text-base" : "text-sm sm:text-lg md:text-xl"
                      }`}>
                        {movie.caption}
                      </h1>
                    </div>

                    {movie.media_url && (movie.media_url.includes("youtube.com") ||
                    movie.media_url.includes("youtu.be")) ? (
                      // YouTube embed with improved error handling
                      <iframe
                        src={getYoutubeEmbedUrl(movie.media_url)}
                        className="w-full aspect-video object-contain"
                        title={movie.caption}
                        style={{ border: "none" }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        onLoad={handleVideoPlay} // Call view API when iframe loads (best effort)
                        onError={(e) => {
                          console.error("YouTube iframe error:", e);
                          // Attempt to reload the iframe
                          const iframe = e.currentTarget as HTMLIFrameElement;
                          if (iframe) {
                            const src = iframe.src;
                            iframe.src = '';
                            setTimeout(() => {
                              iframe.src = src;
                            }, 1000);
                          }
                        }}
                      ></iframe>
                    ) : (
                      // Native video player with improved error handling
                      <div className="w-full aspect-video relative">
                        <video
                          className="w-full h-full object-contain"
                          controls
                          poster={movie.thumbnail_url || '/pics/placeholder.svg'}
                          preload="metadata"
                          autoPlay
                          controlsList="nodownload"
                          playsInline
                          webkit-playsinline="true"
                          x5-playsinline="true"
                          x5-video-player-type="h5"
                          onPlay={handleVideoPlay} // Call view API when video is played
                          onError={(e) => {
                            console.error("Video playback error:", e);
                            const videoElement = e.currentTarget;
                            
                            // Try to recover by reloading the video
                            if (videoElement) {
                              // First, try changing the source slightly to force a reload
                              const currentSrc = videoElement.src || movie.media_url;
                              if (!currentSrc) {
                                console.error("No video source available");
                                return;
                              }
                              
                              // Add a timestamp to force reload
                              const newSrc = currentSrc.includes('?') 
                                ? `${currentSrc}&t=${Date.now()}` 
                                : `${currentSrc}?t=${Date.now()}`;
                              
                              // Set the new source
                              videoElement.src = newSrc;
                              
                              // Then try to play again
                              setTimeout(() => {
                                videoElement.load();
                                videoElement.play().catch(err => {
                                  console.error("Retry failed:", err);
                                  
                                  // If still failing, try a different approach
                                  if (movie.media_url) {
                                    try {
                                      // Create a new video element as a last resort
                                      const newVideo = document.createElement('video');
                                      newVideo.src = movie.media_url;
                                      newVideo.className = videoElement.className;
                                      newVideo.controls = true;
                                      newVideo.autoplay = true;
                                      newVideo.playsInline = true;
                                      
                                      // Add multiple source elements for different formats
                                      const mp4Source = document.createElement('source');
                                      mp4Source.src = movie.media_url;
                                      mp4Source.type = 'video/mp4';
                                      newVideo.appendChild(mp4Source);
                                      
                                      const webmSource = document.createElement('source');
                                      webmSource.src = movie.media_url;
                                      webmSource.type = 'video/webm';
                                      newVideo.appendChild(webmSource);
                                      
                                      if (videoElement.parentNode) {
                                        videoElement.parentNode.replaceChild(newVideo, videoElement);
                                      }
                                    } catch (error) {
                                      console.error("Failed to create replacement video:", error);
                                    }
                                  }
                                });
                              }, 1000);
                            }
                          }}
                        >
                          <source src={movie.media_url} type="video/mp4" />
                          <source src={movie.media_url} type="video/webm" />
                          <source src={movie.media_url} type="video/quicktime" />
                          <source src={movie.media_url} type="video/x-matroska" />
                          Your browser does not support the video tag.
                        </video>
                        
                        {/* Fallback message if video fails to load */}
                        <div id="video-error-fallback" className="hidden absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white p-4 text-center">
                          <div>
                            <p className="text-lg font-semibold mb-2">Video playback error</p>
                            <p>The video could not be played. Please try again later or view another movie.</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Video container with interaction bar - directly connected to video */}
                  <div className={`w-full mx-auto overflow-hidden ${
                    isMobile ? "mb-2 -mt-0" : "mb-2 sm:mb-4 -mt-1"
                  }`}>
                    {/* Video Interaction Bar */}
                    <VideoInteractionBar
                      username={movie.user_name || "Anonymous"}
                      uploadDate={movie.created_at || 'recently'}
                      viewCount={movie.views_count || 0}
                      description={movie.caption}
                      userId={movie.user_id || 'default'}
                      contentId={movie.media_id}
                      contentType="movies"
                      isOwnContent={movie.is_own_content} // Pass prop to hide admire/message for own content
                    />

                    {/* Vendor Details Section */}
                    <div className={`bg-white ${
                      isMobile ? "px-3 pb-3" : "px-2 sm:px-4 pb-2 sm:pb-4"
                    }`}>
                      <VideoVendorDetails videoId={movie.media_id} isVerified={false} />
                    </div>
                  </div>
                </div>

                {/* Add Like button in the main movie details section, near VideoInteractionBar or below video info */}
                <div className={`flex items-center gap-2 mt-2 ${isMobile ? "px-3" : ""}`}>
                  <button
                    className="focus:outline-none"
                    aria-label={movie.is_liked ? 'Unlike' : 'Like'}
                    onClick={handleLikeToggle}
                    style={{ background: 'none', border: 'none', padding: 0, margin: 0, cursor: 'pointer', display: 'inline-flex', alignItems: 'center' }}
                  >
                    {movie.is_liked ? (
                      <MdFavorite className="inline text-red-500" size={24} />
                    ) : (
                      <MdFavoriteBorder className="inline text-gray-500" size={24} />
                    )}
                    <span className="ml-1 text-base">Like</span>
                  </button>
                  <span className="text-gray-700 text-base">{movie.likes_count}</span>

                  {/* Conditionally render admire and message buttons if not own content */}
                  {!movie.is_own_content && (
                    <>
                      <button
                        className="ml-4 px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm font-medium hover:bg-pink-200 transition-colors"
                        type="button"
                      >
                        Admire
                      </button>
                      <button
                        className="ml-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors"
                        type="button"
                      >
                        Message
                      </button>
                    </>
                  )}
                </div>

                {/* Mobile Recommended Videos Section */}
                {isMobile && (
                  <div className="px-3 mt-6">
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="font-semibold text-lg text-black">
                          Recommended Videos
                        </h2>
                        <button className="bg-white border border-gray-200 rounded-full p-1 hover:bg-gray-50">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <rect
                              width="18"
                              height="18"
                              x="3"
                              y="3"
                              rx="2"
                              ry="2"
                            ></rect>
                            <line x1="3" x2="21" y1="9" y2="9"></line>
                            <line x1="9" x2="9" y1="21" y2="9"></line>
                          </svg>
                        </button>
                      </div>

                      <div className="flex mb-4 gap-2 overflow-x-auto pb-1">
                        <button
                          onClick={() => setActiveFilter("all")}
                          className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors ${
                            activeFilter === "all"
                              ? "bg-red-600 text-white"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          All
                        </button>
                        <button
                          onClick={() => setActiveFilter("creator")}
                          className={`px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors ${
                            activeFilter === "creator"
                              ? "bg-red-600 text-white"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          From {movie?.user_name || "Creator"}
                        </button>
                      </div>

                      {/* Mobile List View */}
                      <div className="space-y-4">
                        {filteredVideos.map((video) => (
                          <div
                            key={video.media_id}
                            className="cursor-pointer hover:bg-gray-50 p-3 rounded-lg transition-all duration-200 border border-gray-100"
                            onClick={() => {
                              window.location.href = `/home/<USER>/${video.media_id}`;
                            }}
                          >
                            <div className="relative w-full h-48 rounded-lg overflow-hidden mb-3">
                              <Image
                                src={video.thumbnail_url || "/pics/placeholder.svg"}
                                alt={video.caption || "Movie"}
                                fill
                                className="object-cover"
                                sizes="100vw"
                              />
                              {typeof video.duration === 'number' && video.duration > 0 && (
                                <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                  {formatDuration(video.duration)}
                                </div>
                              )}
                            </div>
                            <div>
                              <h3 className="text-base font-medium line-clamp-2 text-black mb-2">
                                {video.caption}
                              </h3>
                              <p className="text-sm text-gray-600 mb-1">
                                {formatViewCount(video.views_count || 0)} views • {video.created_at || "3 years ago"}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Additional Content Sections - Lazy Loaded */}
                <div className={isMobile ? "px-3" : ""}>
                  <DetailPageLazyLoad id="glimpses-section" index={0} rootMargin="0px 0px 500px 0px">
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Glimpses shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad id="flashes-section" index={1} rootMargin="0px 0px 400px 0px">
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Flashes shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad id="wedding-videos-section" index={2} rootMargin="0px 0px 300px 0px">
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <WeddingVideosSection shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>

                  <DetailPageLazyLoad id="photos-section" index={3} rootMargin="0px 0px 200px 0px">
                    {({ shouldLoad }) => (
                      <div className="overflow-x-auto w-full mx-auto">
                        <Photos shouldLoad={shouldLoad} />
                      </div>
                    )}
                  </DetailPageLazyLoad>
                </div>
              </div>
            ) : null}
          </main>

          {/* Right Sidebar - Recommended Videos (Desktop Only) */}
          <aside className="hidden md:block md:relative md:bg-transparent md:w-[320px] lg:w-[350px] md:sticky md:top-20 bg-white border-l border-gray-100 overflow-y-auto md:h-[calc(100vh-80px)] shadow-md md:flex-shrink-0 md:flex-grow-0">
            <div className="md:w-full md:static md:z-auto">
              <div className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="font-semibold text-lg text-black">Recommended Videos</h2>
                  <button className="bg-white border border-gray-200 rounded-full p-1 hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                      <line x1="3" x2="21" y1="9" y2="9"></line>
                      <line x1="9" x2="9" y1="21" y2="9"></line>
                    </svg>
                  </button>
                </div>

              <div className="flex mb-3 gap-2 overflow-x-auto pb-1">
                <button
                  onClick={() => setActiveFilter('all')}
                  className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${activeFilter === 'all' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-800'}`}
                >
                  All
                </button>
                <button
                  onClick={() => setActiveFilter('creator')}
                  className={`px-3 py-1 rounded-full text-sm whitespace-nowrap transition-colors ${activeFilter === 'creator' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-800'}`}
                >
                  From {movie?.user_name || 'Creator'}
                </button>
              </div>

              <div className="space-y-3">
                {filteredVideos.map((video) => (
                  <div
                    key={video.media_id}
                    className="flex space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-all duration-200"
                    onClick={() => {
                      window.location.href = `/home/<USER>/${video.media_id}`;
                    }}
                  >
                    <div className="relative w-28 sm:w-32 md:w-36 h-16 sm:h-18 md:h-20 flex-shrink-0 rounded-lg overflow-hidden">
                      <Image
                        src={video.thumbnail_url || "/pics/placeholder.svg"}
                        alt={video.caption}
                        fill
                        className="object-cover"
                        sizes="128px"
                      />
                      {typeof video.duration === 'number' && video.duration > 0 && (
                        <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
                          {formatDuration(video.duration)}
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-medium line-clamp-2 text-black">{video.caption}</h3>
                      <p className="text-xs text-black mt-1">{video.user_name}</p>
                      <p className="text-xs text-gray-500">
                        {formatViewCount(video.views_count || 0)} views • {video.created_at || 'recently'}
                      </p>
                    </div>
                    <button className="text-gray-500 self-start mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="1"></circle>
                        <circle cx="12" cy="5" r="1"></circle>
                        <circle cx="12" cy="19" r="1"></circle>
                      </svg>
                    </button>
                  </div>
                ))}
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}
