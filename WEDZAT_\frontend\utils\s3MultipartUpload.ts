import axios from 'axios';

// Constants for multipart upload
const CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks (minimum size as requested)
const MAX_CONCURRENT_UPLOADS = 6; // Increased number of concurrent chunk uploads for better performance

export interface MultipartUploadProgress {
  totalBytes: number;
  uploadedBytes: number;
  percentage: number;
  speed: number; // bytes per second
}

export interface UploadedPart {
  PartNumber: number;
  ETag: string;
}

/**
 * Handles multipart upload of large files directly to S3 using provided part URLs
 */
export async function multipartUpload(
  file: File,
  partUrls: { part_number: number; url: string }[],
  onProgress?: (progress: MultipartUploadProgress) => void
): Promise<UploadedPart[]> {
  const totalParts = partUrls.length;
  let uploadedBytes = 0;
  let startTime = Date.now();
  let uploadSpeed = 0;
  const partSize = Math.ceil(file.size / totalParts);
  const uploadedParts: UploadedPart[] = [];

  for (let i = 0; i < totalParts; i++) {
    const { part_number, url } = partUrls[i];
    const start = (part_number - 1) * partSize;
    const end = i === totalParts - 1 ? file.size : start + partSize;
    const chunk = file.slice(start, end);
    try {
      const response = await axios.put(url, chunk, {
        headers: {
          'Content-Type': 'application/octet-stream',
          'Content-Length': chunk.size.toString(),
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        timeout: 300000,
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && onProgress) {
            uploadedBytes += progressEvent.loaded;
            const elapsedSeconds = (Date.now() - startTime) / 1000;
            uploadSpeed = elapsedSeconds > 0 ? uploadedBytes / elapsedSeconds : 0;
            onProgress({
              totalBytes: file.size,
              uploadedBytes,
              percentage: Math.round((uploadedBytes / file.size) * 100),
              speed: uploadSpeed
            });
          }
        }
      });
      const eTag = response.headers['etag'] || response.headers['ETag'];
      uploadedParts.push({ PartNumber: part_number, ETag: eTag?.replaceAll('"', '') || '' });
    } catch (error: any) {
      console.error(`Error uploading part ${part_number}:`, error.message);
      throw error;
    }
  }
  return uploadedParts;
}

/**
 * Uploads a single chunk to S3
 */
async function uploadChunk(
  chunk: Blob,
  baseUrl: string,
  queryParams: Record<string, string>,
  chunkNumber: number,
  totalChunks: number,
  onComplete: () => void
): Promise<void> {
  // Add chunk information to query parameters
  const chunkParams = {
    ...queryParams,
    partNumber: (chunkNumber + 1).toString(),
    uploadId: queryParams.uploadId || 'direct-upload',
  };

  // Build the URL with query parameters
  const queryString = Object.entries(chunkParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  const uploadUrl = `${baseUrl}?${queryString}`;

  try {
    // Upload the chunk
    console.log(`Uploading chunk ${chunkNumber + 1}/${totalChunks} (${(chunk.size / (1024 * 1024)).toFixed(2)}MB)`);

    try {
      // First try direct upload
      await axios.put(uploadUrl, chunk, {
        headers: {
          'Content-Type': 'application/octet-stream',
          'Content-Length': chunk.size.toString(),
          // Add CORS headers
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'PUT',
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        timeout: 300000, // 5 minutes timeout per chunk
      });

      console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via direct upload`);
      onComplete();
    } catch (directError: any) {
      // Check if this is a CORS error
      const isCorsError =
        directError.message.includes('CORS') ||
        directError.message.includes('Network Error') ||
        directError.message.includes('Failed to fetch') ||
        directError.message.includes('cross-origin');

      if (isCorsError) {
        console.log(`CORS error detected for chunk ${chunkNumber + 1}. Falling back to proxy upload.`);

        // Use the proxy API instead
        const proxyUrl = `/api/upload-proxy?url=${encodeURIComponent(uploadUrl)}`;

        // Create a FormData object to send the chunk
        const formData = new FormData();
        formData.append('file', chunk);

        // Send the chunk to our proxy API
        await axios.post(proxyUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          timeout: 600000, // 10 minutes timeout for proxy upload
        });

        console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via proxy`);
        onComplete();
      } else {
        // Not a CORS error, rethrow
        throw directError;
      }
    }
  } catch (error: any) {
    console.error(`Error uploading chunk ${chunkNumber + 1}/${totalChunks}:`, error.message);
    throw error;
  }
}
