"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx":
/*!*********************************************************!*\
  !*** ./app/home/<USER>/components/EInviteEditor.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Loader2,Search,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _templates_IndianTraditionalTemplate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templates/IndianTraditionalTemplate */ \"(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst EInviteEditor = (param)=>{\n    let { templates, einvite, onSave, onCancel, loading } = param;\n    var _einvite_design_settings;\n    _s();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.title) || \"Our Wedding E-Invite\");\n    const [weddingDate, setWeddingDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_date) || \"\");\n    const [weddingLocation, setWeddingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.wedding_location) || \"\");\n    const [aboutCouple, setAboutCouple] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.about_couple) || \"\");\n    const [coupleNames, setCoupleNames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.couple_names) || (einvite === null || einvite === void 0 ? void 0 : (_einvite_design_settings = einvite.design_settings) === null || _einvite_design_settings === void 0 ? void 0 : _einvite_design_settings.couple_names) || \"\");\n    const [designSettings, setDesignSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((einvite === null || einvite === void 0 ? void 0 : einvite.design_settings) || {\n        colors: {\n            primary: \"#B31B1E\",\n            secondary: \"#333333\",\n            background: \"#FFFFFF\",\n            text: \"#000000\"\n        },\n        fonts: {\n            heading: \"Playfair Display\",\n            body: \"Open Sans\",\n            coupleNames: \"Dancing Script\",\n            date: \"Open Sans\",\n            location: \"Open Sans\",\n            aboutCouple: \"Open Sans\"\n        },\n        customImage: \"\",\n        type: \"einvite\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('content');\n    const [uploadingImage, setUploadingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchingImages, setSearchingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fontOptions = [\n        \"Arial\",\n        \"Helvetica\",\n        \"Times New Roman\",\n        \"Georgia\",\n        \"Verdana\",\n        \"Playfair Display\",\n        \"Open Sans\",\n        \"Lato\",\n        \"Roboto\",\n        \"Dancing Script\",\n        \"Great Vibes\",\n        \"Pacifico\",\n        \"Lobster\",\n        \"Montserrat\",\n        \"Poppins\"\n    ];\n    const handleColorChange = (colorType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                colors: {\n                    ...prev.colors,\n                    [colorType]: value\n                }\n            }));\n    };\n    const handleFontChange = (fontType, value)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                fonts: {\n                    ...prev.fonts,\n                    [fontType]: value\n                }\n            }));\n    };\n    const handleImageUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        setUploadingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', file);\n            const response = await fetch('/api/upload-image', {\n                method: 'POST',\n                body: formData\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setDesignSettings((prev)=>({\n                        ...prev,\n                        customImage: data.imageUrl\n                    }));\n            } else {\n                console.error('Failed to upload image');\n            }\n        } catch (error) {\n            console.error('Error uploading image:', error);\n        } finally{\n            setUploadingImage(false);\n        }\n    };\n    const searchImages = async ()=>{\n        if (!searchQuery.trim()) return;\n        setSearchingImages(true);\n        try {\n            const response = await fetch(\"/api/search-images?q=\".concat(encodeURIComponent(searchQuery)));\n            if (response.ok) {\n                const data = await response.json();\n                setSearchResults(data.images || []);\n            }\n        } catch (error) {\n            console.error('Error searching images:', error);\n        } finally{\n            setSearchingImages(false);\n        }\n    };\n    const selectSearchImage = (imageUrl)=>{\n        setDesignSettings((prev)=>({\n                ...prev,\n                customImage: imageUrl\n            }));\n        setSearchResults([]);\n        setSearchQuery(\"\");\n    };\n    // Function to render the appropriate template\n    const renderTemplate = ()=>{\n        var _designSettings_couple_names, _designSettings_couple_names1, _selectedTemplate, _selectedTemplate1, _designSettings_colors, _designSettings_colors1, _designSettings_fonts, _designSettings_colors2, _designSettings_fonts1, _designSettings_colors3, _designSettings_fonts2, _designSettings_colors4, _designSettings_fonts3, _designSettings_colors5, _designSettings_fonts4;\n        const templateData = {\n            partner1_name: ((_designSettings_couple_names = designSettings.couple_names) === null || _designSettings_couple_names === void 0 ? void 0 : _designSettings_couple_names.split(' & ')[0]) || (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[0]) || 'Navneet',\n            partner2_name: ((_designSettings_couple_names1 = designSettings.couple_names) === null || _designSettings_couple_names1 === void 0 ? void 0 : _designSettings_couple_names1.split(' & ')[1]) || (coupleNames === null || coupleNames === void 0 ? void 0 : coupleNames.split(' & ')[1]) || 'Suknya',\n            wedding_date: weddingDate || 'Monday, 22th Aug 2022',\n            wedding_venue: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',\n            muhurtam_time: '7:00 PM',\n            reception_date: weddingDate || 'Monday, 23th Aug 2022',\n            reception_venue: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',\n            reception_time: '10:00 To 11:00 AM',\n            couple_photo: designSettings.customImage || '',\n            custom_message: aboutCouple || 'May your love story be as magical and charming as in fairy tales!',\n            colors: designSettings.colors,\n            fonts: designSettings.fonts\n        };\n        // Check if this is the Indian Traditional template\n        if (((_selectedTemplate = selectedTemplate) === null || _selectedTemplate === void 0 ? void 0 : _selectedTemplate.template_id) === 'indian-traditional-001' || ((_selectedTemplate1 = selectedTemplate) === null || _selectedTemplate1 === void 0 ? void 0 : _selectedTemplate1.name) === 'Indian Traditional') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_templates_IndianTraditionalTemplate__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: templateData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 194,\n                columnNumber: 14\n            }, undefined);\n        }\n        // Default template rendering for other templates\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative\",\n            style: {\n                backgroundImage: designSettings.customImage ? \"url(\".concat(designSettings.customImage, \")\") : 'none',\n                backgroundColor: ((_designSettings_colors = designSettings.colors) === null || _designSettings_colors === void 0 ? void 0 : _designSettings_colors.background) || '#FFFFFF',\n                backgroundSize: 'cover',\n                backgroundPosition: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 p-6 flex flex-col justify-center items-center text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors1 = designSettings.colors) === null || _designSettings_colors1 === void 0 ? void 0 : _designSettings_colors1.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts = designSettings.fonts) === null || _designSettings_fonts === void 0 ? void 0 : _designSettings_fonts.heading) || 'serif'\n                                },\n                                children: \"You are invited to celebrate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-2\",\n                                style: {\n                                    color: ((_designSettings_colors2 = designSettings.colors) === null || _designSettings_colors2 === void 0 ? void 0 : _designSettings_colors2.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts1 = designSettings.fonts) === null || _designSettings_fonts1 === void 0 ? void 0 : _designSettings_fonts1.coupleNames) || 'serif'\n                                },\n                                children: designSettings.couple_names || coupleNames || 'Couple Names'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold mb-1\",\n                                style: {\n                                    color: ((_designSettings_colors3 = designSettings.colors) === null || _designSettings_colors3 === void 0 ? void 0 : _designSettings_colors3.primary) || '#B31B1E',\n                                    fontFamily: ((_designSettings_fonts2 = designSettings.fonts) === null || _designSettings_fonts2 === void 0 ? void 0 : _designSettings_fonts2.date) || 'serif'\n                                },\n                                children: weddingDate || 'Wedding Date'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                style: {\n                                    color: ((_designSettings_colors4 = designSettings.colors) === null || _designSettings_colors4 === void 0 ? void 0 : _designSettings_colors4.text) || '#000000',\n                                    fontFamily: ((_designSettings_fonts3 = designSettings.fonts) === null || _designSettings_fonts3 === void 0 ? void 0 : _designSettings_fonts3.location) || 'sans-serif'\n                                },\n                                children: weddingLocation || 'Wedding Location'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined),\n                    aboutCouple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs italic\",\n                        style: {\n                            color: ((_designSettings_colors5 = designSettings.colors) === null || _designSettings_colors5 === void 0 ? void 0 : _designSettings_colors5.text) || '#000000',\n                            fontFamily: ((_designSettings_fonts4 = designSettings.fonts) === null || _designSettings_fonts4 === void 0 ? void 0 : _designSettings_fonts4.aboutCouple) || 'sans-serif'\n                        },\n                        children: aboutCouple\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleSave = ()=>{\n        var _templates_;\n        console.log('Save button clicked');\n        // Process colors to ensure they're valid hex codes\n        const processedColors = {\n            ...designSettings.colors\n        };\n        Object.keys(processedColors).forEach((key)=>{\n            const color = processedColors[key];\n            if (color && !color.startsWith('#')) {\n                processedColors[key] = \"#\".concat(color);\n            }\n        });\n        // Process fonts to ensure they're valid\n        const processedFonts = {\n            ...designSettings.fonts\n        };\n        Object.keys(processedFonts).forEach((key)=>{\n            if (!processedFonts[key]) {\n                processedFonts[key] = fontOptions[0];\n            }\n        });\n        // Get template_id and REJECT any \"default-\" IDs\n        let templateId = (einvite === null || einvite === void 0 ? void 0 : einvite.template_id) || ((_templates_ = templates[0]) === null || _templates_ === void 0 ? void 0 : _templates_.template_id);\n        // REJECT any \"default-\" template IDs completely\n        if (templateId && templateId.includes('default-')) {\n            var _templates_find;\n            console.error('REJECTING invalid template ID in editor:', templateId);\n            templateId = (_templates_find = templates.find((t)=>!t.template_id.includes('default-'))) === null || _templates_find === void 0 ? void 0 : _templates_find.template_id;\n        }\n        if (!templateId) {\n            alert('No valid template available. Please refresh the page.');\n            return;\n        }\n        // Prepare the data - EXACTLY like website editor\n        const einviteData = {\n            title,\n            template_id: templateId,\n            wedding_date: weddingDate || null,\n            wedding_location: weddingLocation,\n            about_couple: aboutCouple,\n            couple_names: coupleNames,\n            design_settings: {\n                colors: processedColors,\n                fonts: processedFonts,\n                // Use only the custom image from upload or search\n                customImage: designSettings.customImage || '',\n                // Store couple_names in design_settings for backward compatibility\n                couple_names: coupleNames,\n                // Add type marker for e-invites\n                type: 'einvite'\n            },\n            is_published: true\n        };\n        console.log('Saving e-invite data:', einviteData);\n        onSave(einviteData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onCancel,\n                            className: \"p-2 hover:bg-gray-100 rounded-md transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-gray-400 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-black\",\n                                    children: [\n                                        \"Our Most Trending \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#B31B1E]\",\n                                            children: \"Invites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto mb-6\",\n                        children: renderTemplate()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(activeTab === 'design' ? 'content' : 'design'),\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    activeTab === 'design' ? 'Hide Settings' : 'Colors & Background'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Resize\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Undo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onCancel,\n                                className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: \"Back to Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSave,\n                                disabled: loading,\n                                className: \"flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 16,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    \"Save & Continue Editing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === 'design' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: Object.entries(designSettings.colors || {}).map((param)=>{\n                                                let [colorType, colorValue] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-black mb-2 capitalize\",\n                                                            children: colorType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"color\",\n                                                                    value: colorValue || '#000000',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: colorValue || '',\n                                                                    onChange: (e)=>handleColorChange(colorType, e.target.value),\n                                                                    className: \"flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black\",\n                                                                    placeholder: \"#000000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, colorType, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 text-black\",\n                                            children: \"Background Image\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleImageUpload,\n                                                    className: \"hidden\",\n                                                    id: \"image-upload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"image-upload\",\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer\",\n                                                    children: [\n                                                        uploadingImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Loader2_Search_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        designSettings.customImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-black mb-2\",\n                                                    children: \"Current Background:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-32 h-20 bg-gray-200 rounded overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: designSettings.customImage,\n                                                        alt: \"Background\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInviteEditor.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInviteEditor, \"77H3G4EMQD+TN3xJ6g+tspSlyR4=\");\n_c = EInviteEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInviteEditor);\nvar _c;\n$RefreshReg$(_c, \"EInviteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\n"));

/***/ })

});