"use client";
import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { businessSubtypesMap } from '../../../../config/constants';

// Placeholder for fetching services by type and subtype
const fetchServices = async (type: string, subtype?: string) => {
  // Replace this with a real API call
  // For now, return mock data
  return [
    {
      service_id: '1',
      service_name: `${type} Example Service 1`,
      business_subtype_name: subtype || 'Subtype',
      location: 'Bengaluru',
      price_range: '3 Lakhs',
      capacity: 1500,
      amenities: ['AC-Hall', '1500-Chair', '10+ Rooms'],
      images: [
        'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80',
      ],
      description: 'A beautiful venue for your special day.',
    },
    {
      service_id: '2',
      service_name: `${type} Example Service 2`,
      business_subtype_name: subtype || 'Subtype',
      location: 'Bengaluru',
      price_range: '1 Lakh',
      capacity: 800,
      amenities: ['AC-Hall', '800-Chair'],
      images: [
        'https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80',
      ],
      description: 'Perfect for intimate gatherings.',
    },
  ];
};

export default function VendorTypePage() {
  const params = useParams();
  const type = params && typeof params === 'object' && 'type' in params && params.type ? String(params.type) : '';
  const subtypes = businessSubtypesMap[type] || [];
  const [selectedSubtype, setSelectedSubtype] = useState<string | undefined>(undefined);
  const [services, setServices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!type) return;
    setIsLoading(true);
    fetchServices(type, selectedSubtype).then((data) => {
      setServices(data);
      setIsLoading(false);
    });
  }, [type, selectedSubtype]);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">{type} Subtypes</h1>
      <div className="flex gap-2 mb-6 flex-wrap">
        {subtypes.map(sub => (
          <button
            key={sub.subtype_id}
            className={`p-2 border rounded ${selectedSubtype === sub.name ? 'bg-red-700 text-white' : 'bg-white'}`}
            onClick={() => setSelectedSubtype(sub.name)}
          >
            {sub.name}
          </button>
        ))}
        {subtypes.length > 0 && (
          <button
            className={`p-2 border rounded ${!selectedSubtype ? 'bg-red-700 text-white' : 'bg-white'}`}
            onClick={() => setSelectedSubtype(undefined)}
          >
            All
          </button>
        )}
      </div>
      <div>
        {isLoading ? (
          <p>Loading services...</p>
        ) : services.length === 0 ? (
          <p>No services found for this category.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {services.map(service => (
              <div key={service.service_id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="aspect-w-16 aspect-h-9 relative">
                  {service.images && service.images.length > 0 ? (
                    <img
                      src={service.images[0]}
                      alt={service.service_name}
                      width={400}
                      height={225}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="bg-gray-200 w-full h-full flex items-center justify-center">
                      <span className="text-gray-500">No image available</span>
                    </div>
                  )}
                  <div className="absolute top-2 left-2 bg-red-700 text-white text-xs px-2 py-1 rounded-md">
                    {service.business_subtype_name}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{service.service_name}</h3>
                  {service.description && (
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{service.description}</p>
                  )}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {service.price_range && (
                      <div className="text-sm">
                        <span className="font-medium">Price:</span> {service.price_range}
                      </div>
                    )}
                    {service.location && (
                      <div className="text-sm">
                        <span className="font-medium">Location:</span> {service.location}
                      </div>
                    )}
                    {service.capacity && (
                      <div className="text-sm">
                        <span className="font-medium">Capacity:</span> {service.capacity}
                      </div>
                    )}
                  </div>
                  {service.amenities && service.amenities.length > 0 && (
                    <div className="mb-3">
                      <h4 className="text-sm font-medium mb-1">Amenities:</h4>
                      <div className="flex flex-wrap gap-1">
                        {service.amenities.map((amenity: string, index: number) => (
                          <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                            {amenity}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 