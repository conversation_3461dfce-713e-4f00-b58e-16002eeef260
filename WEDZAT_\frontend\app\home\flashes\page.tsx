"use client";
import React, { useState, useEffect, useRef } from "react";
import { TopNavigation, SideNavigation, MobileNavigation } from "../../../components/HomeDashboard/Navigation";
import axios from "../../../services/axiosConfig";
import Image from "next/image";
import UserAvatar from "../../../components/HomeDashboard/UserAvatar";
import useSWR from 'swr';

// Updated interface for flash video items (matches new API response)
interface FlashVideo {
  media_id: string;
  media_url: string;
  thumbnail_url: string | null;
  caption?: string;
  location?: string;
  event_type?: string;
  video_type?: string;
  partner?: string;
  wedding_style?: string;
  budget?: string;
  created_at: string;
  user_id: string;
  user_name: string;
  user_avatar: string | null;
  is_own_content: boolean;
  followers_count: number;
  following_count: number;
  is_following: boolean;
  views_count: number;
  likes_count: number;
  is_liked: boolean;
}

interface ApiResponse {
  flashes: FlashVideo[];
  next_page: boolean;
  total_count: number;
  current_page: number;
  filtered_by_location?: string;
  location_matches_in_page?: number;
}

// SWR fetcher function
const fetcher = async (url: string, token: string) => {
  const response = await axios.get<ApiResponse>(url, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return response.data;
};

export default function FlashesPage() {
  const [flashes, setFlashes] = useState<FlashVideo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const userAvatarPlaceholders = [
    "/pics/1stim.jfif",
    "/pics/2ndim.jfif",
    "/pics/jpeg3.jfif",
    "/pics/user-profile.png",
    "/pics/user-profile.png",
  ];
  useEffect(() => setIsClient(true), []);
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
  const { data, error: swrError, isValidating } = useSWR(
    token ? [`/flashes?page=${page}&limit=10`, token] : null,
    ([url, token]) => fetcher(url, token),
    {
      revalidateOnFocus: false,
      dedupingInterval: 2 * 60 * 1000,
      keepPreviousData: true,
    }
  );
  useEffect(() => {
    setLoading(isValidating);
    setError(swrError ? 'Failed to load flashes' : null);
    if (data && data.flashes) {
      if (page === 1) {
        setFlashes(data.flashes);
      } else {
        setFlashes(prev => [...prev, ...data.flashes]);
      }
      setHasMore(data.next_page);
    }
  }, [data, swrError, isValidating, page]);
  const lastFlashRef = useRef<HTMLDivElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  useEffect(() => {
    if (observerRef.current) observerRef.current.disconnect();
    if (loading || !hasMore) return;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && !loading && hasMore) {
          setTimeout(() => {
            setPage(prevPage => prevPage + 1);
          }, 300);
        }
      },
      { threshold: 0.5, rootMargin: '0px 0px 200px 0px' }
    );
    const lastElement = lastFlashRef.current;
    if (lastElement) observer.observe(lastElement);
    observerRef.current = observer;
    return () => { if (observerRef.current) observerRef.current.disconnect(); };
  }, [loading, hasMore, flashes.length]);
  // Get appropriate image source for a flash
  const getImageSource = (flash: FlashVideo): string => {
    if (flash.thumbnail_url) return flash.thumbnail_url;
    return '/pics/placeholder.svg';
  };
  return (
    <div className={isClient ? "flex flex-col min-h-screen bg-white w-full" : "opacity-0"}>
      {/* Top Navigation Bar */}
      <TopNavigation />

      <div className="flex flex-1 bg-white">
        <SideNavigation
          expanded={sidebarExpanded}
          onExpand={() => setSidebarExpanded(true)}
          onCollapse={() => setSidebarExpanded(false)}
        />

        {/* Main Content */}
        <main
          className={`flex-1 p-4 bg-white mt-20 ${sidebarExpanded ? "md:ml-48" : "md:ml-20"}`}
          style={{
            transition: "all 300ms ease-in-out",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">Flashes</h1>

            {/* Loading state for initial load */}
            {loading && page === 1 && (
              <div className="py-10 text-center flex items-center justify-center">
                <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-600">Loading flashes</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="py-10 text-center text-red-500">{error}</div>
            )}

            {/* Content */}
            {flashes.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {flashes.map((flash, index) => {
                  const isLastItem = index === flashes.length - 1;
                  return (
                  <div
                    key={`${flash.media_id}-${index}`}
                    ref={isLastItem ? lastFlashRef : null}
                    onClick={() => {
                      window.location.href = `/home/<USER>/shorts?index=${index}`;
                    }}
                    style={{
                      width: "100%",
                      height: "308px",
                      position: "relative",
                      borderRadius: "10px",
                      border: "1px solid #B31B1E",
                      padding: "10px",
                      background:
                        "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                      cursor: "pointer",
                      overflow: "hidden",
                      transition: "transform 0.2s ease",
                    }}
                    className="hover:scale-105"
                  >
                    <div
                      style={{
                        position: "absolute",
                        top: "10px",
                        left: "10px",
                        zIndex: 2,
                      }}
                    >
                      <UserAvatar
                        username={flash.user_name || "user"}
                        size="sm"
                        isGradientBorder={true}
                        imageUrl={userAvatarPlaceholders[index % userAvatarPlaceholders.length]}
                      />
                    </div>

                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        zIndex: 1,
                      }}
                    >
                      <div className="relative w-full h-full">
                        <Image
                          src={getImageSource(flash)}
                          alt={flash.caption || flash.media_id}
                          fill
                          sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 16vw"
                          className="object-cover"
                          priority={index < 6}
                          unoptimized={!flash.thumbnail_url}
                          onError={(e) => {
                            const imgElement = e.target as HTMLImageElement;
                            if (imgElement) {
                              imgElement.src = '/pics/placeholder.svg';
                            }
                          }}
                        />
                      </div>
                    </div>

                    <div
                      style={{
                        position: "absolute",
                        bottom: "10px",
                        left: "10px",
                        right: "10px",
                        zIndex: 2,
                        color: "white",
                      }}
                    >
                      <div style={{ fontSize: "14px", fontWeight: 500 }}>
                        {flash.caption}
                      </div>
                      <div style={{ fontSize: "12px" }}>
                        {flash.location ? `${flash.location}` : ''}
                        {flash.views_count ? ` • ${flash.views_count} views` : ''}
                        {flash.likes_count ? ` • ${flash.likes_count} likes` : ''}
                      </div>
                    </div>
                  </div>
                  );
                })}
              </div>
            )}

            {/* Loading more indicator - subtle version */}
            {loading && page > 1 && (
              <div className="py-4 text-center flex items-center justify-center">
                <div className="inline-block w-5 h-5 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                <span className="text-gray-500 text-sm">Loading more</span>
              </div>
            )}

            {/* No more content indicator */}
            {!loading && !hasMore && flashes.length > 0 && (
              <div className="py-6 text-center text-gray-500">No more flashes to load</div>
            )}

            {/* No content state */}
            {!loading && flashes.length === 0 && !error && (
              <div className="py-10 text-center text-gray-500">No flashes available</div>
            )}
          </div>
        </main>
      </div>

      <MobileNavigation />
    </div>
  );
}
