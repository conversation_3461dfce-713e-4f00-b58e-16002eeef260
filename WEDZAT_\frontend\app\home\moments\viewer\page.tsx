"use client";
import React, { useState, useEffect, useRef, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "../../../../services/axiosConfig";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import UserAvatar from "../../../../components/HomeDashboard/UserAvatar";
import {
  TopNavigation,
  SideNavigation,
} from "../../../../components/HomeDashboard/Navigation";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  user_id: string;
  user_avatar?: string;
  content_type: "video" | "photo";
  is_own_content: boolean;
  viewed?: boolean;
  // API fields for admire/like/follow
  followers_count?: number;
  following_count?: number;
  is_following?: boolean;
  views_count?: number;
  is_liked?: boolean;
  likes_count?: number;
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

// Create a Client component
function MomentsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isClient, setIsClient] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);
  const [videoPlayStates, setVideoPlayStates] = useState<Record<string, boolean>>({});
  const [admiringUsers, setAdmiringUsers] = useState<{ [key: string]: boolean }>({});
  const [contentLoading, setContentLoading] = useState<{ [key: string]: boolean }>({});
  const [likeStates, setLikeStates] = useState<{ [key: string]: boolean }>({});
  const [likeCounts, setLikeCounts] = useState<{ [key: string]: number }>({});
  const [viewCounts, setViewCounts] = useState<{ [key: string]: number }>({});
  const [isLiking, setIsLiking] = useState(false);
  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [storyProgress, setStoryProgress] = useState<{ [key: string]: number }>({});

  // User avatar placeholders - using placeholder.svg which exists
  const userAvatarPlaceholders = [
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
    "/pics/placeholder.svg",
  ];

  // Remove localStorage initialization - always fetch fresh data

  const containerRef = useRef<HTMLDivElement>(null);
  const videoRefs = useRef<Record<string, HTMLVideoElement>>({});

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch stories from the API without caching
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        // Get token from localStorage
        const token = localStorage.getItem("token");

        if (!token) {
          console.warn("No authentication token found");
          setError("Authentication required");
          return;
        }

        const response = await axios.get<ApiResponse>(
          `/stories?page=${page}&limit=10`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data && response.data.stories) {
          console.log("Stories API response:", response.data);

          // Process the stories to ensure thumbnails are properly formatted
          const processedStories = response.data.stories.map((story) => {
            // For photos, ensure we're using content_url directly
            // For videos, use thumbnail_url if available, otherwise generate one
            const thumbnailUrl =
              story.content_type === "photo"
                ? story.content_url
                : story.thumbnail_url || getDefaultThumbnail(story);

            return {
              ...story,
              viewed: true, // Mark as viewed since we're viewing it
              thumbnail_url: thumbnailUrl,
              // Ensure content_url is also set correctly
              content_url: story.content_url || thumbnailUrl,
            };
          });

          // Remove filtering to only one story per user
          // Do NOT sort or prioritize user's own story; just append as received
          setStories(processedStories);

          // Initialize admiringUsers state from is_following
          const newAdmiringUsers: { [key: string]: boolean } = {};
          processedStories.forEach((story) => {
            if (story.user_id && typeof story.is_following !== 'undefined') {
              newAdmiringUsers[story.user_id] = !!story.is_following;
            }
          });
          setAdmiringUsers((prev) => ({ ...prev, ...newAdmiringUsers }));

          // Remove all uniqueStories logic and just use processedStories
          if (page === 1) {
            // Initialize loading state for all content
            const initialLoadingState: { [key: string]: boolean } = {};
            processedStories.forEach((story: Story) => {
              initialLoadingState[story.content_id] = true;
            });
            setContentLoading(initialLoadingState);
          } else {
            setStories((prev) => {
              const newStories = [...prev, ...processedStories];
              // Initialize loading state for new content
              setContentLoading(prev => {
                const newState = { ...prev };
                processedStories.forEach((story: Story) => {
                  newState[story.content_id] = true;
                });
                return newState;
              });
              return newStories;
            });
          }

          setHasMore(response.data.next_page);
        } else {
          console.warn("Unexpected API response format:", response.data);
          setError("Failed to load moments");
        }
      } catch (err) {
        console.error("Error fetching stories:", err);
        setError("Failed to load moments");
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, [page, stories.length]);

  // Load more stories when reaching the end
  useEffect(() => {
    if (currentIndex >= stories.length - 2 && hasMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [currentIndex, stories.length, hasMore, loading]);

  // Auto-play videos when they become the current item
  useEffect(() => {
    if (!isClient || stories.length === 0) return;
    Object.values(videoRefs.current).forEach(video => video && video.pause());
    const currentStory = stories[currentIndex];
    if (currentStory?.content_type === "video") {
      const video = videoRefs.current[currentStory.content_id];
      if (video) {
        video.currentTime = 0;
        video.muted = false;
        video.play().catch(e => console.log("Autoplay blocked:", e));
      }
    }
  }, [currentIndex, stories, isClient]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isClient) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft" || e.key === "ArrowUp") {
        navigateToPrevious();
      } else if (e.key === "ArrowRight" || e.key === "ArrowDown") {
        navigateToNext();
      } else if (e.key === "Escape") {
        router.push("/home/<USER>");
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isClient, currentIndex, stories.length, router]);

  // Handle touch events for mobile swipe
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;

    const handleTouchStart = (e: TouchEvent) => {
      touchStartX = e.changedTouches[0].screenX;
      touchStartY = e.changedTouches[0].screenY;
    };

    const handleTouchEnd = (e: TouchEvent) => {
      touchEndX = e.changedTouches[0].screenX;
      touchEndY = e.changedTouches[0].screenY;

      // Calculate horizontal and vertical distance
      const horizontalDistance = touchEndX - touchStartX;
      const verticalDistance = touchEndY - touchStartY;

      // Only handle horizontal swipes if they're more significant than vertical movement
      if (Math.abs(horizontalDistance) > Math.abs(verticalDistance)) {
        if (horizontalDistance > 50) {
          // Swipe right - go to previous
          navigateToPrevious();
        } else if (horizontalDistance < -50) {
          // Swipe left - go to next
          navigateToNext();
        }
      }
    };

    const container = containerRef.current;
    container.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    container.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isClient, currentIndex, stories.length]);

  // Handle wheel events for touchpad scrolling
  useEffect(() => {
    if (!containerRef.current || !isClient) return;

    const handleWheel = (e: WheelEvent) => {
      // Debounce the wheel event to prevent too many navigations
      if (e.deltaX > 50) {
        // Scroll right - go to next
        navigateToNext();
      } else if (e.deltaX < -50) {
        // Scroll left - go to previous
        navigateToPrevious();
      }
    };

    const container = containerRef.current;
    container.addEventListener("wheel", handleWheel, { passive: true });

    return () => {
      container.removeEventListener("wheel", handleWheel);
    };
  }, [isClient, currentIndex, stories.length]);

  // Auto-play functionality for images and videos
  useEffect(() => {
    if (!isClient || stories.length === 0) return;
    const story = stories[currentIndex];
    if (!story) return;

    let interval: NodeJS.Timeout | null = null;
    let autoAdvanceTimeout: NodeJS.Timeout | null = null;
    let removeVideoEndedListener: (() => void) | null = null;

    if (story.content_type === 'photo') {
      // For images, fill over 10 seconds and auto-advance
      let start = Date.now();
      let duration = 10000; // 10s
      setStoryProgress((prev) => ({ ...prev, [story.content_id]: 0 }));
      interval = setInterval(() => {
        const elapsed = Date.now() - start;
        let percent = Math.min((elapsed / duration) * 100, 100);
        setStoryProgress((prev) => ({ ...prev, [story.content_id]: percent }));
        if (percent >= 100 && interval) clearInterval(interval);
      }, 100);
      // Auto-advance after 10s
      autoAdvanceTimeout = setTimeout(() => {
        navigateToNext();
      }, 10000);
    } else if (story.content_type === 'video') {
      // For videos, use currentTime/duration and auto-advance on ended
      const videoEl = videoRefs.current[story.content_id];
      if (videoEl) {
        const updateProgress = () => {
          if (!videoEl.duration) return setStoryProgress((prev) => ({ ...prev, [story.content_id]: 0 }));
          let percent = (videoEl.currentTime / videoEl.duration) * 100;
          setStoryProgress((prev) => ({ ...prev, [story.content_id]: percent }));
        };
        videoEl.addEventListener('timeupdate', updateProgress);
        // Initial set
        updateProgress();
        // Auto-advance on ended
        const handleEnded = () => navigateToNext();
        videoEl.addEventListener('ended', handleEnded);
        removeVideoEndedListener = () => {
          videoEl.removeEventListener('timeupdate', updateProgress);
          videoEl.removeEventListener('ended', handleEnded);
        };
      }
    }
    return () => {
      if (interval) clearInterval(interval);
      if (autoAdvanceTimeout) clearTimeout(autoAdvanceTimeout);
      if (removeVideoEndedListener) removeVideoEndedListener();
    };
  }, [currentIndex, stories, isClient]);

  const navigateToNext = () => {
    if (currentIndex < stories.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
    } else {
      // Loop back to the first moment when reaching the end
      setCurrentIndex(0);
    }
  };

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return "/pics/placeholder.svg";

    // If it's already a local URL, return as is
    if (url.startsWith("/")) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes("cloudfront.net") || url.includes("amazonaws.com")) {
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === "video") {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes("youtube")) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return "";

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes("/")) return url;

    // Try to extract ID from YouTube URL
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : "";
  };

  // Format date to a readable string
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Add custom CSS for mobile responsiveness
  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      .moments-page {
        background-color: #f8f8f8;
      }

      .moments-container {
        background-color: #000;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        border-radius: 12px;
        overflow: hidden;
        position: relative;
      }

      /* Mobile responsive styles */
      @media (max-width: 768px) {
        .moments-page {
          background-color: #000;
          padding-bottom: env(safe-area-inset-bottom, 20px) !important;
        }

        .moments-container {
          width: 100vw !important;
          max-width: none !important;
          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;
          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;
          max-height: none !important;
          border-radius: 0 !important;
          border: none !important;
          margin: 0 !important;
          padding-bottom: 20px !important;
        }

        .mobile-moment-item {
          margin-bottom: 10px !important;
          border-radius: 8px !important;
          overflow: hidden !important;
        }

        .mobile-nav-buttons {
          position: fixed !important;
          left: 16px !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          z-index: 50 !important;
          display: flex !important;
          flex-direction: column !important;
          gap: 20px !important;
        }

        .mobile-nav-button {
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(255, 255, 255, 0.9) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-nav-button:disabled {
          background: rgba(255, 255, 255, 0.3) !important;
          color: rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-interaction-buttons {
          position: fixed !important;
          right: 16px !important;
          bottom: calc(120px + env(safe-area-inset-bottom, 20px)) !important;
          z-index: 50 !important;
          display: flex !important;
          flex-direction: column !important;
          gap: 20px !important;
        }

        .mobile-interaction-button {
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(0, 0, 0, 0.6) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          color: white !important;
        }

        .mobile-back-button {
          position: fixed !important;
          top: 20px !important;
          left: 16px !important;
          z-index: 50 !important;
          width: 48px !important;
          height: 48px !important;
          border-radius: 50% !important;
          background: rgba(255, 255, 255, 0.9) !important;
          backdrop-filter: blur(10px) !important;
          border: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .mobile-user-info {
          position: absolute !important;
          top: 20px !important;
          left: 80px !important;
          right: 16px !important;
          z-index: 50 !important;
          background: rgba(0, 0, 0, 0.6) !important;
          backdrop-filter: blur(10px) !important;
          border-radius: 12px !important;
          padding: 12px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
        }

        .mobile-progress-indicator {
          position: fixed !important;
          top: 80px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          z-index: 50 !important;
        }

        /* Hide desktop navigation buttons on mobile */
        .desktop-nav-buttons {
          display: none !important;
        }

        .desktop-interaction-buttons {
          display: none !important;
        }

        /* Ensure main content takes full space on mobile */
        .main-content-mobile {
          padding: 0 !important;
          padding-bottom: env(safe-area-inset-bottom, 20px) !important;
          margin: 0 !important;
          width: 100vw !important;
          height: calc(100vh - env(safe-area-inset-bottom, 20px)) !important;
          height: calc(100dvh - env(safe-area-inset-bottom, 20px)) !important;
          position: relative !important;
        }
      }

      /* Desktop styles */
      @media (min-width: 769px) {
        .mobile-nav-buttons,
        .mobile-interaction-buttons,
        .mobile-back-button,
        .mobile-user-info,
        .mobile-progress-indicator {
          display: none !important;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Initialize like/view state from stories
  useEffect(() => {
    if (stories.length > 0) {
      const newLikeStates: { [key: string]: boolean } = {};
      const newLikeCounts: { [key: string]: number } = {};
      const newViewCounts: { [key: string]: number } = {};
      stories.forEach((story) => {
        newLikeStates[story.content_id] = !!story.is_liked;
        newLikeCounts[story.content_id] = story.likes_count || 0;
        newViewCounts[story.content_id] = story.views_count || 0;
      });
      setLikeStates(newLikeStates);
      setLikeCounts(newLikeCounts);
      setViewCounts(newViewCounts);
    }
  }, [stories]);

  // Trigger view API when story changes
  useEffect(() => {
    const story = stories[currentIndex];
    if (!story) return;
    const token = localStorage.getItem("token");
    if (!token) return;
    // Only trigger if not already viewed in this session
    if (sessionStorage.getItem(`viewed_${story.content_id}`)) return;
    sessionStorage.setItem(`viewed_${story.content_id}`, "1");
    axios.post(
      "/view",
      { content_id: story.content_id, content_type: story.content_type },
      { headers: { Authorization: `Bearer ${token}` } }
    ).then(() => {
      setViewCounts((prev) => ({
        ...prev,
        [story.content_id]: (prev[story.content_id] || 0) + 1,
      }));
    }).catch((err) => {
      // Ignore errors for view
      console.error("View API error", err);
    });
  }, [currentIndex, stories]);

  // Like/unlike handler
  const handleLikeToggle = async (contentId: string, contentType: string) => {
    if (isLiking) return;
    setIsLiking(true);
    const liked = likeStates[contentId];
    const token = localStorage.getItem("token");
    if (!token) {
      setIsLiking(false);
      return;
    }
    // Optimistic update
    setLikeStates((prev) => ({ ...prev, [contentId]: !liked }));
    setLikeCounts((prev) => ({ ...prev, [contentId]: prev[contentId] + (liked ? -1 : 1) }));
    try {
      const endpoint = liked ? "/unlike" : "/like";
      await axios.post(
        endpoint,
        { content_id: contentId, content_type: contentType },
        { headers: { Authorization: `Bearer ${token}` } }
      );
    } catch (err) {
      // Revert on error
      setLikeStates((prev) => ({ ...prev, [contentId]: liked }));
      setLikeCounts((prev) => ({ ...prev, [contentId]: prev[contentId] + (liked ? 1 : -1) }));
      alert("Failed to update like. Please try again.");
    } finally {
      setIsLiking(false);
    }
  };

// Accept a query param for initial moment index or content_id (set only once)
const hasInitializedIndex = useRef(false);

useEffect(() => {
  if (stories.length > 0 && !hasInitializedIndex.current) {
    const contentIdParam = searchParams?.get("content_id");
    const initialIndexParam = searchParams?.get("index");
    let indexValue = 0;
    if (contentIdParam) {
      const foundIndex = stories.findIndex(s => s.content_id === contentIdParam);
      if (foundIndex !== -1) {
        indexValue = foundIndex;
      }
    } else if (initialIndexParam) {
      const parsed = parseInt(initialIndexParam, 10);
      if (!isNaN(parsed) && parsed >= 0 && parsed < stories.length) {
        indexValue = parsed;
      }
    }
    // Do not auto-select user's own story first; always use content_id, index, or default to 0
    setCurrentIndex(indexValue);
    hasInitializedIndex.current = true;
  }
}, [stories.length, searchParams]);

  // Update the URL to reflect the current story's content_id as the user navigates
useEffect(() => {
  if (stories.length > 0 && isClient) {
    const currentStory = stories[currentIndex];
    if (currentStory) {
      const params = new URLSearchParams(Array.from(searchParams?.entries() || []));
      params.set("content_id", currentStory.content_id);
      // Remove index param if present
      params.delete("index");
      const newUrl = `/home/<USER>/viewer?${params.toString()}`;
      // Use replace to avoid pushing to history stack
      router.replace(newUrl);
    }
  }
  // eslint-disable-next-line react-hooks/exhaustive-deps
}, [currentIndex, stories.length, isClient]);

  if (!isClient) {
    return null; // Don't render anything on the server
  }

  return (
    <div className="min-h-screen bg-white flex flex-col moments-page">
      {/* Top Navigation - Hidden on mobile */}
      <div className="hidden md:block">
        <TopNavigation />
      </div>

      <div className="flex flex-1">
        {/* Left Sidebar - Hidden on mobile */}
        <div className="hidden md:block">
          <SideNavigation
            expanded={sidebarExpanded}
            onExpand={() => setSidebarExpanded(true)}
            onCollapse={() => setSidebarExpanded(false)}
          />
        </div>

        {/* Desktop Back Button */}
        <button
          onClick={() => window.location.href = "/home"}
          className={`fixed top-24 z-50 bg-white rounded-full p-3 text-black shadow-lg hover:bg-gray-200 transition-colors flex items-center justify-center hidden md:flex ${
            sidebarExpanded ? "left-52" : "left-24"
          }`}
          style={{
            width: "48px",
            height: "48px",
            transition: "left 300ms ease-in-out"
          }}
          title="Back to Home"
        >
          <X size={24} />
        </button>

        {/* Mobile Back Button */}
        <button
          onClick={() => window.location.href = "/home"}
          className="mobile-back-button md:hidden"
          title="Back to Home"
        >
          <X size={24} />
        </button>

        {/* Main Content - Single Moment View */}
        <main
          className={`flex-1 flex items-center justify-center px-4 md:px-4 px-0 relative main-content-mobile ${
            sidebarExpanded ? "md:ml-48" : "md:ml-20"
          }`}
          style={{
            transition: "all 300ms ease-in-out",
            paddingTop: "100px", // Account for TopNav (80px) + extra spacing (20px)
            paddingBottom: "20px",
            paddingLeft: "20px",
            paddingRight: "20px",
            minHeight: "100vh",
          }}
        >
          <div className="flex items-center w-full md:w-auto">
            {/* Desktop Manual Navigation Buttons - Left Side */}
            <div className="desktop-nav-buttons flex flex-col items-center justify-center space-y-4 mr-4 hidden md:flex">
              {/* Previous button */}
              <button
                onClick={navigateToPrevious}
                disabled={currentIndex === 0}
                className={`p-3 rounded-full transition-all duration-200 ${
                  currentIndex === 0
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl"
                }`}
                title="Previous Moment"
              >
                <ChevronLeft size={24} />
              </button>

              {/* Next button */}
              <button
                onClick={navigateToNext}
                disabled={currentIndex === stories.length - 1}
                className={`p-3 rounded-full transition-all duration-200 ${
                  currentIndex === stories.length - 1
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-100 shadow-lg hover:shadow-xl"
                }`}
                title="Next Moment"
              >
                <ChevronRight size={24} />
              </button>
            </div>

            <div
              ref={containerRef}
              className="moments-container relative w-full md:w-[400px] h-full md:h-[calc(100vh-140px)]"
              style={{
                // Desktop styles
                ...(typeof window !== 'undefined' && window.innerWidth >= 768 ? {
                  width: "400px",
                  height: "calc(100vh - 140px)",
                  maxHeight: "600px",
                  margin: "0 auto",
                  overflow: "hidden",
                } : {
                  // Mobile styles - full screen with safe area
                  width: "100vw",
                  height: "calc(100vh - env(safe-area-inset-bottom, 20px))",
                  margin: "0",
                  overflow: "hidden",
                  paddingBottom: "env(safe-area-inset-bottom, 20px)",
                })
              }}
            >
            {/* Loading state */}
            {loading && stories.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-gray-800 bg-white">
                <div className="animate-spin rounded-full h-10 w-10 border-2 border-red-600 border-t-transparent mr-3"></div>
                <span>Loading moments...</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && stories.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center text-red-600 bg-white p-4 text-center">
                <div>
                  <div className="mb-2 font-medium">Error</div>
                  <div>{error}</div>
                </div>
              </div>
            )}

            {/* Single Moment View */}
            {stories.length > 0 && (
              <div
                className="h-full w-full relative rounded-[12px] border-2 border-[#B31B1E] overflow-hidden"
                style={{
                  background: "linear-gradient(180deg, rgba(113, 113, 113, 0) 71.27%, #393939 100%)",
                }}
              >
                {(() => {
                  const story = stories[currentIndex];
                  if (!story) return null;

                  return (
                    <div className="h-full w-full relative">
                      {/* Time Bar Overlay - always at the very top of the moment */}
                      {(storyProgress[story.content_id] !== undefined) && (
                        <div
                          className="absolute left-0 top-0 w-full z-50"
                          style={{ height: '5px', background: 'rgba(0,0,0,0.15)' }}
                        >
                          <div
                            style={{
                              width: `${storyProgress[story.content_id]}%`,
                              height: '100%',
                              background: '#B31B1E',
                              transition: 'width 0.1s linear',
                              borderRadius: '2px',
                            }}
                          />
                        </div>
                      )}

                      {/* Main image/video content */}
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          zIndex: 1,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          background: "#fff" // Card background is white
                        }}
                      >
                        {story.content_type === "video" ? (
                          <div className="relative w-full h-full flex items-center justify-center bg-white">
                            {/* Video Loading Indicator */}
                            {contentLoading[story.content_id] !== false && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-20">
                                <div className="flex flex-col items-center text-white">
                                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2"></div>
                                  <div className="text-sm">Loading video...</div>
                                </div>
                              </div>
                            )}
                            <video
                              ref={(el) => {
                                if (el) videoRefs.current[story.content_id] = el;
                              }}
                              src={story.content_url}
                              className="w-full h-full bg-white"
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover', // Ensures video fills both height and width
                                background: '#fff',
                                borderRadius: 0,
                              }}
                              loop={false}
                              playsInline
                              controls={false}
                              muted={false}
                              onLoadStart={() => {
                                setContentLoading(prev => ({ ...prev, [story.content_id]: true }));
                              }}
                              onCanPlay={(e) => {
                                setContentLoading(prev => ({ ...prev, [story.content_id]: false }));
                                const video = e.target as HTMLVideoElement;
                                video.muted = false;
                                video.play().catch(err => console.log('onCanPlay autoplay blocked:', err));
                              }}
                              onClick={(e?: React.MouseEvent) => {
                                if (e) {
                                  e.stopPropagation();
                                }
                                const video = e?.target as HTMLVideoElement;
                                if (video) {
                                  video.muted = false;
                                  video.play();
                                  setVideoPlayStates((prev) => ({
                                    ...prev,
                                    [story.content_id]: true,
                                  }));
                                }
                              }}
                              onError={() => {
                                console.error(`Failed to load video: ${story.content_url}`);
                                setContentLoading(prev => ({ ...prev, [story.content_id]: false }));
                              }}
                            />
                          </div>
                        ) : (
                          <div className="relative w-full h-full flex items-center justify-center">
                            {/* Image Loading Indicator */}
                            {contentLoading[story.content_id] !== false && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-20">
                                <div className="flex flex-col items-center text-white">
                                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-2"></div>
                                  <div className="text-sm">Loading image...</div>
                                </div>
                              </div>
                            )}
                            <Image
                              src={processImageUrl(story.content_url)}
                              alt={story.content_name || "Moment"}
                              fill
                              sizes="100vw"
                              className="object-contain bg-black"
                              priority={true}
                              unoptimized={true}
                              placeholder="blur"
                              blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImciPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iMjAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNmZmYiIG9mZnNldD0iNTAlIiAvPjxzdG9wIHN0b3AtY29sb3I9IiNlZWUiIG9mZnNldD0iNzAlIiAvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI3MDAiIGhlaWdodD0iNDc1IiBmaWxsPSIjZWVlIiAvPjxyZWN0IGlkPSJyIiB3aWR0aD0iNzAwIiBoZWlnaHQ9IjQ3NSIgZmlsbD0idXJsKCNnKSIgLz48YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTcwMCIgdG89IjcwMCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPjwvc3ZnPg=="
                              onLoad={() => {
                                setContentLoading(prev => ({ ...prev, [story.content_id]: false }));
                              }}
                              onError={(e) => {
                                const imgElement = e.target as HTMLImageElement;
                                if (imgElement) {
                                  imgElement.src = '/pics/placeholder.svg';
                                }
                                setContentLoading(prev => ({ ...prev, [story.content_id]: false }));
                              }}
                            />
                          </div>
                        )}
                      </div>

                      {/* Desktop User info at top with profile and admire */}
                      <div className="absolute top-4 left-4 right-4 z-30 flex items-center bg-black/20 backdrop-blur-sm rounded-lg p-2 hidden md:flex">
                        <div 
                          className="flex items-center cursor-pointer"
                          onClick={(e?: React.MouseEvent) => {
                            if (e) {
                              e.stopPropagation();
                            }
                            if (story.user_id) {
                              router.push(`/profile/${story.user_id}`);
                            } else if (story.user_name) {
                              router.push(`/profile/${story.user_name}`);
                            }
                          }}
                        >
                          <UserAvatar
                            username={story.user_name || "user"}
                            size="sm"
                            isGradientBorder={true}
                            imageUrl={story.user_avatar}
                            onClick={(e?: React.MouseEvent) => {
                              if (e) {
                                e.stopPropagation();
                              }
                              if (story.user_id) {
                                router.push(`/profile/${story.user_id}`);
                              } else if (story.user_name) {
                                router.push(`/profile/${story.user_name}`);
                              }
                            }}
                          />
                          <div className="ml-2 text-white">
                            <div className="text-sm font-medium">{story.user_name || "user"}</div>
                          </div>
                        </div>

                        {/* Admire button */}
                        {!story.is_own_content && (
                          <button
                            className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                            onClick={(e?: React.MouseEvent) => {
                              if (e) {
                                e.stopPropagation();
                              }
                              // Handle admire functionality
                              const userId = story.user_id; // Use user_id for API call
                              if (!userId) return;

                              // Check if already admiring this user
                              const isCurrentlyAdmiring = admiringUsers[userId] || false;

                              // Optimistically update UI state
                              setAdmiringUsers(prev => ({
                                ...prev,
                                [userId]: !isCurrentlyAdmiring
                              }));

                              // Update localStorage
                              try {
                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
                                const admiredUsers = JSON.parse(admiredUsersJson);

                                if (!isCurrentlyAdmiring) {
                                  admiredUsers[userId] = true;
                                } else {
                                  delete admiredUsers[userId];
                                }

                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
                              } catch (error) {
                                console.error('Error updating localStorage:', error);
                              }

                              // Make API call in the background
                              const token = localStorage.getItem('token') ||
                                localStorage.getItem('jwt_token') ||
                                localStorage.getItem('wedzat_token');

                              if (token) {
                                // Desktop admire button handler
                                const endpoint = isCurrentlyAdmiring
                                  ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
                                  : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

                                axios.post(
                                  endpoint,
                                  { target_user_id: userId },
                                  { headers: { Authorization: `Bearer ${token}` } }
                                ).catch(error => {
                                  console.error('Error with admire API call:', error);
                                  // Revert UI state on error
                                  setAdmiringUsers(prev => ({
                                    ...prev,
                                    [userId]: isCurrentlyAdmiring
                                  }));
                                });
                              }
                            }}
                          >
                            {admiringUsers[story.user_id || ''] ? 'Admiring' : 'Admire'}
                            {!admiringUsers[story.user_id || ''] && <span className="ml-1">+</span>}
                          </button>
                        )}
                      </div>

                      {/* Mobile User info at top - Only show for current flash */}
                      <div className="mobile-user-info md:hidden">
                        <div 
                          className="flex items-center cursor-pointer"
                          onClick={(e?: React.MouseEvent) => {
                            if (e) {
                              e.stopPropagation();
                            }
                            if (story.user_id) {
                              router.push(`/profile/${story.user_id}`);
                            } else if (story.user_name) {
                              router.push(`/profile/${story.user_name}`);
                            }
                          }}
                        >
                          <UserAvatar
                            username={story.user_name || "user"}
                            size="sm"
                            isGradientBorder={true}
                            imageUrl={story.user_avatar}
                            onClick={(e?: React.MouseEvent) => {
                              if (e) {
                                e.stopPropagation();
                              }
                              if (story.user_id) {
                                router.push(`/profile/${story.user_id}`);
                              } else if (story.user_name) {
                                router.push(`/profile/${story.user_name}`);
                              }
                            }}
                          />
                          <div className="ml-2 text-white">
                            <div className="text-sm font-medium">{story.user_name || "user"}</div>
                          </div>
                        </div>

                        {/* Admire button */}
                        {!story.is_own_content && (
                          <button
                            className="ml-auto bg-[#B31B1E] text-white text-xs font-medium px-3 py-1 rounded-full flex items-center"
                            onClick={(e?: React.MouseEvent) => {
                              if (e) {
                                e.stopPropagation();
                              }
                              // Handle admire functionality
                              const userId = story.user_id; // Use user_id for API call
                              if (!userId) return;

                              // Check if already admiring this user
                              const isCurrentlyAdmiring = admiringUsers[userId] || false;

                              // Optimistically update UI state
                              setAdmiringUsers(prev => ({
                                ...prev,
                                [userId]: !isCurrentlyAdmiring
                              }));

                              // Update localStorage
                              try {
                                const admiredUsersJson = localStorage.getItem('admiredUsers') || '{}';
                                const admiredUsers = JSON.parse(admiredUsersJson);

                                if (!isCurrentlyAdmiring) {
                                  admiredUsers[userId] = true;
                                } else {
                                  delete admiredUsers[userId];
                                }

                                localStorage.setItem('admiredUsers', JSON.stringify(admiredUsers));
                              } catch (error) {
                                console.error('Error updating localStorage:', error);
                              }

                              // Make API call in the background
                              const token = localStorage.getItem('token') ||
                                localStorage.getItem('jwt_token') ||
                                localStorage.getItem('wedzat_token');

                              if (token) {
                                // Mobile admire button handler (rename variable to avoid redeclaration)
                                const endpointMobile = isCurrentlyAdmiring
                                  ? 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/unfollow'
                                  : 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/follow';

                                axios.post(
                                  endpointMobile,
                                  { target_user_id: userId },
                                  { headers: { Authorization: `Bearer ${token}` } }
                                ).catch(error => {
                                  console.error('Error with admire API call:', error);
                                  // Revert UI state on error
                                  setAdmiringUsers(prev => ({
                                    ...prev,
                                    [userId]: isCurrentlyAdmiring
                                  }));
                                });
                              }
                            }}
                          >
                            {admiringUsers[story.user_id || ''] ? 'Admiring' : 'Admire'}
                            {!admiringUsers[story.user_id || ''] && <span className="ml-1">+</span>}
                          </button>
                        )}
                      </div>

                      {/* Content info at bottom */}
                      {/* Removed content info at bottom as per request */}

                      {/* Like and view UI overlay (bottom left/right) */}
                      {/* Removed followers/following/views overlays as per request */}

                      {/* CSS to hide scrollbar across browsers */}
                      <style jsx>{`
                        /* Hide scrollbar for Chrome, Safari and Opera */
                        .scrollbar-hide::-webkit-scrollbar {
                          display: none;
                        }
                      `}</style>
                    </div>
                  );
                })()}
              </div>
            )}

            {/* Mobile Navigation Buttons */}
            {/* Removed mobile-nav-buttons for mobile view as per request */}

            {/* Mobile Interaction buttons */}
            {/* Removed mobile-interaction-buttons for mobile view as per request */}
          </div>
          {/* End of main flex content for viewer and nav buttons */}
          </div>
        </main>
      </div>
    </div>
  );
}

// Loading component for suspense fallback
function MomentsViewerLoading() {
  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center">
      <div className="animate-spin rounded-full h-10 w-10 border-2 border-red-600 border-t-transparent mr-3"></div>
      <span className="text-gray-800 mt-4">Loading moments...</span>
    </div>
  );
}

// Wrap with a Page component
export default function MomentsViewerPage() {
  return (
    <Suspense fallback={<MomentsViewerLoading />}>
      <MomentsContent />
    </Suspense>
  );
}
