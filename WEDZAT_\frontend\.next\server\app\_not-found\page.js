/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7f0c2e771c3d0513b092fb3afac3f2d5bdc16428db\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__.invalidateCacheAction),\n/* harmony export */   \"7f9c875515fc1ea696b6367e4cde05e2f7b9f31040\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.createOrReadKeylessAction),\n/* harmony export */   \"7f9cdf2d5df654d5952abb8b624fdd122273b42576\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.syncKeylessConfigAction),\n/* harmony export */   \"7fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574\": () => (/* reexport safe */ C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.deleteKeylessAction)\n/* harmony export */ });\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n/* harmony import */ var C_Users_sivas_OneDrive_Desktop_final_WEDZAT_frontend_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Ckeyless-actions.js%22%2C%5B%7B%22id%22%3A%227f9c875515fc1ea696b6367e4cde05e2f7b9f31040%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%2C%7B%22id%22%3A%227f9cdf2d5df654d5952abb8b624fdd122273b42576%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fc6f71be495bbfd59f54f5d4c1dc3a8f9fc803574%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%5D%5D%2C%5B%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40clerk%5C%5Cnextjs%5C%5Cdist%5C%5Cesm%5C%5Capp-router%5C%5Cserver-actions.js%22%2C%5B%7B%22id%22%3A%227f0c2e771c3d0513b092fb3afac3f2d5bdc16428db%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\final\\WEDZAT_\\frontend\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NpdmFzJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDZmluYWwlNUMlNUNXRURaQVRfJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SUFBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNpdmFzXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcZmluYWxcXFxcV0VEWkFUX1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d6ffbd6cb74b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkNmZmYmQ2Y2I3NGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_GlobalUploadManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/GlobalUploadManager */ \"(ssr)/./contexts/GlobalUploadManager.tsx\");\n/* harmony import */ var _contexts_LocationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/LocationContext */ \"(ssr)/./contexts/LocationContext.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n// Metadata is now in metadata.ts\nfunction RootLayout({ children }) {\n    // Create a client\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        \"RootLayout.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 5 * 60 * 1000,\n                        gcTime: 30 * 60 * 1000,\n                        retry: 2,\n                        refetchOnWindowFocus: true\n                    }\n                }\n            })\n    }[\"RootLayout.useState\"]);\n    // Use client-side only rendering for the body content to avoid hydration mismatches\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"RootLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_8__.ClerkProvider, {\n        appearance: {\n            // Add custom appearance options to avoid hydration issues\n            variables: {\n                colorPrimary: \"#b31b1e\"\n            },\n            elements: {\n                // Add data-hydration-safe attributes to avoid hydration issues\n                rootBox: {\n                    attributes: {\n                        \"data-hydration-safe\": \"true\"\n                    }\n                },\n                card: {\n                    attributes: {\n                        \"data-hydration-safe\": \"true\"\n                    }\n                }\n            }\n        },\n        // Use the new redirect props format\n        redirectUrl: \"/auth/callback\",\n        signInUrl: \"/\",\n        signUpUrl: \"/\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        suppressHydrationWarning: true,\n                        children: [\n                            !mounted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col justify-center items-center h-screen\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600 mt-4\",\n                                        children: \"Loading your account...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this) : null,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: mounted ? \"block\" : \"none\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.QueryClientProvider, {\n                                    client: queryClient,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LocationContext__WEBPACK_IMPORTED_MODULE_4__.LocationProvider, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_GlobalUploadManager__WEBPACK_IMPORTED_MODULE_3__.GlobalUploadProvider, {\n                                                children: [\n                                                    children,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_12__.ReactQueryDevtools, {\n                                                        initialIsOpen: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"sync-token-to-cookie\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              function syncTokenToCookie() {\n                try {\n                  const token = localStorage.getItem('wedzat_token') || localStorage.getItem('token') || localStorage.getItem('jwt_token');\n                  if (token) {\n                    document.cookie = 'wedzat_token=' + token + '; path=/; max-age=86400; SameSite=Lax';\n                    console.log('Token synced to cookie for middleware');\n                  }\n\n                  // Also sync vendor flag\n                  const isVendor = localStorage.getItem('is_vendor');\n                  if (isVendor === 'true') {\n                    document.cookie = 'is_vendor=true; path=/; max-age=86400; SameSite=Lax';\n                    console.log('Vendor flag synced to cookie for middleware');\n                  }\n                } catch (e) {\n                  console.error('Error syncing token to cookie:', e);\n                }\n              }\n\n              // Run on page load\n              syncTokenToCookie();\n\n              // Also run when localStorage changes\n              window.addEventListener('storage', syncTokenToCookie);\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"cleanup-extension-attrs\",\n                        strategy: \"beforeInteractive\",\n                        children: `\n              (function() {\n                // Run immediately to clean up before React hydration\n                function cleanupExtensionAttributes() {\n                  // Target common extension attributes\n                  const attributesToRemove = [\n                    'bis_skin_checked',\n                    '__processed_',\n                    'data-bis-'\n                  ];\n\n                  // Get all elements\n                  const allElements = document.querySelectorAll('*');\n\n                  // Remove attributes from each element\n                  allElements.forEach(el => {\n                    for (let i = 0; i < el.attributes.length; i++) {\n                      const attr = el.attributes[i];\n                      for (const badAttr of attributesToRemove) {\n                        if (attr.name.includes(badAttr)) {\n                          el.removeAttribute(attr.name);\n                          // Adjust index since we removed an attribute\n                          i--;\n                          break;\n                        }\n                      }\n                    }\n                  });\n                }\n\n                // Run immediately\n                cleanupExtensionAttributes();\n\n                // Also run after a short delay to catch any late additions\n                setTimeout(cleanupExtensionAttributes, 0);\n              })();\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"init-camera\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              // This script helps ensure the camera is never locked\n              // by creating a persistent camera stream\n              console.log('Camera initialization script loaded');\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        src: \"https://cdn.botpress.cloud/webchat/v2.3/inject.js\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        src: \"https://files.bpcontent.cloud/2025/04/23/14/20250423141656-8AYDGFUF.js\",\n                        strategy: \"afterInteractive\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        id: \"botpress-helper\",\n                        strategy: \"afterInteractive\",\n                        children: `\n              // Create a global function to open the Botpress webchat\n              window.openBotpressChat = function() {\n                // Check if the Botpress webchat is available\n                if (window.botpressWebChat) {\n                  // Try to send the show event\n                  try {\n                    window.botpressWebChat.sendEvent({ type: 'show' });\n                    console.log('Botpress webchat opened successfully');\n                  } catch (error) {\n                    console.error('Error opening Botpress webchat:', error);\n                    // Fallback: Try to click the webchat button\n                    try {\n                      const botpressButton = document.querySelector('.bp-widget-button');\n                      if (botpressButton) {\n                        botpressButton.click();\n                        console.log('Clicked Botpress button as fallback');\n                      } else {\n                        console.error('Could not find Botpress button');\n                      }\n                    } catch (fallbackError) {\n                      console.error('Error with fallback method:', fallbackError);\n                    }\n                  }\n                } else {\n                  console.error('Botpress webchat not initialized');\n                  // Set a flag to open the chat when it becomes available\n                  window.openBotpressChatWhenReady = true;\n\n                  // Check periodically if the webchat becomes available\n                  const checkInterval = setInterval(() => {\n                    if (window.botpressWebChat) {\n                      window.botpressWebChat.sendEvent({ type: 'show' });\n                      console.log('Botpress webchat opened after delay');\n                      clearInterval(checkInterval);\n                      window.openBotpressChatWhenReady = false;\n                    }\n                  }, 500);\n\n                  // Clear the interval after 10 seconds to avoid infinite checking\n                  setTimeout(() => clearInterval(checkInterval), 10000);\n                }\n              };\n\n              // Check if the webchat is loaded and if we need to open it\n              document.addEventListener('DOMContentLoaded', () => {\n                // Wait a bit for the webchat to initialize\n                setTimeout(() => {\n                  if (window.openBotpressChatWhenReady && window.botpressWebChat) {\n                    window.botpressWebChat.sendEvent({ type: 'show' });\n                    console.log('Botpress webchat opened on load');\n                    window.openBotpressChatWhenReady = false;\n                  }\n                }, 1000);\n              });\n            `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/api */ \"(ssr)/./services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isSignedIn, isLoaded: isClerkLoaded, user: clerkUser } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [authInitialized, setAuthInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Function to initialize authentication\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Check if we have a token (including vendor token)\n                        const token = localStorage.getItem(\"token\") || localStorage.getItem(\"jwt_token\");\n                        console.log(`Auth context initialization: Token ${token ? \"found\" : \"not found\"}`);\n                        console.log(`Clerk authentication: ${isClerkLoaded ? isSignedIn ? \"signed in\" : \"not signed in\" : \"loading\"}`);\n                        // Check if authenticated with Clerk\n                        if (isClerkLoaded && isSignedIn) {\n                            console.log(\"User is authenticated with Clerk\");\n                            setIsAuthenticated(true);\n                            // If we don't have a token but have Clerk auth, try to get one\n                            if (!token && clerkUser) {\n                                try {\n                                    // This assumes you have an endpoint to convert Clerk session to JWT\n                                    console.log(\"Getting token from Clerk session\");\n                                    const sessions = await clerkUser.getSessions();\n                                    const clerkToken = sessions[0]?.id;\n                                    if (clerkToken) {\n                                        // Exchange Clerk token for your JWT\n                                        // Extract user info to send along with the token\n                                        const userEmail = clerkUser?.primaryEmailAddress?.emailAddress || \"\";\n                                        const userName = clerkUser?.fullName || \"\";\n                                        const userId = clerkUser?.id || \"\";\n                                        console.log(\"Sending user info to backend:\", {\n                                            email: userEmail,\n                                            name: userName,\n                                            id: userId\n                                        });\n                                        const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.clerkAuth({\n                                            clerk_token: clerkToken,\n                                            user_type: \"customer\",\n                                            user_email: userEmail,\n                                            user_name: userName,\n                                            user_id: userId\n                                        });\n                                        if (response && response.token) {\n                                            localStorage.setItem(\"token\", response.token);\n                                            localStorage.setItem(\"wedzat_token\", response.token); // Also save as wedzat_token for consistency\n                                            console.log(\"Token obtained from Clerk session\");\n                                        } else {\n                                            // If backend auth fails, store the Clerk token as a fallback\n                                            console.log(\"No token received from backend, using Clerk token as fallback\");\n                                            // Use the token we already have\n                                            localStorage.setItem(\"token\", clerkToken);\n                                            localStorage.setItem(\"wedzat_token\", clerkToken);\n                                        }\n                                    }\n                                } catch (error) {\n                                    console.error(\"Error getting token from Clerk session:\", error);\n                                    // If there's an error with the backend, use the Clerk token as a fallback\n                                    // Get the token again if needed\n                                    try {\n                                        const sessions = await clerkUser.getSessions();\n                                        const fallbackToken = sessions[0]?.id;\n                                        if (fallbackToken) {\n                                            console.log(\"Using Clerk token as fallback due to backend error\");\n                                            localStorage.setItem(\"token\", fallbackToken);\n                                            localStorage.setItem(\"wedzat_token\", fallbackToken);\n                                        }\n                                    } catch (tokenError) {\n                                        console.error(\"Error getting fallback token:\", tokenError);\n                                    }\n                                }\n                            }\n                            setIsLoading(false);\n                            setAuthInitialized(true);\n                            return;\n                        }\n                        if (!token) {\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            setIsLoading(false);\n                            setAuthInitialized(true);\n                            // If we're on a protected route, redirect to login\n                            if (false) {}\n                            return;\n                        }\n                        // Attempt to get user profile\n                        try {\n                            console.log(\"Fetching user profile\");\n                            // Check if this is a vendor token\n                            const isVendorToken = localStorage.getItem(\"is_vendor\") === \"true\";\n                            if (isVendorToken) {\n                                console.log(\"Detected vendor token, setting authenticated state\");\n                                // For vendors, we don't need to fetch a profile, just set authenticated\n                                setIsAuthenticated(true);\n                                setUserProfile(null); // No regular user profile for vendors\n                            } else {\n                                // For regular users, fetch the profile\n                                const profile = await _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails();\n                                setUserProfile(profile);\n                                setIsAuthenticated(true);\n                            }\n                        } catch (error) {\n                            console.error(\"Error fetching user profile:\", error);\n                            // Check if this might be a vendor token\n                            try {\n                                // If we're on a vendor page, this might be a vendor token\n                                const pathname = window.location.pathname;\n                                if (pathname.includes(\"/vendor/\")) {\n                                    console.log(\"On vendor page with possible vendor token, setting authenticated\");\n                                    localStorage.setItem(\"is_vendor\", \"true\");\n                                    setIsAuthenticated(true);\n                                    setUserProfile(null);\n                                    return;\n                                }\n                            } catch (vendorCheckError) {\n                                console.error(\"Error checking for vendor token:\", vendorCheckError);\n                            }\n                            // Invalid or expired token\n                            _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            // Redirect to login if on a protected page\n                            if (false) {}\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization error:\", error);\n                        setIsAuthenticated(false);\n                        setUserProfile(null);\n                    } finally{\n                        setIsLoading(false);\n                        setAuthInitialized(true);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            // Only run initAuth when Clerk has loaded\n            if (isClerkLoaded) {\n                initAuth();\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isClerkLoaded,\n        isSignedIn,\n        clerkUser,\n        router\n    ]);\n    // Add a backup timeout to prevent infinite loading state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const loadingTimeoutId = setTimeout({\n                \"AuthProvider.useEffect.loadingTimeoutId\": ()=>{\n                    if (isLoading && !authInitialized) {\n                        console.log(\"Auth initialization timed out, resetting loading state\");\n                        setIsLoading(false);\n                        setAuthInitialized(true);\n                        // Check if we have a token before redirecting\n                        const token = localStorage.getItem(\"token\") || localStorage.getItem(\"jwt_token\") || localStorage.getItem(\"wedzat_token\");\n                        // Only redirect if no token is found\n                        if (!token && \"undefined\" !== \"undefined\") {}\n                    }\n                }\n            }[\"AuthProvider.useEffect.loadingTimeoutId\"], 10000); // Increased to 10 seconds for more time to initialize\n            return ({\n                \"AuthProvider.useEffect\": ()=>clearTimeout(loadingTimeoutId)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isLoading,\n        authInitialized,\n        router\n    ]);\n    // Add listener for storage events to handle token removal in other tabs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"AuthProvider.useEffect.handleStorageChange\": (e)=>{\n                    if (e.key === \"token\" || e.key === \"jwt_token\") {\n                        // If token was removed\n                        if (!e.newValue) {\n                            console.log(\"Token removed in another tab/window\");\n                            setIsAuthenticated(false);\n                            setUserProfile(null);\n                            // If on a protected route, redirect to login\n                            if (false) {}\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"AuthProvider.useEffect\": ()=>window.removeEventListener(\"storage\", handleStorageChange)\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        router\n    ]);\n    const login = (token)=>{\n        console.log(\"Storing token and setting authenticated state\");\n        localStorage.setItem(\"token\", token);\n        localStorage.setItem(\"jwt_token\", token); // Store in both keys for compatibility\n        setIsAuthenticated(true);\n        // Fetch user profile after login\n        _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails().then((profile)=>{\n            console.log(\"User profile fetched:\", profile);\n            setUserProfile(profile);\n        }).catch((error)=>console.error(\"Error fetching user profile:\", error));\n    };\n    const logout = ()=>{\n        console.log(\"Logging out\");\n        _services_api__WEBPACK_IMPORTED_MODULE_3__.authService.logout();\n        setIsAuthenticated(false);\n        setUserProfile(null);\n        router.push(\"/\");\n    };\n    const updateProfile = (profile)=>{\n        console.log(\"Updating user profile\", profile);\n        setUserProfile(profile);\n    };\n    // Refresh user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Only refresh if already authenticated\n            if (isAuthenticated && !isLoading) {\n                const refreshInterval = setInterval({\n                    \"AuthProvider.useEffect.refreshInterval\": ()=>{\n                        console.log(\"Refreshing user profile data\");\n                        _services_api__WEBPACK_IMPORTED_MODULE_3__.userService.getUserDetails().then({\n                            \"AuthProvider.useEffect.refreshInterval\": (profile)=>{\n                                console.log(\"Refreshed user profile:\", profile);\n                                setUserProfile(profile);\n                            }\n                        }[\"AuthProvider.useEffect.refreshInterval\"]).catch({\n                            \"AuthProvider.useEffect.refreshInterval\": (error)=>{\n                                console.error(\"Error refreshing user profile:\", error);\n                            // Don't log out on profile refresh errors\n                            // This prevents unnecessary redirects\n                            }\n                        }[\"AuthProvider.useEffect.refreshInterval\"]);\n                    }\n                }[\"AuthProvider.useEffect.refreshInterval\"], 300000); // Refresh every 5 minutes instead of every minute\n                return ({\n                    \"AuthProvider.useEffect\": ()=>clearInterval(refreshInterval)\n                })[\"AuthProvider.useEffect\"];\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            userProfile,\n            isLoading,\n            login,\n            logout,\n            updateProfile\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/GlobalUploadManager.tsx":
/*!******************************************!*\
  !*** ./contexts/GlobalUploadManager.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalUploadProvider: () => (/* binding */ GlobalUploadProvider),\n/* harmony export */   useGlobalUpload: () => (/* binding */ useGlobalUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// contexts/GlobalUploadManager.tsx\n/* __next_internal_client_entry_do_not_use__ GlobalUploadProvider,useGlobalUpload auto */ \n\nconst GlobalUploadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst GlobalUploadProvider = ({ children })=>{\n    const [isUploadModalOpen, setIsUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialType, setInitialType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [onUploadComplete, setOnUploadComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const openUploadModal = (type, uploadCompleteCallback)=>{\n        setInitialType(type);\n        setOnUploadComplete(()=>uploadCompleteCallback);\n        setIsUploadModalOpen(true);\n    };\n    const closeUploadModal = ()=>{\n        setIsUploadModalOpen(false);\n        setInitialType(undefined);\n        setOnUploadComplete(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GlobalUploadContext.Provider, {\n        value: {\n            isUploadModalOpen,\n            openUploadModal,\n            closeUploadModal\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\GlobalUploadManager.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\nconst useGlobalUpload = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GlobalUploadContext);\n    if (!context) {\n        throw new Error('useGlobalUpload must be used within a GlobalUploadProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/GlobalUploadManager.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LocationContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LocationContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationProvider: () => (/* binding */ LocationProvider),\n/* harmony export */   useLocation: () => (/* binding */ useLocation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LocationProvider,useLocation auto */ \n\nconst LocationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LocationProvider = ({ children })=>{\n    const [selectedLocation, setSelectedLocationState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [reloadKey, setReloadKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const setSelectedLocation = (location)=>{\n        setSelectedLocationState(location);\n        // Trigger homepage reload when location changes\n        setReloadKey((prev)=>prev + 1);\n    };\n    const reloadHomepage = ()=>{\n        setReloadKey((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LocationContext.Provider, {\n        value: {\n            selectedLocation,\n            setSelectedLocation,\n            reloadHomepage\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: children\n        }, reloadKey, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\LocationContext.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\contexts\\\\LocationContext.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLocation = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LocationContext);\n    if (context === undefined) {\n        throw new Error('useLocation must be used within a LocationProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0cy9Mb2NhdGlvbkNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEU7QUFROUUsTUFBTUksZ0NBQWtCSCxvREFBYUEsQ0FBa0NJO0FBRWhFLE1BQU1DLG1CQUFzRCxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUM5RSxNQUFNLENBQUNDLGtCQUFrQkMseUJBQXlCLEdBQUdOLCtDQUFRQSxDQUFTO0lBQ3RFLE1BQU0sQ0FBQ08sV0FBV0MsYUFBYSxHQUFHUiwrQ0FBUUEsQ0FBUztJQUVuRCxNQUFNUyxzQkFBc0IsQ0FBQ0M7UUFDM0JKLHlCQUF5Qkk7UUFDekIsZ0RBQWdEO1FBQ2hERixhQUFhRyxDQUFBQSxPQUFRQSxPQUFPO0lBQzlCO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCSixhQUFhRyxDQUFBQSxPQUFRQSxPQUFPO0lBQzlCO0lBRUEscUJBQ0UsOERBQUNWLGdCQUFnQlksUUFBUTtRQUN2QkMsT0FBTztZQUNMVDtZQUNBSTtZQUNBRztRQUNGO2tCQUVBLDRFQUFDRztzQkFDRVg7V0FET0c7Ozs7Ozs7Ozs7QUFLaEIsRUFBRTtBQUVLLE1BQU1TLGNBQWM7SUFDekIsTUFBTUMsVUFBVWxCLGlEQUFVQSxDQUFDRTtJQUMzQixJQUFJZ0IsWUFBWWYsV0FBVztRQUN6QixNQUFNLElBQUlnQixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNpdmFzXFxPbmVEcml2ZVxcRGVza3RvcFxcZmluYWxcXFdFRFpBVF9cXGZyb250ZW5kXFxjb250ZXh0c1xcTG9jYXRpb25Db250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xyXG5cclxuaW50ZXJmYWNlIExvY2F0aW9uQ29udGV4dFR5cGUge1xyXG4gIHNlbGVjdGVkTG9jYXRpb246IHN0cmluZztcclxuICBzZXRTZWxlY3RlZExvY2F0aW9uOiAobG9jYXRpb246IHN0cmluZykgPT4gdm9pZDtcclxuICByZWxvYWRIb21lcGFnZTogKCkgPT4gdm9pZDtcclxufVxyXG5cclxuY29uc3QgTG9jYXRpb25Db250ZXh0ID0gY3JlYXRlQ29udGV4dDxMb2NhdGlvbkNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuZXhwb3J0IGNvbnN0IExvY2F0aW9uUHJvdmlkZXI6IFJlYWN0LkZDPHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9PiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICBjb25zdCBbc2VsZWN0ZWRMb2NhdGlvbiwgc2V0U2VsZWN0ZWRMb2NhdGlvblN0YXRlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xyXG4gIGNvbnN0IFtyZWxvYWRLZXksIHNldFJlbG9hZEtleV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG5cclxuICBjb25zdCBzZXRTZWxlY3RlZExvY2F0aW9uID0gKGxvY2F0aW9uOiBzdHJpbmcpID0+IHtcclxuICAgIHNldFNlbGVjdGVkTG9jYXRpb25TdGF0ZShsb2NhdGlvbik7XHJcbiAgICAvLyBUcmlnZ2VyIGhvbWVwYWdlIHJlbG9hZCB3aGVuIGxvY2F0aW9uIGNoYW5nZXNcclxuICAgIHNldFJlbG9hZEtleShwcmV2ID0+IHByZXYgKyAxKTtcclxuICB9O1xyXG5cclxuICBjb25zdCByZWxvYWRIb21lcGFnZSA9ICgpID0+IHtcclxuICAgIHNldFJlbG9hZEtleShwcmV2ID0+IHByZXYgKyAxKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPExvY2F0aW9uQ29udGV4dC5Qcm92aWRlclxyXG4gICAgICB2YWx1ZT17e1xyXG4gICAgICAgIHNlbGVjdGVkTG9jYXRpb24sXHJcbiAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbixcclxuICAgICAgICByZWxvYWRIb21lcGFnZSxcclxuICAgICAgfX1cclxuICAgID5cclxuICAgICAgPGRpdiBrZXk9e3JlbG9hZEtleX0+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvTG9jYXRpb25Db250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdXNlTG9jYXRpb24gPSAoKTogTG9jYXRpb25Db250ZXh0VHlwZSA9PiB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoTG9jYXRpb25Db250ZXh0KTtcclxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUxvY2F0aW9uIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBMb2NhdGlvblByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwiTG9jYXRpb25Db250ZXh0IiwidW5kZWZpbmVkIiwiTG9jYXRpb25Qcm92aWRlciIsImNoaWxkcmVuIiwic2VsZWN0ZWRMb2NhdGlvbiIsInNldFNlbGVjdGVkTG9jYXRpb25TdGF0ZSIsInJlbG9hZEtleSIsInNldFJlbG9hZEtleSIsInNldFNlbGVjdGVkTG9jYXRpb24iLCJsb2NhdGlvbiIsInByZXYiLCJyZWxvYWRIb21lcGFnZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJkaXYiLCJ1c2VMb2NhdGlvbiIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LocationContext.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(ssr)/./app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NpdmFzJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDZmluYWwlNUMlNUNXRURaQVRfJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SUFBbUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNpdmFzXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcZmluYWxcXFxcV0VEWkFUX1xcXFxmcm9udGVuZFxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csivas%5C%5COneDrive%5C%5CDesktop%5C%5Cfinal%5C%5CWEDZAT_%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   flashesService: () => (/* binding */ flashesService),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   uploadService: () => (/* binding */ uploadService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_s3MultipartUpload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/s3MultipartUpload */ \"(ssr)/./utils/s3MultipartUpload.ts\");\n// services/api.ts\n\n\n// Use environment variable or fallback to localhost for development\nconst BASE_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n// Log the API URL being used\n// console.log(`API Service using base URL: ${BASE_URL}`);\n// For development, use a CORS proxy if needed\nconst API_BASE_URL = BASE_URL;\nif (true) {\n    // Uncomment the line below to use a CORS proxy in development if needed\n    // API_BASE_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\n    // Log the API URL for debugging\n    console.log('Development mode detected, using API URL:', API_BASE_URL);\n}\n// Log the API URL being used\nconsole.log(`API Service using base URL: ${API_BASE_URL}`);\nconst TOKEN_KEY = 'token'; // Keep using your existing token key\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 60000,\n    withCredentials: false // Helps with CORS issues\n});\n// Helper to get token from storage (checking both keys)\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(TOKEN_KEY);\n};\n// Helper to save token to storage (using both keys for compatibility)\nconst saveToken = (token)=>{\n    if (true) return;\n    console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\n};\n// Request interceptor to add auth token to all requests\napi.interceptors.request.use((config)=>{\n    const token = getToken();\n    if (token && config.headers) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Authentication services\nconst authService = {\n    // Register a new user\n    signup: async (signupData)=>{\n        try {\n            console.log('Attempting signup with data:', {\n                ...signupData,\n                password: signupData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${BASE_URL}/signup`, signupData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Signup response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in signup response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error(\"Signup error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred during signup\"\n            };\n        }\n    },\n    // Register a new vendor\n    registerVendor: async (vendorData)=>{\n        try {\n            console.log('Attempting vendor registration with data:', {\n                ...vendorData,\n                password: vendorData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${BASE_URL}/register-vendor`, vendorData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Vendor registration response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in vendor registration response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error(\"Vendor registration error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred during vendor registration\"\n            };\n        }\n    },\n    // Get business types\n    getBusinessTypes: async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`${BASE_URL}/business-types`);\n            return response.data.business_types;\n        } catch (error) {\n            console.error(\"Get business types error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred while fetching business types\"\n            };\n        }\n    },\n    // Get vendor profile\n    getVendorProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`${BASE_URL}/vendor-profile`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data.profile;\n        } catch (error) {\n            console.error(\"Get vendor profile error:\", error);\n            throw error.response?.data || {\n                error: \"An error occurred while fetching vendor profile\"\n            };\n        }\n    },\n    // Login with email/mobile and password\n    // In services/api.ts - login function\n    login: async (credentials)=>{\n        try {\n            console.log('Attempting login with:', {\n                email: credentials.email,\n                mobile_number: credentials.mobile_number,\n                password: '********'\n            });\n            const response = await api.post('/login', credentials);\n            console.log('Login response received:', {\n                success: true,\n                hasToken: !!response.data.token,\n                tokenPreview: response.data.token ? `${response.data.token.substring(0, 10)}...` : 'none'\n            });\n            // Save token to localStorage with explicit console logs\n            if (response.data.token) {\n                console.log('Saving token to localStorage...');\n                localStorage.setItem('token', response.data.token);\n                // Verify token was saved\n                const savedToken = localStorage.getItem('token');\n                console.log(`Token verification: ${savedToken ? 'Successfully saved' : 'Failed to save'}`);\n            } else {\n                console.warn('No token received in login response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Login error:', error);\n            throw error.response?.data || {\n                error: 'Invalid credentials'\n            };\n        }\n    },\n    // Authenticate with Clerk\n    clerkAuth: async (clerkData)=>{\n        try {\n            console.log('Attempting Clerk authentication');\n            const response = await api.post('/clerk_auth', clerkData);\n            console.log('Clerk auth response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            // Save token to localStorage\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in Clerk auth response');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Clerk authentication error:', error);\n            throw error.response?.data || {\n                error: 'Clerk authentication failed'\n            };\n        }\n    },\n    // Check if user profile is complete\n    checkProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Checking user profile');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`${BASE_URL}/check-profile`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Profile check error:', error);\n            throw error.response?.data || {\n                error: 'Failed to check profile'\n            };\n        }\n    },\n    // Get the current token\n    getToken: ()=>{\n        return getToken();\n    },\n    // Check if the user is authenticated\n    isAuthenticated: ()=>{\n        return !!getToken();\n    },\n    // Logout - clear token from localStorage\n    logout: ()=>{\n        console.log('Logging out and removing tokens');\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n};\n// User services\nconst userService = {\n    // Get user details - uses GET method with explicit token in header\n    getUserDetails: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Fetching user details');\n            // Set the correct Authorization format (Bearer + token)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`${BASE_URL}/user-details`, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching user details:\", error);\n            throw error.response?.data || {\n                error: 'Failed to fetch user details'\n            };\n        }\n    },\n    // Update user details\n    updateUser: async (userData)=>{\n        try {\n            console.log('Updating user details');\n            const response = await api.put('/update-user', userData);\n            return response.data;\n        } catch (error) {\n            console.error('Update user error:', error);\n            throw error.response?.data || {\n                error: 'Failed to update user'\n            };\n        }\n    }\n};\n// Helper to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (true) {\n        return false; // For server-side rendering\n    }\n    const token = getToken();\n    return !!token; // Return true if token exists, false otherwise\n};\n// Upload services\nconst uploadService = {\n    /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */ verifyFace: async (faceImage)=>{\n        try {\n            console.log('Sending face verification request');\n            const token = getToken();\n            // Create the request payload\n            const payload = {\n                face_image: faceImage\n            };\n            const payloadSize = JSON.stringify(payload).length;\n            console.log('Request payload size:', payloadSize);\n            // Check if payload is too large\n            if (payloadSize > 1000000) {\n                console.warn('Warning: Payload is very large, which may cause issues with the API');\n            }\n            // No mock implementation - always use the real API\n            console.log('Using real face verification API');\n            // Make the real API call\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/verify-face`, payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 60000,\n                withCredentials: false\n            });\n            console.log('Face verification response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('Error verifying face:', error);\n            throw error.response?.data || {\n                error: 'Face verification failed'\n            };\n        }\n    },\n    /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */ getPresignedUrl: async (request)=>{\n        try {\n            console.log('Getting presigned URL with request:', request);\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when getting presigned URL');\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/get-upload-url`, request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 30000 // 30 seconds timeout\n            });\n            console.log('Presigned URL response:', response.data);\n            // Validate the new multipart response\n            if (!response.data.media_id || !response.data.multipart || !response.data.part_urls || !response.data.upload_id || !response.data.main_key) {\n                throw new Error('Invalid response from get-upload-url API');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Error getting presigned URL:', error);\n            if (error.response?.status === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw error.response?.data || {\n                error: 'Failed to get upload URL'\n            };\n        }\n    },\n    /**\r\n   * Complete the upload process by notifying the backend\r\n   */ completeUpload: async (request)=>{\n        try {\n            console.log('Completing upload with request:', JSON.stringify(request, null, 2));\n            // Validate the request\n            if (!request.media_id) {\n                console.error('Missing media_id in completeUpload request');\n                throw new Error('Missing media_id in request');\n            }\n            const token = getToken();\n            let payload = {\n                ...request\n            };\n            if (request.media_type === 'video' && Array.isArray(request.vendor_details)) {\n                const vendorArr = request.vendor_details;\n                const vendorObj = {};\n                const keys = [\n                    'venue',\n                    'photographer',\n                    'makeup_artist',\n                    'decoration',\n                    'caterer'\n                ];\n                keys.forEach((k, i)=>{\n                    if (vendorArr[i]) {\n                        vendorObj[`${k}_name`] = vendorArr[i].name || '';\n                        vendorObj[`${k}_contact`] = vendorArr[i].phone || '';\n                    }\n                });\n                for(let i = 5; i < vendorArr.length; i++){\n                    vendorObj[`additional_vendor_${i - 4}_name`] = vendorArr[i].name || '';\n                    vendorObj[`additional_vendor_${i - 4}_contact`] = vendorArr[i].phone || '';\n                }\n                payload = {\n                    ...payload,\n                    vendor_details: vendorObj\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/complete-upload`, payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? `Bearer ${token}` : ''\n                },\n                timeout: 60000\n            });\n            if (!response.data.message) {\n                throw new Error('Invalid response from complete-upload API');\n            }\n            return response.data;\n        } catch (error) {\n            console.error('Error completing upload:', error);\n            if (error.response?.status === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw error.response?.data || {\n                error: 'Failed to complete upload'\n            };\n        }\n    },\n    /**\r\n   * Upload a file to a presigned URL with optimized performance\r\n   */ uploadToPresignedUrl: async (url, file, onProgress)=>{\n        try {\n            console.log('Starting simple direct upload for file:', file.name);\n            console.log('File type:', file.type);\n            console.log('File size:', (file.size / (1024 * 1024)).toFixed(2) + ' MB');\n            console.log('Upload URL:', url);\n            // Report initial progress\n            if (onProgress) onProgress(10);\n            // Start timing the upload\n            const startTime = Date.now();\n            let lastProgressUpdate = 0;\n            // Use simple direct upload for all files\n            console.log('Using simple direct upload');\n            // Create simple headers for direct upload\n            const headers = {\n                'Content-Type': file.type,\n                'Content-Length': file.size.toString()\n            };\n            // Perform simple direct upload to the presigned URL\n            await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].put(url, file, {\n                headers,\n                timeout: 3600000,\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                onUploadProgress: (progressEvent)=>{\n                    if (onProgress && progressEvent.total) {\n                        // Calculate raw percentage\n                        const rawPercent = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                        // Update UI in 5% increments for smoother progress\n                        if (rawPercent >= lastProgressUpdate + 5 || rawPercent === 100) {\n                            lastProgressUpdate = rawPercent;\n                            // Map to 10-95% range for UI\n                            const percentCompleted = 10 + Math.floor(rawPercent * 85 / 100);\n                            onProgress(percentCompleted);\n                            // Calculate and log upload speed\n                            const elapsedSeconds = (Date.now() - startTime) / 1000;\n                            if (elapsedSeconds > 0) {\n                                const speedMBps = (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2);\n                                console.log(`Upload progress: ${percentCompleted}% at ${speedMBps}MB/s`);\n                            }\n                        }\n                    }\n                }\n            });\n            // Calculate final stats\n            const endTime = Date.now();\n            const elapsedSeconds = (endTime - startTime) / 1000;\n            const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);\n            console.log(`Upload completed in ${elapsedSeconds.toFixed(2)}s at ${uploadSpeed}MB/s`);\n            console.log('Response status: 200 (success)');\n            // Report completion\n            if (onProgress) onProgress(100);\n            console.log('File uploaded successfully');\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            // Provide more detailed error information\n            let errorMessage = 'Failed to upload file';\n            if (error.message) {\n                if (error.message.includes('Network Error') || error.message.includes('CORS')) {\n                    errorMessage = 'Network error or CORS issue. Please try again or contact support.';\n                } else {\n                    errorMessage = `Upload error: ${error.message}`;\n                }\n            }\n            if (error.response) {\n                console.error('Response status:', error.response.status);\n                console.error('Response headers:', error.response.headers);\n                console.error('Response data:', error.response.data);\n                if (error.response.status === 403) {\n                    errorMessage = 'Permission denied. The upload URL may have expired.';\n                }\n            }\n            throw {\n                error: errorMessage\n            };\n        }\n    },\n    /**\r\n   * Handle the complete upload process\r\n   */ handleUpload: async (file, mediaType, category, description, tags, details, duration, thumbnail, onProgress)=>{\n        try {\n            console.log('API SERVICE - handleUpload called with params:', {\n                fileName: file?.name,\n                fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\n                mediaType,\n                category,\n                description,\n                tagsCount: tags?.length,\n                detailsCount: details ? Object.keys(details).length : 0,\n                videoCategory: details?.video_category || 'Not set',\n                duration,\n                hasThumbnail: !!thumbnail\n            });\n            // Log the video_category from details\n            if (mediaType === 'video') {\n                console.log('API SERVICE - Video category from details:', details?.video_category);\n                console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\n            }\n            if (!file) {\n                throw new Error('No file provided');\n            }\n            // Log the file size and selected category without overriding the user's selection\n            if (mediaType === 'video') {\n                const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[category] || 250;\n                console.log(`API SERVICE - File size: ${fileSizeMB} MB, Selected category: ${category}`);\n                console.log(`API SERVICE - Size limit for ${category}: ${sizeLimit} MB`);\n            // Warning is now handled in the presignedUrlRequest section\n            }\n            console.log('API SERVICE - Starting upload process for:', file.name);\n            // Update progress to 10%\n            if (onProgress) onProgress(10);\n            // Step 1: Get presigned URLs\n            console.log('API SERVICE - Creating presigned URL request with category:', category);\n            // Ensure we're using the correct backend category names\n            // Frontend: flashes, glimpses, movies, moments\n            // Backend: flash, glimpse, movie, story\n            let backendCategory = mediaType === 'photo' ? category === 'story' ? 'story' : 'post' // For photos: either 'story' or 'post'\n             : category; // For videos: keep the original category\n            // Double-check that we have a valid category\n            const validCategories = [\n                'story',\n                'flash',\n                'glimpse',\n                'movie',\n                'post'\n            ];\n            if (!validCategories.includes(backendCategory)) {\n                console.log(`API SERVICE - WARNING: Invalid category '${backendCategory}'. Using 'flash' as fallback.`);\n                console.log(`API SERVICE - Valid categories are: ${validCategories.join(', ')}`);\n                console.log(`API SERVICE - Category type: ${typeof backendCategory}`);\n                console.log(`API SERVICE - Category value: '${backendCategory}'`);\n                // Use 'flash' as the default for videos instead of 'glimpse'\n                backendCategory = 'flash';\n            }\n            console.log(`API SERVICE - Original category from context: ${category}`);\n            console.log(`API SERVICE - Using backend category: ${backendCategory}`);\n            // Log the file size to help with debugging\n            const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n            console.log(`API SERVICE - File size: ${fileSizeMB} MB`);\n            // Log the category limits\n            const categoryLimits = {\n                'story': 50,\n                'flash': 100,\n                'glimpse': 250,\n                'movie': 2000\n            };\n            const sizeLimit = categoryLimits[backendCategory] || 250;\n            console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB`);\n            // Log a warning if the file size exceeds the limit\n            if (fileSizeMB > sizeLimit) {\n                console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\n                console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\n            }\n            // When constructing presignedUrlRequest for photo, set media_subtype to null\n            const presignedUrlRequest = {\n                media_type: mediaType,\n                media_subtype: mediaType === 'photo' ? null : backendCategory,\n                filename: file.name,\n                content_type: file.type,\n                file_size: file.size\n            };\n            // Log the presigned URL request\n            console.log(`API SERVICE - Sending presigned URL request with media_subtype: ${backendCategory}`);\n            // Add video_category for videos\n            if (mediaType === 'video') {\n                // Get the video_category from details - no default value\n                console.log('API SERVICE - Details object:', details);\n                console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\n                const videoCategory = details?.video_category;\n                console.log('API SERVICE - Video category from details:', videoCategory);\n                // Make sure we're using a valid video_category\n                const allowedCategories = [\n                    'my_wedding',\n                    'wedding_vlog'\n                ];\n                // If no video_category is provided, use a default one\n                if (!videoCategory) {\n                    console.error(`API SERVICE - Missing video_category. Using default 'my_wedding'.`);\n                    // Use 'my_wedding' as the default video_category\n                    presignedUrlRequest.video_category = 'my_wedding';\n                    console.log('API SERVICE - Using default video_category: my_wedding');\n                } else {\n                    // Map the UI category to the backend category if needed\n                    let backendVideoCategory = videoCategory;\n                    // If the category is in UI format, map it to backend format\n                    if (videoCategory === 'my_wedding_videos') {\n                        backendVideoCategory = 'my_wedding';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    } else if (videoCategory === 'wedding_vlog_videos') {\n                        backendVideoCategory = 'wedding_vlog';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    }\n                    // If the video_category is not allowed, throw an error\n                    if (!allowedCategories.includes(backendVideoCategory)) {\n                        console.error(`API SERVICE - Invalid video_category: ${backendVideoCategory}. Must be one of ${JSON.stringify(allowedCategories)}`);\n                        throw new Error(`Invalid video category. Must be one of ${JSON.stringify(allowedCategories)}`);\n                    }\n                    // Use the provided video_category\n                    presignedUrlRequest.video_category = backendVideoCategory;\n                    console.log('API SERVICE - Using video_category:', backendVideoCategory);\n                    console.log(`API SERVICE - Final video category: ${backendVideoCategory}`);\n                    console.log(`API SERVICE - Complete presigned URL request:`, JSON.stringify(presignedUrlRequest, null, 2));\n                }\n                // Log the category and file size limits\n                console.log(`API SERVICE - Original category from UI: ${category}`);\n                console.log(`API SERVICE - Using backend media subtype: ${backendCategory}`);\n                // Log size limits based on category without overriding the user's selection\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[backendCategory] || 250;\n                console.log(`API SERVICE - Size limit for ${backendCategory}: ${sizeLimit} MB (file size: ${fileSizeMB} MB)`);\n                // Log a warning if the file size exceeds the limit for the selected category\n                if (fileSizeMB > sizeLimit) {\n                    console.log(`API SERVICE - WARNING: File size (${fileSizeMB} MB) exceeds the limit for ${backendCategory} (${sizeLimit} MB).`);\n                    console.log(`API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.`);\n                }\n                // Add duration if available\n                if (duration) {\n                    console.log(`Video duration: ${duration} seconds`);\n                // You could add this to the request if the API supports it\n                // presignedUrlRequest.duration = duration;\n                }\n            }\n            const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\n            // Update progress to 20%\n            if (onProgress) onProgress(20);\n            // Step 2: Upload the file using multipart upload (20-70% of progress)\n            const parts = await (0,_utils_s3MultipartUpload__WEBPACK_IMPORTED_MODULE_0__.multipartUpload)(file, presignedUrlResponse.part_urls, (progress)=>{\n                // Map the upload progress from 0-100 to 20-70 in our overall progress\n                if (onProgress) onProgress(20 + progress.percentage * 0.5);\n            });\n            // Complete multipart upload\n            await uploadService.completeMultipartUpload({\n                media_id: presignedUrlResponse.media_id,\n                upload_id: presignedUrlResponse.upload_id,\n                parts: parts\n            });\n            // Step 3: Upload thumbnail if available (70-90% of progress)\n            if (thumbnail && presignedUrlResponse.thumbnail_url) {\n                console.log('Uploading thumbnail:', thumbnail.name);\n                // Update progress to 70%\n                if (onProgress) onProgress(70);\n                await uploadService.uploadToPresignedUrl(presignedUrlResponse.thumbnail_url, thumbnail, (uploadProgress)=>{\n                    // Map the upload progress from 0-100 to 70-90 in our overall progress\n                    if (onProgress) onProgress(70 + uploadProgress * 0.2);\n                });\n            }\n            // Update progress to 90%\n            if (onProgress) onProgress(90);\n            // Step 4: Complete the upload (90-100% of progress)\n            try {\n                // When constructing completeRequest for photo, do not include title\n                const completeRequest = {\n                    media_id: presignedUrlResponse.media_id,\n                    media_type: mediaType,\n                    media_subtype: mediaType === 'photo' ? null : backendCategory,\n                    description: description || '',\n                    tags: tags || []\n                };\n                console.log(`API SERVICE - Setting media_subtype in completeRequest: ${backendCategory}`);\n                // Log the description being sent\n                console.log('Sending description:', description || '');\n                // Add duration for videos if available\n                if (mediaType === 'video' && duration) {\n                    completeRequest.duration = duration;\n                }\n                // Extract personal details from the details object\n                const personalDetails = {\n                    caption: details?.caption || '',\n                    life_partner: details?.personal_life_partner || details?.lifePartner || '',\n                    wedding_style: details?.personal_wedding_style || description || '',\n                    place: details?.personal_place || details?.location || ''\n                };\n                // Extract vendor details from the details object\n                const vendorDetails = {};\n                // Process vendor details from the details object\n                if (details) {\n                    console.log('API SERVICE - Processing vendor details from details object');\n                    console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\n                    // Count vendor fields in the details object\n                    let vendorFieldCount = 0;\n                    Object.keys(details).forEach((key)=>{\n                        if (key.startsWith('vendor_')) {\n                            vendorFieldCount++;\n                        }\n                    });\n                    console.log(`API SERVICE - Found ${vendorFieldCount} vendor-related fields in details object`);\n                    // Keep track of which vendor types we've already processed\n                    const processedVendors = new Set();\n                    Object.entries(details).forEach(([key, value])=>{\n                        if (key.startsWith('vendor_') && value) {\n                            const parts = key.split('_');\n                            if (parts.length >= 3) {\n                                const vendorType = parts[1];\n                                const fieldType = parts.slice(2).join('_');\n                                // Normalize vendor type to avoid duplicates\n                                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                console.log(`API SERVICE - Processing vendor field: ${key} = ${value}`, {\n                                    vendorType,\n                                    fieldType,\n                                    normalizedType,\n                                    alreadyProcessed: processedVendors.has(normalizedType)\n                                });\n                                // Skip if we've already processed this vendor type\n                                if (processedVendors.has(normalizedType)) {\n                                    console.log(`API SERVICE - Skipping ${key} as ${normalizedType} is already processed`);\n                                    return;\n                                }\n                                if (fieldType === 'name') {\n                                    vendorDetails[`${normalizedType}_name`] = value;\n                                    console.log(`API SERVICE - Set ${normalizedType}_name = ${value}`);\n                                    // Also set the contact if available\n                                    const contactKey = `vendor_${vendorType}_contact`;\n                                    if (details[contactKey]) {\n                                        vendorDetails[`${normalizedType}_contact`] = details[contactKey];\n                                        console.log(`API SERVICE - Also set ${normalizedType}_contact = ${details[contactKey]}`);\n                                        processedVendors.add(normalizedType);\n                                        console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\n                                    }\n                                } else if (fieldType === 'contact') {\n                                    vendorDetails[`${normalizedType}_contact`] = value;\n                                    console.log(`API SERVICE - Set ${normalizedType}_contact = ${value}`);\n                                    // Also set the name if available\n                                    const nameKey = `vendor_${vendorType}_name`;\n                                    if (details[nameKey]) {\n                                        vendorDetails[`${normalizedType}_name`] = details[nameKey];\n                                        console.log(`API SERVICE - Also set ${normalizedType}_name = ${details[nameKey]}`);\n                                        processedVendors.add(normalizedType);\n                                        console.log(`API SERVICE - Marked ${normalizedType} as processed (has both name and contact)`);\n                                    }\n                                }\n                            }\n                        }\n                    });\n                    // Log the processed vendor details\n                    // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\n                    // Final check to ensure we have both name and contact for each vendor\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    Object.keys(vendorDetails).forEach((key)=>{\n                        if (key.endsWith('_name')) {\n                            vendorNames.add(key.replace('_name', ''));\n                        } else if (key.endsWith('_contact')) {\n                            vendorContacts.add(key.replace('_contact', ''));\n                        }\n                    });\n                    // Count complete pairs\n                    vendorNames.forEach((name)=>{\n                        if (vendorContacts.has(name)) {\n                            completeVendorCount++;\n                        } else {\n                            // If we have a name but no contact, add a default contact\n                            console.log(`API SERVICE - WARNING: Vendor ${name} has name but no contact, adding default contact`);\n                            vendorDetails[`${name}_contact`] = '0000000000';\n                            completeVendorCount++;\n                        }\n                    });\n                    // Check for contacts without names\n                    vendorContacts.forEach((contact)=>{\n                        if (!vendorNames.has(contact)) {\n                            // If we have a contact but no name, add a default name\n                            console.log(`API SERVICE - WARNING: Vendor ${contact} has contact but no name, adding default name`);\n                            if (typeof contact === 'string') {\n                                vendorDetails[`${contact}_name`] = `Vendor ${contact.charAt(0).toUpperCase() + contact.slice(1)}`;\n                            }\n                            completeVendorCount++;\n                        }\n                    });\n                    console.log(`API SERVICE - Final check: Found ${completeVendorCount} complete vendor pairs`);\n                    console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\n                }\n                // Ensure we have the required vendor fields for wedding videos\n                if (mediaType === 'video' && details?.video_category && [\n                    'my_wedding',\n                    'wedding_vlog'\n                ].includes(details.video_category)) {\n                    // Try to get vendor details from localStorage if they're missing from the details object\n                    const initialVendorKeys = Object.keys(vendorDetails);\n                    const vendorCount = initialVendorKeys.filter((key)=>key.endsWith('_name')).length;\n                    console.log(`API SERVICE - Initial vendor count: ${vendorCount} name fields found`);\n                    console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // Always check localStorage for vendor details to ensure we have the most complete set\n                    console.log('API SERVICE - Checking localStorage for vendor details');\n                    try {\n                        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                        if (storedVendorDetails) {\n                            const parsedVendorDetails = JSON.parse(storedVendorDetails);\n                            console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\n                            // Track how many complete vendor details we've added\n                            let completeVendorCount = 0;\n                            // Process vendor details from localStorage\n                            Object.entries(parsedVendorDetails).forEach(([vendorType, details])=>{\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to vendorDetails\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    vendorDetails[`${normalizedType}_name`] = details.name;\n                                    vendorDetails[`${normalizedType}_contact`] = details.mobileNumber;\n                                    console.log(`API SERVICE - Added vendor ${normalizedType} with name: ${details.name} and contact: ${details.mobileNumber}`);\n                                    completeVendorCount++;\n                                    // Also add the original type if it's different\n                                    if (normalizedType !== vendorType) {\n                                        vendorDetails[`${vendorType}_name`] = details.name;\n                                        vendorDetails[`${vendorType}_contact`] = details.mobileNumber;\n                                        console.log(`API SERVICE - Also added original vendor ${vendorType}`);\n                                    }\n                                }\n                            });\n                            console.log(`API SERVICE - Added ${completeVendorCount} complete vendor details from localStorage`);\n                            console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\n                            // Force update the state with these vendor details\n                            try {\n                                // This is a direct state update to ensure the vendor details are available for validation\n                                if (window && window.dispatchEvent) {\n                                    const vendorUpdateEvent = new CustomEvent('vendor-details-update', {\n                                        detail: parsedVendorDetails\n                                    });\n                                    window.dispatchEvent(vendorUpdateEvent);\n                                    console.log('API SERVICE - Dispatched vendor-details-update event');\n                                }\n                            } catch (eventError) {\n                                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\n                            }\n                        } else {\n                            console.log('API SERVICE - No vendor details found in localStorage');\n                        }\n                    } catch (error) {\n                        console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\n                    }\n                    // Make sure we have at least 4 vendor details with both name and contact\n                    // Define required vendor types for fallback if needed\n                    const requiredVendorTypes = [\n                        'venue',\n                        'photographer',\n                        'makeup_artist',\n                        'decoration',\n                        'caterer'\n                    ];\n                    // Count all vendor details, including additional ones\n                    let validVendorCount = 0;\n                    // Count all vendor details where both name and contact are provided\n                    console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\n                    const allVendorKeys = Object.keys(vendorDetails);\n                    console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\n                    // Keep track of which vendors we've already counted to avoid duplicates\n                    const countedVendors = new Set();\n                    // First pass: Check for standard vendor types\n                    for(let i = 0; i < allVendorKeys.length; i++){\n                        const key = allVendorKeys[i];\n                        if (key.endsWith('_name')) {\n                            const baseKey = key.replace('_name', '');\n                            const contactKey = `${baseKey}_contact`;\n                            // Normalize the key to handle both frontend and backend naming\n                            const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey;\n                            // Skip if we've already counted this vendor\n                            if (countedVendors.has(normalizedKey)) {\n                                continue;\n                            }\n                            console.log(`Checking vendor ${baseKey}:`, {\n                                name: vendorDetails[key],\n                                contact: vendorDetails[contactKey]\n                            });\n                            if (vendorDetails[key] && vendorDetails[contactKey]) {\n                                validVendorCount++;\n                                countedVendors.add(normalizedKey);\n                                console.log(`Valid vendor found: ${baseKey} with name: ${vendorDetails[key]} and contact: ${vendorDetails[contactKey]}`);\n                            }\n                        }\n                    }\n                    console.log(`Total valid vendor count after first pass: ${validVendorCount}`);\n                    // Second pass: Check for additional vendors with different naming patterns\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = `additional${i}_name`;\n                        const contactKey = `additional${i}_contact`;\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(`additional${i}`)) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(`additional${i}`);\n                            console.log(`Valid additional vendor found: additional${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\n                        }\n                    }\n                    // Third pass: Check for additionalVendor pattern (used in some parts of the code)\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = `additionalVendor${i}_name`;\n                        const contactKey = `additionalVendor${i}_contact`;\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(`additionalVendor${i}`)) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(`additionalVendor${i}`);\n                            console.log(`Valid additionalVendor found: additionalVendor${i} with name: ${vendorDetails[nameKey]} and contact: ${vendorDetails[contactKey]}`);\n                        }\n                    }\n                    console.log(`Total valid vendor count after all passes: ${validVendorCount}`);\n                    // Map additional vendors to the predefined vendor types if needed\n                    // This ensures the backend will count them correctly\n                    if (validVendorCount < 4) {\n                        console.log('Need to map additional vendors to predefined types');\n                        // First, collect all additional vendors from all patterns\n                        const additionalVendors = [];\n                        // Collect all additional vendors with 'additional' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = `additional${i}_name`;\n                            const contactKey = `additional${i}_contact`;\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(`additional${i}`)) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: `additional${i}`\n                                });\n                                countedVendors.add(`additional${i}`);\n                                console.log(`Found additional vendor ${i}: ${vendorDetails[nameKey]}`);\n                            }\n                        }\n                        // Collect all additional vendors with 'additionalVendor' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = `additionalVendor${i}_name`;\n                            const contactKey = `additionalVendor${i}_contact`;\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(`additionalVendor${i}`)) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: `additionalVendor${i}`\n                                });\n                                countedVendors.add(`additionalVendor${i}`);\n                                console.log(`Found additionalVendor ${i}: ${vendorDetails[nameKey]}`);\n                            }\n                        }\n                        console.log(`Found ${additionalVendors.length} additional vendors to map`);\n                        // Map additional vendors to empty predefined vendor slots\n                        let additionalIndex = 0;\n                        for (const type of requiredVendorTypes){\n                            // Check if this type is already filled\n                            const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                            // Skip if we've already counted this vendor type\n                            if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                console.log(`Skipping ${type} as it's already counted`);\n                                continue;\n                            }\n                            if (additionalIndex < additionalVendors.length) {\n                                // Map this additional vendor to this predefined type\n                                vendorDetails[`${type}_name`] = additionalVendors[additionalIndex].name;\n                                vendorDetails[`${type}_contact`] = additionalVendors[additionalIndex].contact;\n                                console.log(`Mapped additional vendor ${additionalVendors[additionalIndex].key} to ${type}: ${additionalVendors[additionalIndex].name}`);\n                                additionalIndex++;\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                if (validVendorCount >= 4) {\n                                    console.log(`Reached 4 valid vendors after mapping, no need to continue`);\n                                    break;\n                                }\n                            }\n                        }\n                        // If we still don't have enough, add placeholders\n                        if (validVendorCount < 4) {\n                            console.log('Still need more vendors, adding placeholders');\n                            for (const type of requiredVendorTypes){\n                                // Check if this type is already filled\n                                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                                // Skip if we've already counted this vendor type\n                                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                    console.log(`Skipping ${type} for placeholder as it's already counted`);\n                                    continue;\n                                }\n                                // Add placeholder for this vendor type\n                                vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                                vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                console.log(`Added placeholder for ${type}: ${vendorDetails[`${type}_name`]}`);\n                                if (validVendorCount >= 4) {\n                                    console.log(`Reached 4 valid vendors after adding placeholders`);\n                                    break;\n                                }\n                            }\n                            // Final check - if we still don't have 4, force add the remaining required types\n                            if (validVendorCount < 4) {\n                                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\n                                for (const type of requiredVendorTypes){\n                                    if (!vendorDetails[`${type}_name`] || !vendorDetails[`${type}_contact`]) {\n                                        vendorDetails[`${type}_name`] = `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                                        vendorDetails[`${type}_contact`] = '0000000000';\n                                        validVendorCount++;\n                                        console.log(`Force added placeholder for ${type}`);\n                                        if (validVendorCount >= 4) break;\n                                    }\n                                }\n                            }\n                        }\n                        // Final log of vendor count\n                        console.log(`Final valid vendor count: ${validVendorCount}`);\n                    }\n                }\n                // Add the details to the request\n                completeRequest.personal_details = personalDetails;\n                completeRequest.vendor_details = vendorDetails;\n                // Final check before sending - ensure we have at least 4 complete vendor details\n                if (mediaType === 'video') {\n                    // Count complete vendor details (with both name and contact)\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    if (vendorDetails) {\n                        Object.keys(vendorDetails).forEach((key)=>{\n                            if (key.endsWith('_name')) {\n                                vendorNames.add(key.replace('_name', ''));\n                            } else if (key.endsWith('_contact')) {\n                                vendorContacts.add(key.replace('_contact', ''));\n                            }\n                        });\n                        // Count complete pairs\n                        vendorNames.forEach((name)=>{\n                            if (vendorContacts.has(name)) {\n                                completeVendorCount++;\n                            }\n                        });\n                    }\n                    console.log(`API SERVICE - FINAL CHECK: Found ${completeVendorCount} complete vendor pairs before sending request`);\n                    // If we don't have enough vendors, add placeholders to reach 4\n                    if (completeVendorCount < 4) {\n                        console.log(`API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors`);\n                        const requiredVendorTypes = [\n                            'venue',\n                            'photographer',\n                            'makeup_artist',\n                            'decoration',\n                            'caterer'\n                        ];\n                        for (const type of requiredVendorTypes){\n                            // Skip if this vendor is already complete\n                            if (vendorDetails[`${type}_name`] && vendorDetails[`${type}_contact`]) {\n                                continue;\n                            }\n                            // Add placeholder for this vendor\n                            vendorDetails[`${type}_name`] = vendorDetails[`${type}_name`] || `${type.charAt(0).toUpperCase() + type.slice(1)}`;\n                            vendorDetails[`${type}_contact`] = vendorDetails[`${type}_contact`] || '0000000000';\n                            completeVendorCount++;\n                            console.log(`API SERVICE - FINAL CHECK: Added placeholder for ${type}`);\n                            if (completeVendorCount >= 4) {\n                                break;\n                            }\n                        }\n                        // Update the request with the new vendor details\n                        completeRequest.vendor_details = vendorDetails;\n                        console.log(`API SERVICE - FINAL CHECK: Now have ${completeVendorCount} complete vendor pairs`);\n                    }\n                }\n                console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\n                // Complete the upload\n                const completeResponse = await uploadService.completeUpload(completeRequest);\n                // Log the complete response\n                console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\n                // Update progress to 100%\n                if (onProgress) onProgress(100);\n                console.log('Upload process completed successfully');\n                return completeResponse;\n            } catch (completeError) {\n                console.error('Error in complete upload step:', completeError);\n                // Handle other errors\n                const errorMessage = completeError.response?.data?.error || completeError.message || 'Failed to complete the upload process';\n                console.error('Throwing error:', errorMessage);\n                throw {\n                    error: errorMessage\n                };\n            }\n        } catch (error) {\n            console.error('Error in upload process:', error);\n            // If error is already formatted correctly, just pass it through\n            if (error.error) {\n                throw error;\n            }\n            // Otherwise, format the error\n            throw error.response?.data || {\n                error: 'Upload process failed'\n            };\n        }\n    },\n    /**\r\n   * Complete the multipart upload by notifying the backend with part ETags\r\n   */ completeMultipartUpload: async ({ media_id, upload_id, parts })=>{\n        try {\n            const token = getToken();\n            if (!token) throw {\n                error: 'No authentication token found'\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/complete-multipart-upload`, {\n                media_id,\n                upload_id,\n                parts\n            }, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                },\n                timeout: 60000\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error completing multipart upload:', error);\n            throw error.response?.data || {\n                error: 'Failed to complete multipart upload'\n            };\n        }\n    }\n};\n// Like/Unlike API for flashes (shorts page)\nconst API_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\nconst flashesService = {\n    like: async (mediaId)=>{\n        try {\n            const token =  false ? 0 : null;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_URL}/like`, {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            return response.data;\n        } catch (error) {\n            throw error.response?.data || {\n                error: 'Failed to like flash'\n            };\n        }\n    },\n    unlike: async (mediaId)=>{\n        try {\n            const token =  false ? 0 : null;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_URL}/unlike`, {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            return response.data;\n        } catch (error) {\n            throw error.response?.data || {\n                error: 'Failed to unlike flash'\n            };\n        }\n    },\n    view: async (mediaId)=>{\n        try {\n            const token =  false ? 0 : null;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_URL}/view`, {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            return response.data;\n        } catch (error) {\n            throw error.response?.data || {\n                error: 'Failed to register view'\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    authService,\n    userService,\n    uploadService,\n    isAuthenticated,\n    flashesService\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zZXJ2aWNlcy9hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSxrQkFBa0I7QUFDZ0M7QUFDb0M7QUFFdEYsb0VBQW9FO0FBQ3BFLE1BQU1FLFdBQVdDLGtFQUErQixJQUFJLENBQTJCO0FBRy9FLDZCQUE2QjtBQUM3QiwwREFBMEQ7QUFFMUQsOENBQThDO0FBQzlDLE1BQU1HLGVBQWVKO0FBQ3JCLElBQUlDLElBQXNDLEVBQUU7SUFDMUMsd0VBQXdFO0lBQ3hFLG9FQUFvRTtJQUVwRSxnQ0FBZ0M7SUFDaENJLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkNGO0FBQzNEO0FBRUEsNkJBQTZCO0FBQzdCQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyw0QkFBNEIsRUFBRUYsY0FBYztBQUN6RCxNQUFNRyxZQUFZLFNBQVUscUNBQXFDO0FBQ2pFLE1BQU1DLGdCQUFnQixhQUFhLG9DQUFvQztBQUV2RSx3QkFBd0I7QUFDeEIsTUFBTUMsTUFBTVgsNkNBQUtBLENBQUNZLE1BQU0sQ0FBQztJQUN2QkMsU0FBU1A7SUFDVFEsU0FBUztRQUNQLGdCQUFnQjtJQUNsQjtJQUNBQyxTQUFTO0lBQ1RDLGlCQUFpQixNQUFNLHlCQUF5QjtBQUNsRDtBQUVBLHdEQUF3RDtBQUN4RCxNQUFNQyxXQUFXO0lBQ2YsSUFBSSxJQUE2QixFQUFFLE9BQU87SUFDMUMsT0FBT0MsYUFBYUMsT0FBTyxDQUFDVjtBQUM5QjtBQUVBLHNFQUFzRTtBQUN0RSxNQUFNVyxZQUFZLENBQUNDO0lBQ2pCLElBQUksSUFBNkIsRUFBRTtJQUNuQ2QsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ2EsTUFBTUMsU0FBUyxDQUFDLEdBQUcsTUFBTTtJQUN0RUosYUFBYUssT0FBTyxDQUFDZCxXQUFXWTtJQUNoQ0gsYUFBYUssT0FBTyxDQUFDYixlQUFlVyxRQUFRLDBDQUEwQztBQUN4RjtBQUVBLHdEQUF3RDtBQUN4RFYsSUFBSWEsWUFBWSxDQUFDQyxPQUFPLENBQUNDLEdBQUcsQ0FDMUIsQ0FBQ0M7SUFDQyxNQUFNTixRQUFRSjtJQUNkLElBQUlJLFNBQVNNLE9BQU9iLE9BQU8sRUFBRTtRQUMzQmEsT0FBT2IsT0FBTyxDQUFDYyxhQUFhLEdBQUcsQ0FBQyxPQUFPLEVBQUVQLE9BQU87SUFDbEQ7SUFDQSxPQUFPTTtBQUNULEdBQ0EsQ0FBQ0U7SUFDQyxPQUFPQyxRQUFRQyxNQUFNLENBQUNGO0FBQ3hCO0FBR0YsMEJBQTBCO0FBQ25CLE1BQU1HLGNBQWM7SUFDekIsc0JBQXNCO0lBQ3RCQyxRQUFRLE9BQU9DO1FBQ2IsSUFBSTtZQUNGM0IsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQztnQkFDMUMsR0FBRzBCLFVBQVU7Z0JBQ2JDLFVBQVVELFdBQVdDLFFBQVEsR0FBRyxhQUFhQztZQUMvQztZQUVBLE1BQU1DLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDc0MsSUFBSSxDQUFDLEdBQUdwQyxTQUFTLE9BQU8sQ0FBQyxFQUFFZ0MsWUFBWTtnQkFDbEVwQixTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBUCxRQUFRQyxHQUFHLENBQUMsNkJBQTZCO2dCQUN2QytCLFNBQVM7Z0JBQ1RDLFVBQVUsQ0FBQyxDQUFDSCxTQUFTSSxJQUFJLENBQUNwQixLQUFLO1lBQ2pDO1lBRUEsSUFBSWdCLFNBQVNJLElBQUksQ0FBQ3BCLEtBQUssRUFBRTtnQkFDdkJELFVBQVVpQixTQUFTSSxJQUFJLENBQUNwQixLQUFLO1lBQy9CLE9BQU87Z0JBQ0xkLFFBQVFtQyxJQUFJLENBQUM7WUFDZjtZQUVBLE9BQU9MLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPWixPQUFZO1lBQ25CdEIsUUFBUXNCLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CLE1BQU1BLE1BQU1RLFFBQVEsRUFBRUksUUFBUTtnQkFBRVosT0FBTztZQUFrQztRQUMzRTtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCYyxnQkFBZ0IsT0FBT0M7UUFDckIsSUFBSTtZQUNGckMsUUFBUUMsR0FBRyxDQUFDLDZDQUE2QztnQkFDdkQsR0FBR29DLFVBQVU7Z0JBQ2JULFVBQVVTLFdBQVdULFFBQVEsR0FBRyxhQUFhQztZQUMvQztZQUVBLE1BQU1DLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDc0MsSUFBSSxDQUFDLEdBQUdwQyxTQUFTLGdCQUFnQixDQUFDLEVBQUUwQyxZQUFZO2dCQUMzRTlCLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtZQUNGO1lBRUFQLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEM7Z0JBQ3BEK0IsU0FBUztnQkFDVEMsVUFBVSxDQUFDLENBQUNILFNBQVNJLElBQUksQ0FBQ3BCLEtBQUs7WUFDakM7WUFFQSxJQUFJZ0IsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSyxFQUFFO2dCQUN2QkQsVUFBVWlCLFNBQVNJLElBQUksQ0FBQ3BCLEtBQUs7WUFDL0IsT0FBTztnQkFDTGQsUUFBUW1DLElBQUksQ0FBQztZQUNmO1lBRUEsT0FBT0wsU0FBU0ksSUFBSTtRQUN0QixFQUFFLE9BQU9aLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUMsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQStDO1FBQ3hGO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckJnQixrQkFBa0I7UUFDaEIsSUFBSTtZQUNGLE1BQU1SLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDOEMsR0FBRyxDQUFDLEdBQUc1QyxTQUFTLGVBQWUsQ0FBQztZQUM3RCxPQUFPbUMsU0FBU0ksSUFBSSxDQUFDTSxjQUFjO1FBQ3JDLEVBQUUsT0FBT2xCLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQWtEO1FBQzNGO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckJtQixrQkFBa0I7UUFDaEIsSUFBSTtZQUNGLE1BQU0zQixRQUFRSjtZQUNkLElBQUksQ0FBQ0ksT0FBTztnQkFDVixNQUFNO29CQUFFUSxPQUFPO2dCQUFnQztZQUNqRDtZQUVBLE1BQU1RLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDOEMsR0FBRyxDQUFDLEdBQUc1QyxTQUFTLGVBQWUsQ0FBQyxFQUFFO2dCQUM3RFksU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVPLE9BQU87b0JBQ2xDLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLE9BQU9nQixTQUFTSSxJQUFJLENBQUNRLE9BQU87UUFDOUIsRUFBRSxPQUFPcEIsT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBa0Q7UUFDM0Y7SUFDRjtJQU1BLHVDQUF1QztJQUN2QyxzQ0FBc0M7SUFDdENxQixPQUFPLE9BQU9DO1FBQ1osSUFBSTtZQUNGNUMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjtnQkFDcEM0QyxPQUFPRCxZQUFZQyxLQUFLO2dCQUN4QkMsZUFBZUYsWUFBWUUsYUFBYTtnQkFDeENsQixVQUFVO1lBQ1o7WUFFQSxNQUFNRSxXQUFXLE1BQU0xQixJQUFJMkIsSUFBSSxDQUFDLFVBQVVhO1lBRTFDNUMsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QjtnQkFDdEMrQixTQUFTO2dCQUNUQyxVQUFVLENBQUMsQ0FBQ0gsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSztnQkFDL0JpQyxjQUFjakIsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSyxHQUFHLEdBQUdnQixTQUFTSSxJQUFJLENBQUNwQixLQUFLLENBQUNDLFNBQVMsQ0FBQyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUc7WUFDckY7WUFFQSx3REFBd0Q7WUFDeEQsSUFBSWUsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSyxFQUFFO2dCQUN2QmQsUUFBUUMsR0FBRyxDQUFDO2dCQUNaVSxhQUFhSyxPQUFPLENBQUMsU0FBU2MsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSztnQkFFakQseUJBQXlCO2dCQUN6QixNQUFNa0MsYUFBYXJDLGFBQWFDLE9BQU8sQ0FBQztnQkFDeENaLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFK0MsYUFBYSx1QkFBdUIsa0JBQWtCO1lBQzNGLE9BQU87Z0JBQ0xoRCxRQUFRbUMsSUFBSSxDQUFDO1lBQ2Y7WUFFQSxPQUFPTCxTQUFTSSxJQUFJO1FBQ3RCLEVBQUUsT0FBT1osT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMsZ0JBQWdCQTtZQUM5QixNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBc0I7UUFDL0Q7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQjJCLFdBQVcsT0FBT0M7UUFPaEIsSUFBSTtZQUNGbEQsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTTZCLFdBQVcsTUFBTTFCLElBQUkyQixJQUFJLENBQUMsZUFBZW1CO1lBRS9DbEQsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQztnQkFDM0MrQixTQUFTO2dCQUNUQyxVQUFVLENBQUMsQ0FBQ0gsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSztZQUNqQztZQUVBLDZCQUE2QjtZQUM3QixJQUFJZ0IsU0FBU0ksSUFBSSxDQUFDcEIsS0FBSyxFQUFFO2dCQUN2QkQsVUFBVWlCLFNBQVNJLElBQUksQ0FBQ3BCLEtBQUs7WUFDL0IsT0FBTztnQkFDTGQsUUFBUW1DLElBQUksQ0FBQztZQUNmO1lBRUEsT0FBT0wsU0FBU0ksSUFBSTtRQUN0QixFQUFFLE9BQU9aLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQThCO1FBQ3ZFO0lBQ0Y7SUFFQSxvQ0FBb0M7SUFDcEM2QixjQUFjO1FBQ1osSUFBSTtZQUNGLE1BQU1yQyxRQUFRSjtZQUVkLElBQUksQ0FBQ0ksT0FBTztnQkFDVixNQUFNO29CQUFFUSxPQUFPO2dCQUFnQztZQUNqRDtZQUVBdEIsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTTZCLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDOEMsR0FBRyxDQUFDLEdBQUc1QyxTQUFTLGNBQWMsQ0FBQyxFQUFFO2dCQUM1RFksU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVPLE9BQU87b0JBQ2xDLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLE9BQU9nQixTQUFTSSxJQUFJO1FBQ3RCLEVBQUUsT0FBT1osT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMsd0JBQXdCQTtZQUN0QyxNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBMEI7UUFDbkU7SUFDRjtJQUVBLHdCQUF3QjtJQUN4QlosVUFBVTtRQUNSLE9BQU9BO0lBQ1Q7SUFFQSxxQ0FBcUM7SUFDckMwQyxpQkFBaUI7UUFDZixPQUFPLENBQUMsQ0FBQzFDO0lBQ1g7SUFFQSx5Q0FBeUM7SUFDekMyQyxRQUFRO1FBQ05yRCxRQUFRQyxHQUFHLENBQUM7UUFDWlUsYUFBYTJDLFVBQVUsQ0FBQ3BEO1FBQ3hCUyxhQUFhMkMsVUFBVSxDQUFDbkQ7SUFDMUI7QUFDRixFQUFFO0FBRUYsZ0JBQWdCO0FBQ1QsTUFBTW9ELGNBQWM7SUFDekIsbUVBQW1FO0lBQ25FQyxnQkFBZ0I7UUFDZCxJQUFJO1lBQ0YsTUFBTTFDLFFBQVFKO1lBQ2QsSUFBSSxDQUFDSSxPQUFPO2dCQUNWLE1BQU07b0JBQUVRLE9BQU87Z0JBQWdDO1lBQ2pEO1lBRUF0QixRQUFRQyxHQUFHLENBQUM7WUFFWix3REFBd0Q7WUFDeEQsTUFBTTZCLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDOEMsR0FBRyxDQUFDLEdBQUc1QyxTQUFTLGFBQWEsQ0FBQyxFQUFFO2dCQUMzRFksU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVPLE9BQU87b0JBQ2xDLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLE9BQU9nQixTQUFTSSxJQUFJO1FBQ3RCLEVBQUUsT0FBT1osT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBK0I7UUFDeEU7SUFDRjtJQUVBLHNCQUFzQjtJQUN0Qm1DLFlBQVksT0FBT0M7UUFRakIsSUFBSTtZQUNGMUQsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTTZCLFdBQVcsTUFBTTFCLElBQUl1RCxHQUFHLENBQUMsZ0JBQWdCRDtZQUMvQyxPQUFPNUIsU0FBU0ksSUFBSTtRQUN0QixFQUFFLE9BQU9aLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcEMsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQXdCO1FBQ2pFO0lBQ0Y7QUFDRixFQUFFO0FBRUYsMkNBQTJDO0FBQ3BDLE1BQU04QixrQkFBa0I7SUFDN0IsSUFBSSxJQUE2QixFQUFFO1FBQ2pDLE9BQU8sT0FBTyw0QkFBNEI7SUFDNUM7SUFDQSxNQUFNdEMsUUFBUUo7SUFDZCxPQUFPLENBQUMsQ0FBQ0ksT0FBTywrQ0FBK0M7QUFDakUsRUFBRTtBQTZFRixrQkFBa0I7QUFDWCxNQUFNOEMsZ0JBQWdCO0lBQzNCOzs7R0FHQyxHQUNEQyxZQUFZLE9BQU9DO1FBQ2pCLElBQUk7WUFDRjlELFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1hLFFBQVFKO1lBRWQsNkJBQTZCO1lBQzdCLE1BQU1xRCxVQUFVO2dCQUFFQyxZQUFZRjtZQUFVO1lBQ3hDLE1BQU1HLGNBQWNDLEtBQUtDLFNBQVMsQ0FBQ0osU0FBU0ssTUFBTTtZQUVsRHBFLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJnRTtZQUVyQyxnQ0FBZ0M7WUFDaEMsSUFBSUEsY0FBYyxTQUFTO2dCQUN6QmpFLFFBQVFtQyxJQUFJLENBQUM7WUFDZjtZQUVBLG1EQUFtRDtZQUNuRG5DLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHlCQUF5QjtZQUN6QixNQUFNNkIsV0FBVyxNQUFNckMsNkNBQUtBLENBQUNzQyxJQUFJLENBQUMsR0FBR2hDLGFBQWEsWUFBWSxDQUFDLEVBQUVnRSxTQUFTO2dCQUN4RXhELFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUJPLFFBQVEsQ0FBQyxPQUFPLEVBQUVBLE9BQU8sR0FBRztnQkFDL0M7Z0JBQ0FOLFNBQVM7Z0JBQ1RDLGlCQUFpQjtZQUNuQjtZQUVBVCxRQUFRQyxHQUFHLENBQUMsK0JBQStCNkIsU0FBU0ksSUFBSTtZQUN4RCxPQUFPSixTQUFTSSxJQUFJO1FBQ3RCLEVBQUUsT0FBT1osT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBMkI7UUFDcEU7SUFDRjtJQUVBOztHQUVDLEdBQ0QrQyxpQkFBaUIsT0FBT25EO1FBQ3RCLElBQUk7WUFDRmxCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUNpQjtZQUNuRCxNQUFNSixRQUFRSjtZQUNkLElBQUksQ0FBQ0ksT0FBTztnQkFDVmQsUUFBUW1DLElBQUksQ0FBQztZQUNmO1lBQ0EsTUFBTUwsV0FBVyxNQUFNckMsNkNBQUtBLENBQUNzQyxJQUFJLENBQUMsR0FBR2hDLGFBQWEsZUFBZSxDQUFDLEVBQUVtQixTQUFTO2dCQUMzRVgsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQk8sUUFBUSxDQUFDLE9BQU8sRUFBRUEsT0FBTyxHQUFHO2dCQUMvQztnQkFDQU4sU0FBUyxNQUFNLHFCQUFxQjtZQUN0QztZQUNBUixRQUFRQyxHQUFHLENBQUMsMkJBQTJCNkIsU0FBU0ksSUFBSTtZQUNwRCxzQ0FBc0M7WUFDdEMsSUFBSSxDQUFDSixTQUFTSSxJQUFJLENBQUNvQyxRQUFRLElBQUksQ0FBQ3hDLFNBQVNJLElBQUksQ0FBQ3FDLFNBQVMsSUFBSSxDQUFDekMsU0FBU0ksSUFBSSxDQUFDc0MsU0FBUyxJQUFJLENBQUMxQyxTQUFTSSxJQUFJLENBQUN1QyxTQUFTLElBQUksQ0FBQzNDLFNBQVNJLElBQUksQ0FBQ3dDLFFBQVEsRUFBRTtnQkFDMUksTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBQ0EsT0FBTzdDLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPWixPQUFZO1lBQ25CdEIsUUFBUXNCLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLElBQUlBLE1BQU1RLFFBQVEsRUFBRThDLFdBQVcsS0FBSztnQkFDbEMsTUFBTTtvQkFBRXRELE9BQU87Z0JBQThDO1lBQy9EO1lBQ0EsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQTJCO1FBQ3BFO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEdUQsZ0JBQWdCLE9BQU8zRDtRQUNyQixJQUFJO1lBQ0ZsQixRQUFRQyxHQUFHLENBQUMsbUNBQW1DaUUsS0FBS0MsU0FBUyxDQUFDakQsU0FBUyxNQUFNO1lBQzdFLHVCQUF1QjtZQUN2QixJQUFJLENBQUNBLFFBQVFvRCxRQUFRLEVBQUU7Z0JBQ3JCdEUsUUFBUXNCLEtBQUssQ0FBQztnQkFDZCxNQUFNLElBQUlxRCxNQUFNO1lBQ2xCO1lBQ0EsTUFBTTdELFFBQVFKO1lBQ2QsSUFBSXFELFVBQVU7Z0JBQUUsR0FBRzdDLE9BQU87WUFBQztZQUMzQixJQUFJQSxRQUFRNEQsVUFBVSxLQUFLLFdBQVdDLE1BQU1DLE9BQU8sQ0FBQzlELFFBQVErRCxjQUFjLEdBQUc7Z0JBQzNFLE1BQU1DLFlBQVloRSxRQUFRK0QsY0FBYztnQkFDeEMsTUFBTUUsWUFBb0MsQ0FBQztnQkFDM0MsTUFBTUMsT0FBTztvQkFDWDtvQkFDQTtvQkFDQTtvQkFDQTtvQkFDQTtpQkFDRDtnQkFDREEsS0FBS0MsT0FBTyxDQUFDLENBQUNDLEdBQUdDO29CQUNmLElBQUlMLFNBQVMsQ0FBQ0ssRUFBRSxFQUFFO3dCQUNoQkosU0FBUyxDQUFDLEdBQUdHLEVBQUUsS0FBSyxDQUFDLENBQUMsR0FBR0osU0FBUyxDQUFDSyxFQUFFLENBQUNDLElBQUksSUFBSTt3QkFDOUNMLFNBQVMsQ0FBQyxHQUFHRyxFQUFFLFFBQVEsQ0FBQyxDQUFDLEdBQUdKLFNBQVMsQ0FBQ0ssRUFBRSxDQUFDRSxLQUFLLElBQUk7b0JBQ3BEO2dCQUNGO2dCQUNBLElBQUssSUFBSUYsSUFBSSxHQUFHQSxJQUFJTCxVQUFVZCxNQUFNLEVBQUVtQixJQUFLO29CQUN6Q0osU0FBUyxDQUFDLENBQUMsa0JBQWtCLEVBQUVJLElBQUksRUFBRSxLQUFLLENBQUMsQ0FBQyxHQUFHTCxTQUFTLENBQUNLLEVBQUUsQ0FBQ0MsSUFBSSxJQUFJO29CQUNwRUwsU0FBUyxDQUFDLENBQUMsa0JBQWtCLEVBQUVJLElBQUksRUFBRSxRQUFRLENBQUMsQ0FBQyxHQUFHTCxTQUFTLENBQUNLLEVBQUUsQ0FBQ0UsS0FBSyxJQUFJO2dCQUMxRTtnQkFDQTFCLFVBQVU7b0JBQ1IsR0FBR0EsT0FBTztvQkFDVmtCLGdCQUFnQkU7Z0JBQ2xCO1lBQ0Y7WUFDQSxNQUFNckQsV0FBVyxNQUFNckMsNkNBQUtBLENBQUNzQyxJQUFJLENBQUMsR0FBR2hDLGFBQWEsZ0JBQWdCLENBQUMsRUFBRWdFLFNBQVM7Z0JBQzVFeEQsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQk8sUUFBUSxDQUFDLE9BQU8sRUFBRUEsT0FBTyxHQUFHO2dCQUMvQztnQkFDQU4sU0FBUztZQUNYO1lBQ0EsSUFBSSxDQUFDc0IsU0FBU0ksSUFBSSxDQUFDd0QsT0FBTyxFQUFFO2dCQUMxQixNQUFNLElBQUlmLE1BQU07WUFDbEI7WUFDQSxPQUFPN0MsU0FBU0ksSUFBSTtRQUN0QixFQUFFLE9BQU9aLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsSUFBSUEsTUFBTVEsUUFBUSxFQUFFOEMsV0FBVyxLQUFLO2dCQUNsQyxNQUFNO29CQUFFdEQsT0FBTztnQkFBOEM7WUFDL0Q7WUFDQSxNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBNEI7UUFDckU7SUFDRjtJQUVBOztHQUVDLEdBQ0RxRSxzQkFBc0IsT0FDcEJDLEtBQ0FDLE1BQ0FDO1FBRUEsSUFBSTtZQUNGOUYsUUFBUUMsR0FBRyxDQUFDLDJDQUEyQzRGLEtBQUtMLElBQUk7WUFDaEV4RixRQUFRQyxHQUFHLENBQUMsY0FBYzRGLEtBQUtFLElBQUk7WUFDbkMvRixRQUFRQyxHQUFHLENBQUMsY0FBYyxDQUFDNEYsS0FBS0csSUFBSSxHQUFJLFFBQU8sSUFBRyxDQUFDLEVBQUdDLE9BQU8sQ0FBQyxLQUFLO1lBQ25FakcsUUFBUUMsR0FBRyxDQUFDLGVBQWUyRjtZQUUzQiwwQkFBMEI7WUFDMUIsSUFBSUUsWUFBWUEsV0FBVztZQUUzQiwwQkFBMEI7WUFDMUIsTUFBTUksWUFBWUMsS0FBS0MsR0FBRztZQUMxQixJQUFJQyxxQkFBcUI7WUFFekIseUNBQXlDO1lBQ3pDckcsUUFBUUMsR0FBRyxDQUFDO1lBRVosMENBQTBDO1lBQzFDLE1BQU1NLFVBQWtDO2dCQUN0QyxnQkFBZ0JzRixLQUFLRSxJQUFJO2dCQUN6QixrQkFBa0JGLEtBQUtHLElBQUksQ0FBQ00sUUFBUTtZQUN0QztZQUVBLG9EQUFvRDtZQUNwRCxNQUFNN0csNkNBQUtBLENBQUNrRSxHQUFHLENBQUNpQyxLQUFLQyxNQUFNO2dCQUN6QnRGO2dCQUNBQyxTQUFTO2dCQUNUK0YsZUFBZUM7Z0JBQ2ZDLGtCQUFrQkQ7Z0JBQ2xCRSxrQkFBa0IsQ0FBQ0M7b0JBQ2pCLElBQUliLGNBQWNhLGNBQWNDLEtBQUssRUFBRTt3QkFDckMsMkJBQTJCO3dCQUMzQixNQUFNQyxhQUFhQyxLQUFLQyxLQUFLLENBQUMsY0FBZUMsTUFBTSxHQUFHLE1BQU9MLGNBQWNDLEtBQUs7d0JBRWhGLG1EQUFtRDt3QkFDbkQsSUFBSUMsY0FBY1IscUJBQXFCLEtBQUtRLGVBQWUsS0FBSzs0QkFDOURSLHFCQUFxQlE7NEJBRXJCLDZCQUE2Qjs0QkFDN0IsTUFBTUksbUJBQW1CLEtBQUtILEtBQUtJLEtBQUssQ0FBQyxhQUFjLEtBQU07NEJBQzdEcEIsV0FBV21COzRCQUVYLGlDQUFpQzs0QkFDakMsTUFBTUUsaUJBQWlCLENBQUNoQixLQUFLQyxHQUFHLEtBQUtGLFNBQVEsSUFBSzs0QkFDbEQsSUFBSWlCLGlCQUFpQixHQUFHO2dDQUN0QixNQUFNQyxZQUFZLENBQUMsY0FBZUosTUFBTSxHQUFHRyxpQkFBbUIsUUFBTyxJQUFHLENBQUMsRUFBR2xCLE9BQU8sQ0FBQztnQ0FDcEZqRyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQkFBaUIsRUFBRWdILGlCQUFpQixLQUFLLEVBQUVHLFVBQVUsSUFBSSxDQUFDOzRCQUN6RTt3QkFDRjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsd0JBQXdCO1lBQ3hCLE1BQU1DLFVBQVVsQixLQUFLQyxHQUFHO1lBQ3hCLE1BQU1lLGlCQUFpQixDQUFDRSxVQUFVbkIsU0FBUSxJQUFLO1lBQy9DLE1BQU1vQixjQUFjLENBQUN6QixLQUFLRyxJQUFJLEdBQUdtQixpQkFBa0IsUUFBTyxJQUFHLENBQUMsRUFBR2xCLE9BQU8sQ0FBQztZQUV6RWpHLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFa0gsZUFBZWxCLE9BQU8sQ0FBQyxHQUFHLEtBQUssRUFBRXFCLFlBQVksSUFBSSxDQUFDO1lBQ3JGdEgsUUFBUUMsR0FBRyxDQUFDO1lBRVosb0JBQW9CO1lBQ3BCLElBQUk2RixZQUFZQSxXQUFXO1lBRTNCOUYsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPcUIsT0FBWTtZQUNuQnRCLFFBQVFzQixLQUFLLENBQUMseUJBQXlCQTtZQUV2QywwQ0FBMEM7WUFDMUMsSUFBSWlHLGVBQWU7WUFFbkIsSUFBSWpHLE1BQU1vRSxPQUFPLEVBQUU7Z0JBQ2pCLElBQUlwRSxNQUFNb0UsT0FBTyxDQUFDOEIsUUFBUSxDQUFDLG9CQUFvQmxHLE1BQU1vRSxPQUFPLENBQUM4QixRQUFRLENBQUMsU0FBUztvQkFDN0VELGVBQWU7Z0JBQ2pCLE9BQU87b0JBQ0xBLGVBQWUsQ0FBQyxjQUFjLEVBQUVqRyxNQUFNb0UsT0FBTyxFQUFFO2dCQUNqRDtZQUNGO1lBRUEsSUFBSXBFLE1BQU1RLFFBQVEsRUFBRTtnQkFDbEI5QixRQUFRc0IsS0FBSyxDQUFDLG9CQUFvQkEsTUFBTVEsUUFBUSxDQUFDOEMsTUFBTTtnQkFDdkQ1RSxRQUFRc0IsS0FBSyxDQUFDLHFCQUFxQkEsTUFBTVEsUUFBUSxDQUFDdkIsT0FBTztnQkFDekRQLFFBQVFzQixLQUFLLENBQUMsa0JBQWtCQSxNQUFNUSxRQUFRLENBQUNJLElBQUk7Z0JBRW5ELElBQUlaLE1BQU1RLFFBQVEsQ0FBQzhDLE1BQU0sS0FBSyxLQUFLO29CQUNqQzJDLGVBQWU7Z0JBQ2pCO1lBQ0Y7WUFFQSxNQUFNO2dCQUFFakcsT0FBT2lHO1lBQWE7UUFDOUI7SUFDRjtJQUVBOztHQUVDLEdBQ0RFLGNBQWMsT0FDWjVCLE1BQ0E2QixXQUNBQyxVQUNBQyxhQUNBQyxNQUNBQyxTQUNBQyxVQUNBQyxXQUNBbEM7UUFFQSxJQUFJO1lBQ0Y5RixRQUFRQyxHQUFHLENBQUMsa0RBQWtEO2dCQUM1RGdJLFVBQVVwQyxNQUFNTDtnQkFDaEIwQyxVQUFVcEIsS0FBS0MsS0FBSyxDQUFDbEIsS0FBS0csSUFBSSxHQUFJLFFBQU8sSUFBRyxJQUFLLE9BQU8sTUFBTTtnQkFDOUQwQjtnQkFDQUM7Z0JBQ0FDO2dCQUNBTyxXQUFXTixNQUFNekQ7Z0JBQ2pCZ0UsY0FBY04sVUFBVU8sT0FBT2pELElBQUksQ0FBQzBDLFNBQVMxRCxNQUFNLEdBQUc7Z0JBQ3REa0UsZUFBZVIsU0FBU1Msa0JBQWtCO2dCQUMxQ1I7Z0JBQ0FTLGNBQWMsQ0FBQyxDQUFDUjtZQUNsQjtZQUVBLHNDQUFzQztZQUN0QyxJQUFJTixjQUFjLFNBQVM7Z0JBQ3pCMUgsUUFBUUMsR0FBRyxDQUFDLDhDQUE4QzZILFNBQVNTO2dCQUNuRXZJLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0M2SCxVQUFVNUQsS0FBS0MsU0FBUyxDQUFDMkQsV0FBVztZQUN0RjtZQUVBLElBQUksQ0FBQ2pDLE1BQU07Z0JBQ1QsTUFBTSxJQUFJbEIsTUFBTTtZQUNsQjtZQUVBLGtGQUFrRjtZQUNsRixJQUFJK0MsY0FBYyxTQUFTO2dCQUN6QixNQUFNZSxhQUFhM0IsS0FBS0MsS0FBSyxDQUFDbEIsS0FBS0csSUFBSSxHQUFJLFFBQU8sSUFBRyxJQUFLLE9BQU87Z0JBQ2pFLE1BQU0wQyxpQkFBaUI7b0JBQ3JCLFNBQVM7b0JBQ1QsU0FBUztvQkFDVCxXQUFXO29CQUNYLFNBQVM7Z0JBQ1g7Z0JBRUEsTUFBTUMsWUFBWUQsY0FBYyxDQUFDZixTQUF3QyxJQUFJO2dCQUU3RTNILFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixFQUFFd0ksV0FBVyx3QkFBd0IsRUFBRWQsVUFBVTtnQkFDdkYzSCxRQUFRQyxHQUFHLENBQUMsQ0FBQyw2QkFBNkIsRUFBRTBILFNBQVMsRUFBRSxFQUFFZ0IsVUFBVSxHQUFHLENBQUM7WUFFdkUsNERBQTREO1lBQzlEO1lBRUEzSSxRQUFRQyxHQUFHLENBQUMsOENBQThDNEYsS0FBS0wsSUFBSTtZQUVuRSx5QkFBeUI7WUFDekIsSUFBSU0sWUFBWUEsV0FBVztZQUUzQiw2QkFBNkI7WUFDN0I5RixRQUFRQyxHQUFHLENBQUMsK0RBQStEMEg7WUFFM0Usd0RBQXdEO1lBQ3hELCtDQUErQztZQUMvQyx3Q0FBd0M7WUFDeEMsSUFBSWlCLGtCQUFrQmxCLGNBQWMsVUFDL0JDLGFBQWEsVUFBVSxVQUFVLE9BQVMsdUNBQXVDO2VBQ2xGQSxVQUFXLHlDQUF5QztZQUV4RCw2Q0FBNkM7WUFDN0MsTUFBTWtCLGtCQUFrQjtnQkFBQztnQkFBUztnQkFBUztnQkFBVztnQkFBUzthQUFPO1lBQ3RFLElBQUksQ0FBQ0EsZ0JBQWdCckIsUUFBUSxDQUFDb0Isa0JBQWtCO2dCQUM5QzVJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlDQUF5QyxFQUFFMkksZ0JBQWdCLDZCQUE2QixDQUFDO2dCQUN0RzVJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9DQUFvQyxFQUFFNEksZ0JBQWdCQyxJQUFJLENBQUMsT0FBTztnQkFDL0U5SSxRQUFRQyxHQUFHLENBQUMsQ0FBQyw2QkFBNkIsRUFBRSxPQUFPMkksaUJBQWlCO2dCQUNwRTVJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLCtCQUErQixFQUFFMkksZ0JBQWdCLENBQUMsQ0FBQztnQkFFaEUsNkRBQTZEO2dCQUM3REEsa0JBQWtCO1lBQ3BCO1lBRUE1SSxRQUFRQyxHQUFHLENBQUMsQ0FBQyw4Q0FBOEMsRUFBRTBILFVBQVU7WUFDdkUzSCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRTJJLGlCQUFpQjtZQUV0RSwyQ0FBMkM7WUFDM0MsTUFBTUgsYUFBYTNCLEtBQUtDLEtBQUssQ0FBQ2xCLEtBQUtHLElBQUksR0FBSSxRQUFPLElBQUcsSUFBSyxPQUFPO1lBQ2pFaEcsUUFBUUMsR0FBRyxDQUFDLENBQUMseUJBQXlCLEVBQUV3SSxXQUFXLEdBQUcsQ0FBQztZQUV2RCwwQkFBMEI7WUFDMUIsTUFBTUMsaUJBQWlCO2dCQUNyQixTQUFTO2dCQUNULFNBQVM7Z0JBQ1QsV0FBVztnQkFDWCxTQUFTO1lBQ1g7WUFFQSxNQUFNQyxZQUFZRCxjQUFjLENBQUNFLGdCQUErQyxJQUFJO1lBQ3BGNUksUUFBUUMsR0FBRyxDQUFDLENBQUMsNkJBQTZCLEVBQUUySSxnQkFBZ0IsRUFBRSxFQUFFRCxVQUFVLEdBQUcsQ0FBQztZQUU5RSxtREFBbUQ7WUFDbkQsSUFBSUYsYUFBYUUsV0FBVztnQkFDMUIzSSxRQUFRQyxHQUFHLENBQUMsQ0FBQyxrQ0FBa0MsRUFBRXdJLFdBQVcsMkJBQTJCLEVBQUVHLGdCQUFnQixFQUFFLEVBQUVELFVBQVUsS0FBSyxDQUFDO2dCQUM3SDNJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBGQUEwRixDQUFDO1lBQzFHO1lBRUEsNkVBQTZFO1lBQzdFLE1BQU04SSxzQkFBMkM7Z0JBQy9DakUsWUFBWTRDO2dCQUNac0IsZUFBZXRCLGNBQWMsVUFBVSxPQUFPa0I7Z0JBQzlDSyxVQUFVcEQsS0FBS0wsSUFBSTtnQkFDbkIwRCxjQUFjckQsS0FBS0UsSUFBSTtnQkFDdkJvRCxXQUFXdEQsS0FBS0csSUFBSTtZQUN0QjtZQUVBLGdDQUFnQztZQUNoQ2hHLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdFQUFnRSxFQUFFMkksaUJBQWlCO1lBRWhHLGdDQUFnQztZQUNoQyxJQUFJbEIsY0FBYyxTQUFTO2dCQUN6Qix5REFBeUQ7Z0JBQ3pEMUgsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQzZIO2dCQUM3QzlILFFBQVFDLEdBQUcsQ0FBQywrQkFBK0I2SCxVQUFVTyxPQUFPakQsSUFBSSxDQUFDMEMsV0FBVztnQkFFNUUsTUFBTVEsZ0JBQWdCUixTQUFTUztnQkFDL0J2SSxRQUFRQyxHQUFHLENBQUMsOENBQThDcUk7Z0JBRTFELCtDQUErQztnQkFDL0MsTUFBTWMsb0JBQW9CO29CQUFDO29CQUFjO2lCQUFlO2dCQUV4RCxzREFBc0Q7Z0JBQ3RELElBQUksQ0FBQ2QsZUFBZTtvQkFDbEJ0SSxRQUFRc0IsS0FBSyxDQUFDLENBQUMsaUVBQWlFLENBQUM7b0JBQ2pGLGlEQUFpRDtvQkFDakR5SCxvQkFBb0JSLGNBQWMsR0FBRztvQkFDckN2SSxRQUFRQyxHQUFHLENBQUM7Z0JBQ2QsT0FBTztvQkFDTCx3REFBd0Q7b0JBQ3hELElBQUlvSix1QkFBdUJmO29CQUUzQiw0REFBNEQ7b0JBQzVELElBQUlBLGtCQUFrQixxQkFBcUI7d0JBQ3pDZSx1QkFBdUI7d0JBQ3ZCckosUUFBUUMsR0FBRyxDQUFDLHlEQUF5RG9KO29CQUN2RSxPQUFPLElBQUlmLGtCQUFrQix1QkFBdUI7d0JBQ2xEZSx1QkFBdUI7d0JBQ3ZCckosUUFBUUMsR0FBRyxDQUFDLHlEQUF5RG9KO29CQUN2RTtvQkFFQSx1REFBdUQ7b0JBQ3ZELElBQUksQ0FBQ0Qsa0JBQWtCNUIsUUFBUSxDQUFDNkIsdUJBQXVCO3dCQUNyRHJKLFFBQVFzQixLQUFLLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRStILHFCQUFxQixpQkFBaUIsRUFBRW5GLEtBQUtDLFNBQVMsQ0FBQ2lGLG9CQUFvQjt3QkFDbEksTUFBTSxJQUFJekUsTUFBTSxDQUFDLHVDQUF1QyxFQUFFVCxLQUFLQyxTQUFTLENBQUNpRixvQkFBb0I7b0JBQy9GO29CQUVBLGtDQUFrQztvQkFDbENMLG9CQUFvQlIsY0FBYyxHQUFHYztvQkFDckNySixRQUFRQyxHQUFHLENBQUMsdUNBQXVDb0o7b0JBRW5EckosUUFBUUMsR0FBRyxDQUFDLENBQUMsb0NBQW9DLEVBQUVvSixzQkFBc0I7b0JBQ3pFckosUUFBUUMsR0FBRyxDQUFDLENBQUMsNkNBQTZDLENBQUMsRUFBRWlFLEtBQUtDLFNBQVMsQ0FBQzRFLHFCQUFxQixNQUFNO2dCQUN6RztnQkFFQSx3Q0FBd0M7Z0JBQ3hDL0ksUUFBUUMsR0FBRyxDQUFDLENBQUMseUNBQXlDLEVBQUUwSCxVQUFVO2dCQUNsRTNILFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxFQUFFMkksaUJBQWlCO2dCQUUzRSw0RUFBNEU7Z0JBQzVFLE1BQU1GLGlCQUFpQjtvQkFDckIsU0FBUztvQkFDVCxTQUFTO29CQUNULFdBQVc7b0JBQ1gsU0FBUztnQkFDWDtnQkFFQSxNQUFNQyxZQUFZRCxjQUFjLENBQUNFLGdCQUErQyxJQUFJO2dCQUNwRjVJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZCQUE2QixFQUFFMkksZ0JBQWdCLEVBQUUsRUFBRUQsVUFBVSxnQkFBZ0IsRUFBRUYsV0FBVyxJQUFJLENBQUM7Z0JBRTVHLDZFQUE2RTtnQkFDN0UsSUFBSUEsYUFBYUUsV0FBVztvQkFDMUIzSSxRQUFRQyxHQUFHLENBQUMsQ0FBQyxrQ0FBa0MsRUFBRXdJLFdBQVcsMkJBQTJCLEVBQUVHLGdCQUFnQixFQUFFLEVBQUVELFVBQVUsS0FBSyxDQUFDO29CQUM3SDNJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBGQUEwRixDQUFDO2dCQUMxRztnQkFFQSw0QkFBNEI7Z0JBQzVCLElBQUk4SCxVQUFVO29CQUNaL0gsUUFBUUMsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUU4SCxTQUFTLFFBQVEsQ0FBQztnQkFDakQsMkRBQTJEO2dCQUMzRCwyQ0FBMkM7Z0JBQzdDO1lBQ0Y7WUFFQSxNQUFNdUIsdUJBQXVCLE1BQU0xRixjQUFjUyxlQUFlLENBQUMwRTtZQUVqRSx5QkFBeUI7WUFDekIsSUFBSWpELFlBQVlBLFdBQVc7WUFFM0Isc0VBQXNFO1lBQ3RFLE1BQU15RCxRQUFRLE1BQU03Six5RUFBZUEsQ0FDakNtRyxNQUNBeUQscUJBQXFCOUUsU0FBUyxFQUM5QixDQUFDZ0Y7Z0JBQ0Msc0VBQXNFO2dCQUN0RSxJQUFJMUQsWUFBWUEsV0FBVyxLQUFNMEQsU0FBU0MsVUFBVSxHQUFHO1lBQ3pEO1lBR0YsNEJBQTRCO1lBQzVCLE1BQU03RixjQUFjOEYsdUJBQXVCLENBQUM7Z0JBQzFDcEYsVUFBVWdGLHFCQUFxQmhGLFFBQVE7Z0JBQ3ZDRyxXQUFXNkUscUJBQXFCN0UsU0FBUztnQkFDekM4RSxPQUFPQTtZQUNUO1lBRUEsNkRBQTZEO1lBQzdELElBQUl2QixhQUFhc0IscUJBQXFCSyxhQUFhLEVBQUU7Z0JBQ25EM0osUUFBUUMsR0FBRyxDQUFDLHdCQUF3QitILFVBQVV4QyxJQUFJO2dCQUVsRCx5QkFBeUI7Z0JBQ3pCLElBQUlNLFlBQVlBLFdBQVc7Z0JBRTNCLE1BQU1sQyxjQUFjK0Isb0JBQW9CLENBQ3RDMkQscUJBQXFCSyxhQUFhLEVBQ2xDM0IsV0FDQSxDQUFDNEI7b0JBQ0Msc0VBQXNFO29CQUN0RSxJQUFJOUQsWUFBWUEsV0FBVyxLQUFNOEQsaUJBQWlCO2dCQUNwRDtZQUVKO1lBRUEseUJBQXlCO1lBQ3pCLElBQUk5RCxZQUFZQSxXQUFXO1lBRTNCLG9EQUFvRDtZQUNwRCxJQUFJO2dCQUNGLG9FQUFvRTtnQkFDcEUsTUFBTStELGtCQUF5QztvQkFDN0N2RixVQUFVZ0YscUJBQXFCaEYsUUFBUTtvQkFDdkNRLFlBQVk0QztvQkFDWnNCLGVBQWV0QixjQUFjLFVBQVUsT0FBT2tCO29CQUM5Q2hCLGFBQWFBLGVBQWU7b0JBQzVCQyxNQUFNQSxRQUFRLEVBQUU7Z0JBQ2xCO2dCQUVBN0gsUUFBUUMsR0FBRyxDQUFDLENBQUMsd0RBQXdELEVBQUUySSxpQkFBaUI7Z0JBRXhGLGlDQUFpQztnQkFDakM1SSxRQUFRQyxHQUFHLENBQUMsd0JBQXdCMkgsZUFBZTtnQkFFbkQsdUNBQXVDO2dCQUN2QyxJQUFJRixjQUFjLFdBQVdLLFVBQVU7b0JBQ3JDOEIsZ0JBQWdCOUIsUUFBUSxHQUFHQTtnQkFDN0I7Z0JBRUEsbURBQW1EO2dCQUNuRCxNQUFNK0Isa0JBQTZEO29CQUNqRUMsU0FBU2pDLFNBQVNpQyxXQUFXO29CQUM3QkMsY0FBY2xDLFNBQVNtQyx5QkFBeUJuQyxTQUFTb0MsZUFBZTtvQkFDeEVDLGVBQWVyQyxTQUFTc0MsMEJBQTBCeEMsZUFBZTtvQkFDakV5QyxPQUFPdkMsU0FBU3dDLGtCQUFrQnhDLFNBQVN5QyxZQUFZO2dCQUN6RDtnQkFFQSxpREFBaUQ7Z0JBQ2pELE1BQU1DLGdCQUF5RCxDQUFDO2dCQUVoRSxpREFBaUQ7Z0JBQ2pELElBQUkxQyxTQUFTO29CQUNYOUgsUUFBUUMsR0FBRyxDQUFDO29CQUNaRCxRQUFRQyxHQUFHLENBQUMscUNBQXFDaUUsS0FBS0MsU0FBUyxDQUFDMkQsU0FBUyxNQUFNO29CQUUvRSw0Q0FBNEM7b0JBQzVDLElBQUkyQyxtQkFBbUI7b0JBQ3ZCcEMsT0FBT2pELElBQUksQ0FBQzBDLFNBQVN6QyxPQUFPLENBQUNxRixDQUFBQTt3QkFDM0IsSUFBSUEsSUFBSUMsVUFBVSxDQUFDLFlBQVk7NEJBQzdCRjt3QkFDRjtvQkFDRjtvQkFDQXpLLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFd0ssaUJBQWlCLHdDQUF3QyxDQUFDO29CQUU3RiwyREFBMkQ7b0JBQzNELE1BQU1HLG1CQUFtQixJQUFJQztvQkFFN0J4QyxPQUFPeUMsT0FBTyxDQUFDaEQsU0FBU3pDLE9BQU8sQ0FBQyxDQUFDLENBQUNxRixLQUFLSyxNQUFNO3dCQUMzQyxJQUFJTCxJQUFJQyxVQUFVLENBQUMsY0FBY0ksT0FBTzs0QkFDdEMsTUFBTXhCLFFBQVFtQixJQUFJTSxLQUFLLENBQUM7NEJBQ3hCLElBQUl6QixNQUFNbkYsTUFBTSxJQUFJLEdBQUc7Z0NBQ3JCLE1BQU02RyxhQUFhMUIsS0FBSyxDQUFDLEVBQUU7Z0NBQzNCLE1BQU0yQixZQUFZM0IsTUFBTTRCLEtBQUssQ0FBQyxHQUFHckMsSUFBSSxDQUFDO2dDQUV0Qyw0Q0FBNEM7Z0NBQzVDLE1BQU1zQyxpQkFBaUJILGVBQWUsaUJBQWlCLGtCQUNyREEsZUFBZSxnQkFBZ0IsZUFBZUE7Z0NBRWhEakwsUUFBUUMsR0FBRyxDQUFDLENBQUMsdUNBQXVDLEVBQUV5SyxJQUFJLEdBQUcsRUFBRUssT0FBTyxFQUFFO29DQUN0RUU7b0NBQ0FDO29DQUNBRTtvQ0FDQUMsa0JBQWtCVCxpQkFBaUJVLEdBQUcsQ0FBQ0Y7Z0NBQ3pDO2dDQUVBLG1EQUFtRDtnQ0FDbkQsSUFBSVIsaUJBQWlCVSxHQUFHLENBQUNGLGlCQUFpQjtvQ0FDeENwTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRXlLLElBQUksSUFBSSxFQUFFVSxlQUFlLHFCQUFxQixDQUFDO29DQUNyRjtnQ0FDRjtnQ0FFQSxJQUFJRixjQUFjLFFBQVE7b0NBQ3hCVixhQUFhLENBQUMsR0FBR1ksZUFBZSxLQUFLLENBQUMsQ0FBQyxHQUFHTDtvQ0FDMUMvSyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxrQkFBa0IsRUFBRW1MLGVBQWUsUUFBUSxFQUFFTCxPQUFPO29DQUVqRSxvQ0FBb0M7b0NBQ3BDLE1BQU1RLGFBQWEsQ0FBQyxPQUFPLEVBQUVOLFdBQVcsUUFBUSxDQUFDO29DQUNqRCxJQUFJbkQsT0FBTyxDQUFDeUQsV0FBVyxFQUFFO3dDQUN2QmYsYUFBYSxDQUFDLEdBQUdZLGVBQWUsUUFBUSxDQUFDLENBQUMsR0FBR3RELE9BQU8sQ0FBQ3lELFdBQVc7d0NBQ2hFdkwsUUFBUUMsR0FBRyxDQUFDLENBQUMsdUJBQXVCLEVBQUVtTCxlQUFlLFdBQVcsRUFBRXRELE9BQU8sQ0FBQ3lELFdBQVcsRUFBRTt3Q0FDdkZYLGlCQUFpQlksR0FBRyxDQUFDSjt3Q0FDckJwTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRW1MLGVBQWUseUNBQXlDLENBQUM7b0NBQy9GO2dDQUNGLE9BQU8sSUFBSUYsY0FBYyxXQUFXO29DQUNsQ1YsYUFBYSxDQUFDLEdBQUdZLGVBQWUsUUFBUSxDQUFDLENBQUMsR0FBR0w7b0NBQzdDL0ssUUFBUUMsR0FBRyxDQUFDLENBQUMsa0JBQWtCLEVBQUVtTCxlQUFlLFdBQVcsRUFBRUwsT0FBTztvQ0FFcEUsaUNBQWlDO29DQUNqQyxNQUFNVSxVQUFVLENBQUMsT0FBTyxFQUFFUixXQUFXLEtBQUssQ0FBQztvQ0FDM0MsSUFBSW5ELE9BQU8sQ0FBQzJELFFBQVEsRUFBRTt3Q0FDcEJqQixhQUFhLENBQUMsR0FBR1ksZUFBZSxLQUFLLENBQUMsQ0FBQyxHQUFHdEQsT0FBTyxDQUFDMkQsUUFBUTt3Q0FDMUR6TCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRW1MLGVBQWUsUUFBUSxFQUFFdEQsT0FBTyxDQUFDMkQsUUFBUSxFQUFFO3dDQUNqRmIsaUJBQWlCWSxHQUFHLENBQUNKO3dDQUNyQnBMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHFCQUFxQixFQUFFbUwsZUFBZSx5Q0FBeUMsQ0FBQztvQ0FDL0Y7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7b0JBRUEsbUNBQW1DO29CQUNuQyxrR0FBa0c7b0JBQ2xHLHFJQUFxSTtvQkFFckksc0VBQXNFO29CQUN0RSxNQUFNTSxjQUFjLElBQUliO29CQUN4QixNQUFNYyxpQkFBaUIsSUFBSWQ7b0JBQzNCLElBQUllLHNCQUFzQjtvQkFFMUJ2RCxPQUFPakQsSUFBSSxDQUFDb0YsZUFBZW5GLE9BQU8sQ0FBQ3FGLENBQUFBO3dCQUNqQyxJQUFJQSxJQUFJbUIsUUFBUSxDQUFDLFVBQVU7NEJBQ3pCSCxZQUFZRixHQUFHLENBQUNkLElBQUlvQixPQUFPLENBQUMsU0FBUzt3QkFDdkMsT0FBTyxJQUFJcEIsSUFBSW1CLFFBQVEsQ0FBQyxhQUFhOzRCQUNuQ0YsZUFBZUgsR0FBRyxDQUFDZCxJQUFJb0IsT0FBTyxDQUFDLFlBQVk7d0JBQzdDO29CQUNGO29CQUVBLHVCQUF1QjtvQkFDdkJKLFlBQVlyRyxPQUFPLENBQUNHLENBQUFBO3dCQUNsQixJQUFJbUcsZUFBZUwsR0FBRyxDQUFDOUYsT0FBTzs0QkFDNUJvRzt3QkFDRixPQUFPOzRCQUNMLDBEQUEwRDs0QkFDMUQ1TCxRQUFRQyxHQUFHLENBQUMsQ0FBQyw4QkFBOEIsRUFBRXVGLEtBQUssZ0RBQWdELENBQUM7NEJBQ25HZ0YsYUFBYSxDQUFDLEdBQUdoRixLQUFLLFFBQVEsQ0FBQyxDQUFDLEdBQUc7NEJBQ25Db0c7d0JBQ0Y7b0JBQ0Y7b0JBRUEsbUNBQW1DO29CQUNuQ0QsZUFBZXRHLE9BQU8sQ0FBQzBHLENBQUFBO3dCQUNyQixJQUFJLENBQUNMLFlBQVlKLEdBQUcsQ0FBQ1MsVUFBVTs0QkFDN0IsdURBQXVEOzRCQUN2RC9MLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhCQUE4QixFQUFFOEwsUUFBUSw2Q0FBNkMsQ0FBQzs0QkFDbkcsSUFBSSxPQUFPQSxZQUFZLFVBQVU7Z0NBQy9CdkIsYUFBYSxDQUFDLEdBQUd1QixRQUFRLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUVBLFFBQVFDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLFFBQVFaLEtBQUssQ0FBQyxJQUFJOzRCQUNuRzs0QkFDQVM7d0JBQ0Y7b0JBQ0Y7b0JBRUE1TCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQ0FBaUMsRUFBRTJMLG9CQUFvQixzQkFBc0IsQ0FBQztvQkFDM0Y1TCxRQUFRQyxHQUFHLENBQUMsdUNBQXVDaUUsS0FBS0MsU0FBUyxDQUFDcUcsZUFBZSxNQUFNO2dCQUN6RjtnQkFFQSwrREFBK0Q7Z0JBQy9ELElBQUk5QyxjQUFjLFdBQVdJLFNBQVNTLGtCQUNwQztvQkFBQztvQkFBYztpQkFBZSxDQUFDZixRQUFRLENBQUNNLFFBQVFTLGNBQWMsR0FBRztvQkFDakUseUZBQXlGO29CQUN6RixNQUFNMkQsb0JBQW9CN0QsT0FBT2pELElBQUksQ0FBQ29GO29CQUN0QyxNQUFNMkIsY0FBY0Qsa0JBQWtCRSxNQUFNLENBQUMxQixDQUFBQSxNQUFPQSxJQUFJbUIsUUFBUSxDQUFDLFVBQVV6SCxNQUFNO29CQUVqRnBFLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9DQUFvQyxFQUFFa00sWUFBWSxrQkFBa0IsQ0FBQztvQkFDbEZuTSxRQUFRQyxHQUFHLENBQUMseUNBQXlDaUUsS0FBS0MsU0FBUyxDQUFDcUcsZUFBZSxNQUFNO29CQUV6Rix1RkFBdUY7b0JBQ3ZGeEssUUFBUUMsR0FBRyxDQUFDO29CQUNaLElBQUk7d0JBQ0YsTUFBTW9NLHNCQUFzQjFMLGFBQWFDLE9BQU8sQ0FBQzt3QkFDakQsSUFBSXlMLHFCQUFxQjs0QkFDdkIsTUFBTUMsc0JBQXNCcEksS0FBS3FJLEtBQUssQ0FBQ0Y7NEJBQ3ZDck0sUUFBUUMsR0FBRyxDQUFDLDZEQUE2RG9NOzRCQUV6RSxxREFBcUQ7NEJBQ3JELElBQUlULHNCQUFzQjs0QkFFMUIsMkNBQTJDOzRCQUMzQ3ZELE9BQU95QyxPQUFPLENBQUN3QixxQkFBcUJqSCxPQUFPLENBQUMsQ0FBQyxDQUFDNEYsWUFBWW5ELFFBQXVCO2dDQUMvRSxJQUFJQSxXQUFXQSxRQUFRdEMsSUFBSSxJQUFJc0MsUUFBUTBFLFlBQVksRUFBRTtvQ0FDbkQsdUJBQXVCO29DQUN2QixNQUFNcEIsaUJBQWlCSCxlQUFlLGlCQUFpQixrQkFDckRBLGVBQWUsZ0JBQWdCLGVBQWVBO29DQUVoRFQsYUFBYSxDQUFDLEdBQUdZLGVBQWUsS0FBSyxDQUFDLENBQUMsR0FBR3RELFFBQVF0QyxJQUFJO29DQUN0RGdGLGFBQWEsQ0FBQyxHQUFHWSxlQUFlLFFBQVEsQ0FBQyxDQUFDLEdBQUd0RCxRQUFRMEUsWUFBWTtvQ0FFakV4TSxRQUFRQyxHQUFHLENBQUMsQ0FBQywyQkFBMkIsRUFBRW1MLGVBQWUsWUFBWSxFQUFFdEQsUUFBUXRDLElBQUksQ0FBQyxjQUFjLEVBQUVzQyxRQUFRMEUsWUFBWSxFQUFFO29DQUMxSFo7b0NBRUEsK0NBQStDO29DQUMvQyxJQUFJUixtQkFBbUJILFlBQVk7d0NBQ2pDVCxhQUFhLENBQUMsR0FBR1MsV0FBVyxLQUFLLENBQUMsQ0FBQyxHQUFHbkQsUUFBUXRDLElBQUk7d0NBQ2xEZ0YsYUFBYSxDQUFDLEdBQUdTLFdBQVcsUUFBUSxDQUFDLENBQUMsR0FBR25ELFFBQVEwRSxZQUFZO3dDQUM3RHhNLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlDQUF5QyxFQUFFZ0wsWUFBWTtvQ0FDdEU7Z0NBQ0Y7NEJBQ0Y7NEJBQ0FqTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxvQkFBb0IsRUFBRTJMLG9CQUFvQiwwQ0FBMEMsQ0FBQzs0QkFDbEc1TCxRQUFRQyxHQUFHLENBQUMseUNBQXlDaUUsS0FBS0MsU0FBUyxDQUFDcUcsZUFBZSxNQUFNOzRCQUV6RixtREFBbUQ7NEJBQ25ELElBQUk7Z0NBQ0YsMEZBQTBGO2dDQUMxRixJQUFJaUMsVUFBVUEsT0FBT0MsYUFBYSxFQUFFO29DQUNsQyxNQUFNQyxvQkFBb0IsSUFBSUMsWUFBWSx5QkFBeUI7d0NBQUVDLFFBQVFQO29DQUFvQjtvQ0FDakdHLE9BQU9DLGFBQWEsQ0FBQ0M7b0NBQ3JCM00sUUFBUUMsR0FBRyxDQUFDO2dDQUNkOzRCQUNGLEVBQUUsT0FBTzZNLFlBQVk7Z0NBQ25COU0sUUFBUXNCLEtBQUssQ0FBQyxpRUFBaUV3TDs0QkFDakY7d0JBQ0YsT0FBTzs0QkFDTDlNLFFBQVFDLEdBQUcsQ0FBQzt3QkFDZDtvQkFDRixFQUFFLE9BQU9xQixPQUFPO3dCQUNkdEIsUUFBUXNCLEtBQUssQ0FBQyxzRUFBc0VBO29CQUN0RjtvQkFFQSx5RUFBeUU7b0JBQ3pFLHNEQUFzRDtvQkFDdEQsTUFBTXlMLHNCQUFzQjt3QkFBQzt3QkFBUzt3QkFBZ0I7d0JBQWlCO3dCQUFjO3FCQUFVO29CQUUvRixzREFBc0Q7b0JBQ3RELElBQUlDLG1CQUFtQjtvQkFFdkIsb0VBQW9FO29CQUNwRWhOLFFBQVFDLEdBQUcsQ0FBQyx5REFBeURpRSxLQUFLQyxTQUFTLENBQUNxRyxlQUFlLE1BQU07b0JBQ3pHLE1BQU15QyxnQkFBZ0I1RSxPQUFPakQsSUFBSSxDQUFDb0Y7b0JBQ2xDeEssUUFBUUMsR0FBRyxDQUFDLDhCQUE4QmdOLGNBQWNuRSxJQUFJLENBQUM7b0JBRTdELHdFQUF3RTtvQkFDeEUsTUFBTW9FLGlCQUFpQixJQUFJckM7b0JBRTNCLDhDQUE4QztvQkFDOUMsSUFBSyxJQUFJdEYsSUFBSSxHQUFHQSxJQUFJMEgsY0FBYzdJLE1BQU0sRUFBRW1CLElBQUs7d0JBQzdDLE1BQU1tRixNQUFNdUMsYUFBYSxDQUFDMUgsRUFBRTt3QkFDNUIsSUFBSW1GLElBQUltQixRQUFRLENBQUMsVUFBVTs0QkFDekIsTUFBTXNCLFVBQVV6QyxJQUFJb0IsT0FBTyxDQUFDLFNBQVM7NEJBQ3JDLE1BQU1QLGFBQWEsR0FBRzRCLFFBQVEsUUFBUSxDQUFDOzRCQUV2QywrREFBK0Q7NEJBQy9ELE1BQU1DLGdCQUFnQkQsWUFBWSxpQkFBaUIsa0JBQWtCQTs0QkFFckUsNENBQTRDOzRCQUM1QyxJQUFJRCxlQUFlNUIsR0FBRyxDQUFDOEIsZ0JBQWdCO2dDQUNyQzs0QkFDRjs0QkFFQXBOLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFa04sUUFBUSxDQUFDLENBQUMsRUFBRTtnQ0FDekMzSCxNQUFNZ0YsYUFBYSxDQUFDRSxJQUFJO2dDQUN4QnFCLFNBQVN2QixhQUFhLENBQUNlLFdBQVc7NEJBQ3BDOzRCQUVBLElBQUlmLGFBQWEsQ0FBQ0UsSUFBSSxJQUFJRixhQUFhLENBQUNlLFdBQVcsRUFBRTtnQ0FDbkR5QjtnQ0FDQUUsZUFBZTFCLEdBQUcsQ0FBQzRCO2dDQUNuQnBOLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFa04sUUFBUSxZQUFZLEVBQUUzQyxhQUFhLENBQUNFLElBQUksQ0FBQyxjQUFjLEVBQUVGLGFBQWEsQ0FBQ2UsV0FBVyxFQUFFOzRCQUN6SDt3QkFDRjtvQkFDRjtvQkFFQXZMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxFQUFFK00sa0JBQWtCO29CQUU1RSwyRUFBMkU7b0JBQzNFLElBQUssSUFBSXpILElBQUksR0FBR0EsS0FBSyxJQUFJQSxJQUFLO3dCQUM1QixNQUFNa0csVUFBVSxDQUFDLFVBQVUsRUFBRWxHLEVBQUUsS0FBSyxDQUFDO3dCQUNyQyxNQUFNZ0csYUFBYSxDQUFDLFVBQVUsRUFBRWhHLEVBQUUsUUFBUSxDQUFDO3dCQUUzQyw0Q0FBNEM7d0JBQzVDLElBQUkySCxlQUFlNUIsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFL0YsR0FBRyxHQUFHOzRCQUN4Qzt3QkFDRjt3QkFFQSxJQUFJaUYsYUFBYSxDQUFDaUIsUUFBUSxJQUFJakIsYUFBYSxDQUFDZSxXQUFXLEVBQUU7NEJBQ3ZEeUI7NEJBQ0FFLGVBQWUxQixHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVqRyxHQUFHOzRCQUNuQ3ZGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlDQUF5QyxFQUFFc0YsRUFBRSxZQUFZLEVBQUVpRixhQUFhLENBQUNpQixRQUFRLENBQUMsY0FBYyxFQUFFakIsYUFBYSxDQUFDZSxXQUFXLEVBQUU7d0JBQzVJO29CQUNGO29CQUVBLGtGQUFrRjtvQkFDbEYsSUFBSyxJQUFJaEcsSUFBSSxHQUFHQSxLQUFLLElBQUlBLElBQUs7d0JBQzVCLE1BQU1rRyxVQUFVLENBQUMsZ0JBQWdCLEVBQUVsRyxFQUFFLEtBQUssQ0FBQzt3QkFDM0MsTUFBTWdHLGFBQWEsQ0FBQyxnQkFBZ0IsRUFBRWhHLEVBQUUsUUFBUSxDQUFDO3dCQUVqRCw0Q0FBNEM7d0JBQzVDLElBQUkySCxlQUFlNUIsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUUvRixHQUFHLEdBQUc7NEJBQzlDO3dCQUNGO3dCQUVBLElBQUlpRixhQUFhLENBQUNpQixRQUFRLElBQUlqQixhQUFhLENBQUNlLFdBQVcsRUFBRTs0QkFDdkR5Qjs0QkFDQUUsZUFBZTFCLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFakcsR0FBRzs0QkFDekN2RixRQUFRQyxHQUFHLENBQUMsQ0FBQyw4Q0FBOEMsRUFBRXNGLEVBQUUsWUFBWSxFQUFFaUYsYUFBYSxDQUFDaUIsUUFBUSxDQUFDLGNBQWMsRUFBRWpCLGFBQWEsQ0FBQ2UsV0FBVyxFQUFFO3dCQUNqSjtvQkFDRjtvQkFFQXZMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxFQUFFK00sa0JBQWtCO29CQUU1RSxrRUFBa0U7b0JBQ2xFLHFEQUFxRDtvQkFDckQsSUFBSUEsbUJBQW1CLEdBQUc7d0JBQ3hCaE4sUUFBUUMsR0FBRyxDQUFDO3dCQUVaLDBEQUEwRDt3QkFDMUQsTUFBTW9OLG9CQUFzRSxFQUFFO3dCQUU5RSwwREFBMEQ7d0JBQzFELElBQUssSUFBSTlILElBQUksR0FBR0EsS0FBSyxJQUFJQSxJQUFLOzRCQUM1QixNQUFNa0csVUFBVSxDQUFDLFVBQVUsRUFBRWxHLEVBQUUsS0FBSyxDQUFDOzRCQUNyQyxNQUFNZ0csYUFBYSxDQUFDLFVBQVUsRUFBRWhHLEVBQUUsUUFBUSxDQUFDOzRCQUMzQyxJQUFJaUYsYUFBYSxDQUFDaUIsUUFBUSxJQUFJakIsYUFBYSxDQUFDZSxXQUFXLElBQ3JELENBQUMyQixlQUFlNUIsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFL0YsR0FBRyxHQUFHO2dDQUN2QzhILGtCQUFrQkMsSUFBSSxDQUFDO29DQUNyQjlILE1BQU1nRixhQUFhLENBQUNpQixRQUFRO29DQUM1Qk0sU0FBU3ZCLGFBQWEsQ0FBQ2UsV0FBVztvQ0FDbENiLEtBQUssQ0FBQyxVQUFVLEVBQUVuRixHQUFHO2dDQUN2QjtnQ0FDQTJILGVBQWUxQixHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVqRyxHQUFHO2dDQUNuQ3ZGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHdCQUF3QixFQUFFc0YsRUFBRSxFQUFFLEVBQUVpRixhQUFhLENBQUNpQixRQUFRLEVBQUU7NEJBQ3ZFO3dCQUNGO3dCQUVBLGdFQUFnRTt3QkFDaEUsSUFBSyxJQUFJbEcsSUFBSSxHQUFHQSxLQUFLLElBQUlBLElBQUs7NEJBQzVCLE1BQU1rRyxVQUFVLENBQUMsZ0JBQWdCLEVBQUVsRyxFQUFFLEtBQUssQ0FBQzs0QkFDM0MsTUFBTWdHLGFBQWEsQ0FBQyxnQkFBZ0IsRUFBRWhHLEVBQUUsUUFBUSxDQUFDOzRCQUNqRCxJQUFJaUYsYUFBYSxDQUFDaUIsUUFBUSxJQUFJakIsYUFBYSxDQUFDZSxXQUFXLElBQ3JELENBQUMyQixlQUFlNUIsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUUvRixHQUFHLEdBQUc7Z0NBQzdDOEgsa0JBQWtCQyxJQUFJLENBQUM7b0NBQ3JCOUgsTUFBTWdGLGFBQWEsQ0FBQ2lCLFFBQVE7b0NBQzVCTSxTQUFTdkIsYUFBYSxDQUFDZSxXQUFXO29DQUNsQ2IsS0FBSyxDQUFDLGdCQUFnQixFQUFFbkYsR0FBRztnQ0FDN0I7Z0NBQ0EySCxlQUFlMUIsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUVqRyxHQUFHO2dDQUN6Q3ZGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFc0YsRUFBRSxFQUFFLEVBQUVpRixhQUFhLENBQUNpQixRQUFRLEVBQUU7NEJBQ3RFO3dCQUNGO3dCQUVBekwsUUFBUUMsR0FBRyxDQUFDLENBQUMsTUFBTSxFQUFFb04sa0JBQWtCakosTUFBTSxDQUFDLDBCQUEwQixDQUFDO3dCQUV6RSwwREFBMEQ7d0JBQzFELElBQUltSixrQkFBa0I7d0JBQ3RCLEtBQUssTUFBTXhILFFBQVFnSCxvQkFBcUI7NEJBQ3RDLHVDQUF1Qzs0QkFDdkMsTUFBTTNCLGlCQUFpQnJGLFNBQVMsa0JBQWtCLGlCQUNoREEsU0FBUyxlQUFlLGdCQUFnQkE7NEJBRTFDLGlEQUFpRDs0QkFDakQsSUFBSW1ILGVBQWU1QixHQUFHLENBQUN2RixTQUFTbUgsZUFBZTVCLEdBQUcsQ0FBQ0YsaUJBQWlCO2dDQUNsRXBMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFNBQVMsRUFBRThGLEtBQUssd0JBQXdCLENBQUM7Z0NBQ3REOzRCQUNGOzRCQUVBLElBQUl3SCxrQkFBa0JGLGtCQUFrQmpKLE1BQU0sRUFBRTtnQ0FDOUMscURBQXFEO2dDQUNyRG9HLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxLQUFLLENBQUMsQ0FBQyxHQUFHc0gsaUJBQWlCLENBQUNFLGdCQUFnQixDQUFDL0gsSUFBSTtnQ0FDdkVnRixhQUFhLENBQUMsR0FBR3pFLEtBQUssUUFBUSxDQUFDLENBQUMsR0FBR3NILGlCQUFpQixDQUFDRSxnQkFBZ0IsQ0FBQ3hCLE9BQU87Z0NBQzdFL0wsUUFBUUMsR0FBRyxDQUFDLENBQUMseUJBQXlCLEVBQUVvTixpQkFBaUIsQ0FBQ0UsZ0JBQWdCLENBQUM3QyxHQUFHLENBQUMsSUFBSSxFQUFFM0UsS0FBSyxFQUFFLEVBQUVzSCxpQkFBaUIsQ0FBQ0UsZ0JBQWdCLENBQUMvSCxJQUFJLEVBQUU7Z0NBQ3ZJK0g7Z0NBQ0FQO2dDQUNBRSxlQUFlMUIsR0FBRyxDQUFDekY7Z0NBRW5CLElBQUlpSCxvQkFBb0IsR0FBRztvQ0FDekJoTixRQUFRQyxHQUFHLENBQUMsQ0FBQywwREFBMEQsQ0FBQztvQ0FDeEU7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7d0JBRUEsa0RBQWtEO3dCQUNsRCxJQUFJK00sbUJBQW1CLEdBQUc7NEJBQ3hCaE4sUUFBUUMsR0FBRyxDQUFDOzRCQUVaLEtBQUssTUFBTThGLFFBQVFnSCxvQkFBcUI7Z0NBQ3RDLHVDQUF1QztnQ0FDdkMsTUFBTTNCLGlCQUFpQnJGLFNBQVMsa0JBQWtCLGlCQUNoREEsU0FBUyxlQUFlLGdCQUFnQkE7Z0NBRTFDLGlEQUFpRDtnQ0FDakQsSUFBSW1ILGVBQWU1QixHQUFHLENBQUN2RixTQUFTbUgsZUFBZTVCLEdBQUcsQ0FBQ0YsaUJBQWlCO29DQUNsRXBMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFNBQVMsRUFBRThGLEtBQUssd0NBQXdDLENBQUM7b0NBQ3RFO2dDQUNGO2dDQUVBLHVDQUF1QztnQ0FDdkN5RSxhQUFhLENBQUMsR0FBR3pFLEtBQUssS0FBSyxDQUFDLENBQUMsR0FBR3lFLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxLQUFLLENBQUMsQ0FBQyxJQUFJLEdBQUdBLEtBQUtpRyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLbEcsS0FBS29GLEtBQUssQ0FBQyxJQUFJO2dDQUNsSFgsYUFBYSxDQUFDLEdBQUd6RSxLQUFLLFFBQVEsQ0FBQyxDQUFDLEdBQUd5RSxhQUFhLENBQUMsR0FBR3pFLEtBQUssUUFBUSxDQUFDLENBQUMsSUFBSTtnQ0FDdkVpSDtnQ0FDQUUsZUFBZTFCLEdBQUcsQ0FBQ3pGO2dDQUNuQi9GLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFOEYsS0FBSyxFQUFFLEVBQUV5RSxhQUFhLENBQUMsR0FBR3pFLEtBQUssS0FBSyxDQUFDLENBQUMsRUFBRTtnQ0FFN0UsSUFBSWlILG9CQUFvQixHQUFHO29DQUN6QmhOLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlEQUFpRCxDQUFDO29DQUMvRDtnQ0FDRjs0QkFDRjs0QkFFQSxpRkFBaUY7NEJBQ2pGLElBQUkrTSxtQkFBbUIsR0FBRztnQ0FDeEJoTixRQUFRQyxHQUFHLENBQUM7Z0NBQ1osS0FBSyxNQUFNOEYsUUFBUWdILG9CQUFxQjtvQ0FDdEMsSUFBSSxDQUFDdkMsYUFBYSxDQUFDLEdBQUd6RSxLQUFLLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQ3lFLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxRQUFRLENBQUMsQ0FBQyxFQUFFO3dDQUN2RXlFLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxLQUFLLENBQUMsQ0FBQyxHQUFHLEdBQUdBLEtBQUtpRyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLbEcsS0FBS29GLEtBQUssQ0FBQyxJQUFJO3dDQUNqRlgsYUFBYSxDQUFDLEdBQUd6RSxLQUFLLFFBQVEsQ0FBQyxDQUFDLEdBQUc7d0NBQ25DaUg7d0NBQ0FoTixRQUFRQyxHQUFHLENBQUMsQ0FBQyw0QkFBNEIsRUFBRThGLE1BQU07d0NBQ2pELElBQUlpSCxvQkFBb0IsR0FBRztvQ0FDN0I7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7d0JBRUEsNEJBQTRCO3dCQUM1QmhOLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFK00sa0JBQWtCO29CQUM3RDtnQkFDRjtnQkFFQSxpQ0FBaUM7Z0JBQ2pDbkQsZ0JBQWdCMkQsZ0JBQWdCLEdBQUcxRDtnQkFDbkNELGdCQUFnQjVFLGNBQWMsR0FBR3VGO2dCQUVqQyxpRkFBaUY7Z0JBQ2pGLElBQUk5QyxjQUFjLFNBQVM7b0JBQ3pCLDZEQUE2RDtvQkFDN0QsTUFBTWdFLGNBQWMsSUFBSWI7b0JBQ3hCLE1BQU1jLGlCQUFpQixJQUFJZDtvQkFDM0IsSUFBSWUsc0JBQXNCO29CQUUxQixJQUFJcEIsZUFBZTt3QkFDakJuQyxPQUFPakQsSUFBSSxDQUFDb0YsZUFBZW5GLE9BQU8sQ0FBQ3FGLENBQUFBOzRCQUNqQyxJQUFJQSxJQUFJbUIsUUFBUSxDQUFDLFVBQVU7Z0NBQ3pCSCxZQUFZRixHQUFHLENBQUNkLElBQUlvQixPQUFPLENBQUMsU0FBUzs0QkFDdkMsT0FBTyxJQUFJcEIsSUFBSW1CLFFBQVEsQ0FBQyxhQUFhO2dDQUNuQ0YsZUFBZUgsR0FBRyxDQUFDZCxJQUFJb0IsT0FBTyxDQUFDLFlBQVk7NEJBQzdDO3dCQUNGO3dCQUVBLHVCQUF1Qjt3QkFDdkJKLFlBQVlyRyxPQUFPLENBQUNHLENBQUFBOzRCQUNsQixJQUFJbUcsZUFBZUwsR0FBRyxDQUFDOUYsT0FBTztnQ0FDNUJvRzs0QkFDRjt3QkFDRjtvQkFDRjtvQkFFQTVMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlDQUFpQyxFQUFFMkwsb0JBQW9CLDZDQUE2QyxDQUFDO29CQUVsSCwrREFBK0Q7b0JBQy9ELElBQUlBLHNCQUFzQixHQUFHO3dCQUMzQjVMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlFQUFpRSxDQUFDO3dCQUUvRSxNQUFNOE0sc0JBQXNCOzRCQUFDOzRCQUFTOzRCQUFnQjs0QkFBaUI7NEJBQWM7eUJBQVU7d0JBRS9GLEtBQUssTUFBTWhILFFBQVFnSCxvQkFBcUI7NEJBQ3RDLDBDQUEwQzs0QkFDMUMsSUFBSXZDLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxLQUFLLENBQUMsQ0FBQyxJQUFJeUUsYUFBYSxDQUFDLEdBQUd6RSxLQUFLLFFBQVEsQ0FBQyxDQUFDLEVBQUU7Z0NBQ3JFOzRCQUNGOzRCQUVBLGtDQUFrQzs0QkFDbEN5RSxhQUFhLENBQUMsR0FBR3pFLEtBQUssS0FBSyxDQUFDLENBQUMsR0FBR3lFLGFBQWEsQ0FBQyxHQUFHekUsS0FBSyxLQUFLLENBQUMsQ0FBQyxJQUFJLEdBQUdBLEtBQUtpRyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLbEcsS0FBS29GLEtBQUssQ0FBQyxJQUFJOzRCQUNsSFgsYUFBYSxDQUFDLEdBQUd6RSxLQUFLLFFBQVEsQ0FBQyxDQUFDLEdBQUd5RSxhQUFhLENBQUMsR0FBR3pFLEtBQUssUUFBUSxDQUFDLENBQUMsSUFBSTs0QkFDdkU2Rjs0QkFFQTVMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlEQUFpRCxFQUFFOEYsTUFBTTs0QkFFdEUsSUFBSTZGLHVCQUF1QixHQUFHO2dDQUM1Qjs0QkFDRjt3QkFDRjt3QkFFQSxpREFBaUQ7d0JBQ2pEL0IsZ0JBQWdCNUUsY0FBYyxHQUFHdUY7d0JBQ2pDeEssUUFBUUMsR0FBRyxDQUFDLENBQUMsb0NBQW9DLEVBQUUyTCxvQkFBb0Isc0JBQXNCLENBQUM7b0JBQ2hHO2dCQUNGO2dCQUVBNUwsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ2lFLEtBQUtDLFNBQVMsQ0FBQzBGLGlCQUFpQixNQUFNO2dCQUV0RixzQkFBc0I7Z0JBQ3RCLE1BQU00RCxtQkFBbUIsTUFBTTdKLGNBQWNpQixjQUFjLENBQUNnRjtnQkFFNUQsNEJBQTRCO2dCQUM1QjdKLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJpRSxLQUFLQyxTQUFTLENBQUNzSixrQkFBa0IsTUFBTTtnQkFFaEYsMEJBQTBCO2dCQUMxQixJQUFJM0gsWUFBWUEsV0FBVztnQkFFM0I5RixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osT0FBT3dOO1lBQ1QsRUFBRSxPQUFPQyxlQUFvQjtnQkFDM0IxTixRQUFRc0IsS0FBSyxDQUFDLGtDQUFrQ29NO2dCQUVoRCxzQkFBc0I7Z0JBQ3RCLE1BQU1uRyxlQUFlbUcsY0FBYzVMLFFBQVEsRUFBRUksTUFBTVosU0FDakRvTSxjQUFjaEksT0FBTyxJQUNyQjtnQkFFRjFGLFFBQVFzQixLQUFLLENBQUMsbUJBQW1CaUc7Z0JBQ2pDLE1BQU07b0JBQUVqRyxPQUFPaUc7Z0JBQWE7WUFDOUI7UUFDRixFQUFFLE9BQU9qRyxPQUFZO1lBQ25CdEIsUUFBUXNCLEtBQUssQ0FBQyw0QkFBNEJBO1lBRTFDLGdFQUFnRTtZQUNoRSxJQUFJQSxNQUFNQSxLQUFLLEVBQUU7Z0JBQ2YsTUFBTUE7WUFDUjtZQUVBLDhCQUE4QjtZQUM5QixNQUFNQSxNQUFNUSxRQUFRLEVBQUVJLFFBQVE7Z0JBQUVaLE9BQU87WUFBd0I7UUFDakU7SUFDRjtJQUVBOztHQUVDLEdBQ0RvSSx5QkFBeUIsT0FBTyxFQUFFcEYsUUFBUSxFQUFFRyxTQUFTLEVBQUU4RSxLQUFLLEVBQTBGO1FBQ3BKLElBQUk7WUFDRixNQUFNekksUUFBUUo7WUFDZCxJQUFJLENBQUNJLE9BQU8sTUFBTTtnQkFBRVEsT0FBTztZQUFnQztZQUMzRCxNQUFNUSxXQUFXLE1BQU1yQyw2Q0FBS0EsQ0FBQ3NDLElBQUksQ0FBQyxHQUFHaEMsYUFBYSwwQkFBMEIsQ0FBQyxFQUFFO2dCQUM3RXVFO2dCQUNBRztnQkFDQThFO1lBQ0YsR0FBRztnQkFDRGhKLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVPLE9BQU87Z0JBQ3BDO2dCQUNBTixTQUFTO1lBQ1g7WUFDQSxPQUFPc0IsU0FBU0ksSUFBSTtRQUN0QixFQUFFLE9BQU9aLE9BQVk7WUFDbkJ0QixRQUFRc0IsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcEQsTUFBTUEsTUFBTVEsUUFBUSxFQUFFSSxRQUFRO2dCQUFFWixPQUFPO1lBQXNDO1FBQy9FO0lBQ0Y7QUFDRixFQUFFO0FBRUYsNENBQTRDO0FBQzVDLE1BQU1xTSxVQUFVL04sa0VBQStCLElBQUksQ0FBRTtBQUM5QyxNQUFNZ08saUJBQWlCO0lBQzVCQyxNQUFNLE9BQU9DO1FBQ1gsSUFBSTtZQUNGLE1BQU1oTixRQUFRLE1BQTZCLEdBQUdILENBQTZCLEdBQUc7WUFDOUUsSUFBSSxDQUFDRyxPQUFPLE1BQU0sSUFBSTZELE1BQU07WUFDNUIsTUFBTTdDLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDc0MsSUFBSSxDQUFDLEdBQUc0TCxRQUFRLEtBQUssQ0FBQyxFQUFFO2dCQUNuREksWUFBWUQ7Z0JBQ1o1RSxjQUFjO1lBQ2hCLEdBQUc7Z0JBQ0QzSSxTQUFTO29CQUFFYyxlQUFlLENBQUMsT0FBTyxFQUFFUCxPQUFPO2dCQUFDO1lBQzlDO1lBQ0EsT0FBT2dCLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPWixPQUFZO1lBQ25CLE1BQU1BLE1BQU1RLFFBQVEsRUFBRUksUUFBUTtnQkFBRVosT0FBTztZQUF1QjtRQUNoRTtJQUNGO0lBQ0EwTSxRQUFRLE9BQU9GO1FBQ2IsSUFBSTtZQUNGLE1BQU1oTixRQUFRLE1BQTZCLEdBQUdILENBQTZCLEdBQUc7WUFDOUUsSUFBSSxDQUFDRyxPQUFPLE1BQU0sSUFBSTZELE1BQU07WUFDNUIsTUFBTTdDLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDc0MsSUFBSSxDQUFDLEdBQUc0TCxRQUFRLE9BQU8sQ0FBQyxFQUFFO2dCQUNyREksWUFBWUQ7Z0JBQ1o1RSxjQUFjO1lBQ2hCLEdBQUc7Z0JBQ0QzSSxTQUFTO29CQUFFYyxlQUFlLENBQUMsT0FBTyxFQUFFUCxPQUFPO2dCQUFDO1lBQzlDO1lBQ0EsT0FBT2dCLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPWixPQUFZO1lBQ25CLE1BQU1BLE1BQU1RLFFBQVEsRUFBRUksUUFBUTtnQkFBRVosT0FBTztZQUF5QjtRQUNsRTtJQUNGO0lBQ0EyTSxNQUFNLE9BQU9IO1FBQ1gsSUFBSTtZQUNGLE1BQU1oTixRQUFRLE1BQTZCLEdBQUdILENBQTZCLEdBQUc7WUFDOUUsSUFBSSxDQUFDRyxPQUFPLE1BQU0sSUFBSTZELE1BQU07WUFDNUIsTUFBTTdDLFdBQVcsTUFBTXJDLDZDQUFLQSxDQUFDc0MsSUFBSSxDQUFDLEdBQUc0TCxRQUFRLEtBQUssQ0FBQyxFQUFFO2dCQUNuREksWUFBWUQ7Z0JBQ1o1RSxjQUFjO1lBQ2hCLEdBQUc7Z0JBQ0QzSSxTQUFTO29CQUFFYyxlQUFlLENBQUMsT0FBTyxFQUFFUCxPQUFPO2dCQUFDO1lBQzlDO1lBQ0EsT0FBT2dCLFNBQVNJLElBQUk7UUFDdEIsRUFBRSxPQUFPWixPQUFZO1lBQ25CLE1BQU1BLE1BQU1RLFFBQVEsRUFBRUksUUFBUTtnQkFBRVosT0FBTztZQUEwQjtRQUNuRTtJQUNGO0FBQ0YsRUFBRTtBQUVGLGlFQUFlO0lBQ2JHO0lBQ0E4QjtJQUNBSztJQUNBUjtJQUNBd0s7QUFDRixDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2l2YXNcXE9uZURyaXZlXFxEZXNrdG9wXFxmaW5hbFxcV0VEWkFUX1xcZnJvbnRlbmRcXHNlcnZpY2VzXFxhcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc2VydmljZXMvYXBpLnRzXHJcbmltcG9ydCBheGlvcywgeyBBeGlvc1JlcXVlc3RDb25maWcgfSBmcm9tICdheGlvcyc7XHJcbmltcG9ydCB7IG11bHRpcGFydFVwbG9hZCwgTXVsdGlwYXJ0VXBsb2FkUHJvZ3Jlc3MgfSBmcm9tICcuLi91dGlscy9zM011bHRpcGFydFVwbG9hZCc7XHJcblxyXG4vLyBVc2UgZW52aXJvbm1lbnQgdmFyaWFibGUgb3IgZmFsbGJhY2sgdG8gbG9jYWxob3N0IGZvciBkZXZlbG9wbWVudFxyXG5jb25zdCBCQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly8xMjcuMC4wLjE6NTAwMC9odWInO1xyXG5cclxuXHJcbi8vIExvZyB0aGUgQVBJIFVSTCBiZWluZyB1c2VkXHJcbi8vIGNvbnNvbGUubG9nKGBBUEkgU2VydmljZSB1c2luZyBiYXNlIFVSTDogJHtCQVNFX1VSTH1gKTtcclxuXHJcbi8vIEZvciBkZXZlbG9wbWVudCwgdXNlIGEgQ09SUyBwcm94eSBpZiBuZWVkZWRcclxuY29uc3QgQVBJX0JBU0VfVVJMID0gQkFTRV9VUkw7XHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gIC8vIFVuY29tbWVudCB0aGUgbGluZSBiZWxvdyB0byB1c2UgYSBDT1JTIHByb3h5IGluIGRldmVsb3BtZW50IGlmIG5lZWRlZFxyXG4gIC8vIEFQSV9CQVNFX1VSTCA9IGBodHRwczovL2NvcnMtYW55d2hlcmUuaGVyb2t1YXBwLmNvbS8ke0JBU0VfVVJMfWA7XHJcblxyXG4gIC8vIExvZyB0aGUgQVBJIFVSTCBmb3IgZGVidWdnaW5nXHJcbiAgY29uc29sZS5sb2coJ0RldmVsb3BtZW50IG1vZGUgZGV0ZWN0ZWQsIHVzaW5nIEFQSSBVUkw6JywgQVBJX0JBU0VfVVJMKTtcclxufVxyXG5cclxuLy8gTG9nIHRoZSBBUEkgVVJMIGJlaW5nIHVzZWRcclxuY29uc29sZS5sb2coYEFQSSBTZXJ2aWNlIHVzaW5nIGJhc2UgVVJMOiAke0FQSV9CQVNFX1VSTH1gKTtcclxuY29uc3QgVE9LRU5fS0VZID0gJ3Rva2VuJzsgIC8vIEtlZXAgdXNpbmcgeW91ciBleGlzdGluZyB0b2tlbiBrZXlcclxuY29uc3QgSldUX1RPS0VOX0tFWSA9ICdqd3RfdG9rZW4nOyAvLyBBbHRlcm5hdGl2ZSBrZXkgZm9yIGNvbXBhdGliaWxpdHlcclxuXHJcbi8vIENyZWF0ZSBheGlvcyBpbnN0YW5jZVxyXG5jb25zdCBhcGkgPSBheGlvcy5jcmVhdGUoe1xyXG4gIGJhc2VVUkw6IEFQSV9CQVNFX1VSTCxcclxuICBoZWFkZXJzOiB7XHJcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gIH0sXHJcbiAgdGltZW91dDogNjAwMDAsIC8vIDYwIHNlY29uZHMgZGVmYXVsdCB0aW1lb3V0XHJcbiAgd2l0aENyZWRlbnRpYWxzOiBmYWxzZSAvLyBIZWxwcyB3aXRoIENPUlMgaXNzdWVzXHJcbn0pO1xyXG5cclxuLy8gSGVscGVyIHRvIGdldCB0b2tlbiBmcm9tIHN0b3JhZ2UgKGNoZWNraW5nIGJvdGgga2V5cylcclxuY29uc3QgZ2V0VG9rZW4gPSAoKTogc3RyaW5nIHwgbnVsbCA9PiB7XHJcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbDtcclxuICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oVE9LRU5fS0VZKTtcclxufTtcclxuXHJcbi8vIEhlbHBlciB0byBzYXZlIHRva2VuIHRvIHN0b3JhZ2UgKHVzaW5nIGJvdGgga2V5cyBmb3IgY29tcGF0aWJpbGl0eSlcclxuY29uc3Qgc2F2ZVRva2VuID0gKHRva2VuOiBzdHJpbmcpOiB2b2lkID0+IHtcclxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcclxuICBjb25zb2xlLmxvZygnU2F2aW5nIHRva2VuIHRvIGxvY2FsU3RvcmFnZTonLCB0b2tlbi5zdWJzdHJpbmcoMCwgMTUpICsgJy4uLicpO1xyXG4gIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFRPS0VOX0tFWSwgdG9rZW4pO1xyXG4gIGxvY2FsU3RvcmFnZS5zZXRJdGVtKEpXVF9UT0tFTl9LRVksIHRva2VuKTsgLy8gRm9yIGNvbXBhdGliaWxpdHkgd2l0aCBvdGhlciBjb21wb25lbnRzXHJcbn07XHJcblxyXG4vLyBSZXF1ZXN0IGludGVyY2VwdG9yIHRvIGFkZCBhdXRoIHRva2VuIHRvIGFsbCByZXF1ZXN0c1xyXG5hcGkuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKFxyXG4gIChjb25maWcpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0VG9rZW4oKTtcclxuICAgIGlmICh0b2tlbiAmJiBjb25maWcuaGVhZGVycykge1xyXG4gICAgICBjb25maWcuaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke3Rva2VufWA7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gY29uZmlnO1xyXG4gIH0sXHJcbiAgKGVycm9yKSA9PiB7XHJcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xyXG4gIH1cclxuKTtcclxuXHJcbi8vIEF1dGhlbnRpY2F0aW9uIHNlcnZpY2VzXHJcbmV4cG9ydCBjb25zdCBhdXRoU2VydmljZSA9IHtcclxuICAvLyBSZWdpc3RlciBhIG5ldyB1c2VyXHJcbiAgc2lnbnVwOiBhc3luYyAoc2lnbnVwRGF0YTogYW55KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygnQXR0ZW1wdGluZyBzaWdudXAgd2l0aCBkYXRhOicsIHtcclxuICAgICAgICAuLi5zaWdudXBEYXRhLFxyXG4gICAgICAgIHBhc3N3b3JkOiBzaWdudXBEYXRhLnBhc3N3b3JkID8gJyoqKioqKioqJyA6IHVuZGVmaW5lZFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtCQVNFX1VSTH0vc2lnbnVwYCwgc2lnbnVwRGF0YSwge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1NpZ251cCByZXNwb25zZSByZWNlaXZlZDonLCB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBoYXNUb2tlbjogISFyZXNwb25zZS5kYXRhLnRva2VuXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudG9rZW4pIHtcclxuICAgICAgICBzYXZlVG9rZW4ocmVzcG9uc2UuZGF0YS50b2tlbik7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdObyB0b2tlbiByZWNlaXZlZCBpbiBzaWdudXAgcmVzcG9uc2UnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJTaWdudXAgZXJyb3I6XCIsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogXCJBbiBlcnJvciBvY2N1cnJlZCBkdXJpbmcgc2lnbnVwXCIgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBSZWdpc3RlciBhIG5ldyB2ZW5kb3JcclxuICByZWdpc3RlclZlbmRvcjogYXN5bmMgKHZlbmRvckRhdGE6IGFueSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ0F0dGVtcHRpbmcgdmVuZG9yIHJlZ2lzdHJhdGlvbiB3aXRoIGRhdGE6Jywge1xyXG4gICAgICAgIC4uLnZlbmRvckRhdGEsXHJcbiAgICAgICAgcGFzc3dvcmQ6IHZlbmRvckRhdGEucGFzc3dvcmQgPyAnKioqKioqKionIDogdW5kZWZpbmVkXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5wb3N0KGAke0JBU0VfVVJMfS9yZWdpc3Rlci12ZW5kb3JgLCB2ZW5kb3JEYXRhLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnVmVuZG9yIHJlZ2lzdHJhdGlvbiByZXNwb25zZSByZWNlaXZlZDonLCB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBoYXNUb2tlbjogISFyZXNwb25zZS5kYXRhLnRva2VuXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudG9rZW4pIHtcclxuICAgICAgICBzYXZlVG9rZW4ocmVzcG9uc2UuZGF0YS50b2tlbik7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdObyB0b2tlbiByZWNlaXZlZCBpbiB2ZW5kb3IgcmVnaXN0cmF0aW9uIHJlc3BvbnNlJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiVmVuZG9yIHJlZ2lzdHJhdGlvbiBlcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvci5yZXNwb25zZT8uZGF0YSB8fCB7IGVycm9yOiBcIkFuIGVycm9yIG9jY3VycmVkIGR1cmluZyB2ZW5kb3IgcmVnaXN0cmF0aW9uXCIgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBHZXQgYnVzaW5lc3MgdHlwZXNcclxuICBnZXRCdXNpbmVzc1R5cGVzOiBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtCQVNFX1VSTH0vYnVzaW5lc3MtdHlwZXNgKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuYnVzaW5lc3NfdHlwZXM7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJHZXQgYnVzaW5lc3MgdHlwZXMgZXJyb3I6XCIsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogXCJBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBmZXRjaGluZyBidXNpbmVzcyB0eXBlc1wiIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IHZlbmRvciBwcm9maWxlXHJcbiAgZ2V0VmVuZG9yUHJvZmlsZTogYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gICAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgdGhyb3cgeyBlcnJvcjogJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtCQVNFX1VSTH0vdmVuZG9yLXByb2ZpbGVgLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEucHJvZmlsZTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkdldCB2ZW5kb3IgcHJvZmlsZSBlcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvci5yZXNwb25zZT8uZGF0YSB8fCB7IGVycm9yOiBcIkFuIGVycm9yIG9jY3VycmVkIHdoaWxlIGZldGNoaW5nIHZlbmRvciBwcm9maWxlXCIgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuXHJcblxyXG5cclxuXHJcbiAgLy8gTG9naW4gd2l0aCBlbWFpbC9tb2JpbGUgYW5kIHBhc3N3b3JkXHJcbiAgLy8gSW4gc2VydmljZXMvYXBpLnRzIC0gbG9naW4gZnVuY3Rpb25cclxuICBsb2dpbjogYXN5bmMgKGNyZWRlbnRpYWxzOiB7IGVtYWlsPzogc3RyaW5nOyBtb2JpbGVfbnVtYmVyPzogc3RyaW5nOyBwYXNzd29yZDogc3RyaW5nIH0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIGxvZ2luIHdpdGg6Jywge1xyXG4gICAgICAgIGVtYWlsOiBjcmVkZW50aWFscy5lbWFpbCxcclxuICAgICAgICBtb2JpbGVfbnVtYmVyOiBjcmVkZW50aWFscy5tb2JpbGVfbnVtYmVyLFxyXG4gICAgICAgIHBhc3N3b3JkOiAnKioqKioqKionXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdCgnL2xvZ2luJywgY3JlZGVudGlhbHMpO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ0xvZ2luIHJlc3BvbnNlIHJlY2VpdmVkOicsIHtcclxuICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgIGhhc1Rva2VuOiAhIXJlc3BvbnNlLmRhdGEudG9rZW4sXHJcbiAgICAgICAgdG9rZW5QcmV2aWV3OiByZXNwb25zZS5kYXRhLnRva2VuID8gYCR7cmVzcG9uc2UuZGF0YS50b2tlbi5zdWJzdHJpbmcoMCwgMTApfS4uLmAgOiAnbm9uZSdcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBTYXZlIHRva2VuIHRvIGxvY2FsU3RvcmFnZSB3aXRoIGV4cGxpY2l0IGNvbnNvbGUgbG9nc1xyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS50b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdTYXZpbmcgdG9rZW4gdG8gbG9jYWxTdG9yYWdlLi4uJyk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Rva2VuJywgcmVzcG9uc2UuZGF0YS50b2tlbik7XHJcblxyXG4gICAgICAgIC8vIFZlcmlmeSB0b2tlbiB3YXMgc2F2ZWRcclxuICAgICAgICBjb25zdCBzYXZlZFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coYFRva2VuIHZlcmlmaWNhdGlvbjogJHtzYXZlZFRva2VuID8gJ1N1Y2Nlc3NmdWxseSBzYXZlZCcgOiAnRmFpbGVkIHRvIHNhdmUnfWApO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gdG9rZW4gcmVjZWl2ZWQgaW4gbG9naW4gcmVzcG9uc2UnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGVycm9yOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ludmFsaWQgY3JlZGVudGlhbHMnIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gQXV0aGVudGljYXRlIHdpdGggQ2xlcmtcclxuICBjbGVya0F1dGg6IGFzeW5jIChjbGVya0RhdGE6IHtcclxuICAgIGNsZXJrX3Rva2VuOiBzdHJpbmc7XHJcbiAgICB1c2VyX3R5cGU6IHN0cmluZztcclxuICAgIHVzZXJfZW1haWw/OiBzdHJpbmc7XHJcbiAgICB1c2VyX25hbWU/OiBzdHJpbmc7XHJcbiAgICB1c2VyX2lkPzogc3RyaW5nO1xyXG4gIH0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIENsZXJrIGF1dGhlbnRpY2F0aW9uJyk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KCcvY2xlcmtfYXV0aCcsIGNsZXJrRGF0YSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnQ2xlcmsgYXV0aCByZXNwb25zZSByZWNlaXZlZDonLCB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBoYXNUb2tlbjogISFyZXNwb25zZS5kYXRhLnRva2VuXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gU2F2ZSB0b2tlbiB0byBsb2NhbFN0b3JhZ2VcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudG9rZW4pIHtcclxuICAgICAgICBzYXZlVG9rZW4ocmVzcG9uc2UuZGF0YS50b2tlbik7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdObyB0b2tlbiByZWNlaXZlZCBpbiBDbGVyayBhdXRoIHJlc3BvbnNlJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdDbGVyayBhdXRoZW50aWNhdGlvbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHsgZXJyb3I6ICdDbGVyayBhdXRoZW50aWNhdGlvbiBmYWlsZWQnIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gQ2hlY2sgaWYgdXNlciBwcm9maWxlIGlzIGNvbXBsZXRlXHJcbiAgY2hlY2tQcm9maWxlOiBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuKCk7XHJcblxyXG4gICAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgICAgdGhyb3cgeyBlcnJvcjogJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnQ2hlY2tpbmcgdXNlciBwcm9maWxlJyk7XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtCQVNFX1VSTH0vY2hlY2stcHJvZmlsZWAsIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcblxyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignUHJvZmlsZSBjaGVjayBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHsgZXJyb3I6ICdGYWlsZWQgdG8gY2hlY2sgcHJvZmlsZScgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvLyBHZXQgdGhlIGN1cnJlbnQgdG9rZW5cclxuICBnZXRUb2tlbjogKCkgPT4ge1xyXG4gICAgcmV0dXJuIGdldFRva2VuKCk7XHJcbiAgfSxcclxuXHJcbiAgLy8gQ2hlY2sgaWYgdGhlIHVzZXIgaXMgYXV0aGVudGljYXRlZFxyXG4gIGlzQXV0aGVudGljYXRlZDogKCkgPT4ge1xyXG4gICAgcmV0dXJuICEhZ2V0VG9rZW4oKTtcclxuICB9LFxyXG5cclxuICAvLyBMb2dvdXQgLSBjbGVhciB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gIGxvZ291dDogKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ0xvZ2dpbmcgb3V0IGFuZCByZW1vdmluZyB0b2tlbnMnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFRPS0VOX0tFWSk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShKV1RfVE9LRU5fS0VZKTtcclxuICB9LFxyXG59O1xyXG5cclxuLy8gVXNlciBzZXJ2aWNlc1xyXG5leHBvcnQgY29uc3QgdXNlclNlcnZpY2UgPSB7XHJcbiAgLy8gR2V0IHVzZXIgZGV0YWlscyAtIHVzZXMgR0VUIG1ldGhvZCB3aXRoIGV4cGxpY2l0IHRva2VuIGluIGhlYWRlclxyXG4gIGdldFVzZXJEZXRhaWxzOiBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuKCk7XHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICB0aHJvdyB7IGVycm9yOiAnTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnIH07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyB1c2VyIGRldGFpbHMnKTtcclxuXHJcbiAgICAgIC8vIFNldCB0aGUgY29ycmVjdCBBdXRob3JpemF0aW9uIGZvcm1hdCAoQmVhcmVyICsgdG9rZW4pXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAke0JBU0VfVVJMfS91c2VyLWRldGFpbHNgLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB1c2VyIGRldGFpbHM6XCIsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCB1c2VyIGRldGFpbHMnIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBkYXRlIHVzZXIgZGV0YWlsc1xyXG4gIHVwZGF0ZVVzZXI6IGFzeW5jICh1c2VyRGF0YToge1xyXG4gICAgbmFtZTogc3RyaW5nO1xyXG4gICAgZW1haWw6IHN0cmluZztcclxuICAgIG1vYmlsZV9udW1iZXI/OiBzdHJpbmc7XHJcbiAgICBkb2I/OiBzdHJpbmc7XHJcbiAgICBtYXJpdGFsX3N0YXR1cz86IHN0cmluZztcclxuICAgIHBsYWNlPzogc3RyaW5nO1xyXG4gIH0pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyB1c2VyIGRldGFpbHMnKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnB1dCgnL3VwZGF0ZS11c2VyJywgdXNlckRhdGEpO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignVXBkYXRlIHVzZXIgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvci5yZXNwb25zZT8uZGF0YSB8fCB7IGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSB1c2VyJyB9O1xyXG4gICAgfVxyXG4gIH0sXHJcbn07XHJcblxyXG4vLyBIZWxwZXIgdG8gY2hlY2sgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkXHJcbmV4cG9ydCBjb25zdCBpc0F1dGhlbnRpY2F0ZWQgPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICByZXR1cm4gZmFsc2U7IC8vIEZvciBzZXJ2ZXItc2lkZSByZW5kZXJpbmdcclxuICB9XHJcbiAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gIHJldHVybiAhIXRva2VuOyAvLyBSZXR1cm4gdHJ1ZSBpZiB0b2tlbiBleGlzdHMsIGZhbHNlIG90aGVyd2lzZVxyXG59O1xyXG5cclxuLy8gVXBsb2FkIGFuZCBNZWRpYSBJbnRlcmZhY2VzXHJcbmV4cG9ydCBpbnRlcmZhY2UgUHJlc2lnbmVkVXJsUmVxdWVzdCB7XHJcbiAgbWVkaWFfdHlwZTogJ3Bob3RvJyB8ICd2aWRlbyc7XHJcbiAgbWVkaWFfc3VidHlwZTogc3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZDsgLy8gJ3N0b3J5JywgJ2ZsYXNoJywgJ2dsaW1wc2UnLCAnbW92aWUnIGZvciB2aWRlbzsgbnVsbCBmb3IgcGhvdG9cclxuICB2aWRlb19jYXRlZ29yeT86IHN0cmluZzsgLy8gJ215X3dlZGRpbmcnIG9yICd3ZWRkaW5nX3Zsb2cnXHJcbiAgZmlsZW5hbWU6IHN0cmluZztcclxuICBjb250ZW50X3R5cGU6IHN0cmluZztcclxuICBmaWxlX3NpemU6IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBQcmVzaWduZWRVcmxSZXNwb25zZSB7XHJcbiAgbWVkaWFfaWQ6IHN0cmluZztcclxuICBtdWx0aXBhcnQ6IHRydWU7XHJcbiAgdXBsb2FkX2lkOiBzdHJpbmc7XHJcbiAgczNfdXBsb2FkX2lkPzogc3RyaW5nO1xyXG4gIG1haW5fa2V5OiBzdHJpbmc7XHJcbiAgcGFydF91cmxzOiB7IHBhcnRfbnVtYmVyOiBudW1iZXI7IHVybDogc3RyaW5nIH1bXTtcclxuICB0aHVtYm5haWxfdXJsPzogc3RyaW5nO1xyXG4gIGV4cGlyZXNfaW5fc2Vjb25kcz86IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBDb21wbGV0ZVVwbG9hZFJlcXVlc3Qge1xyXG4gIG1lZGlhX2lkOiBzdHJpbmc7XHJcbiAgbWVkaWFfdHlwZTogJ3Bob3RvJyB8ICd2aWRlbyc7XHJcbiAgbWVkaWFfc3VidHlwZT86IHN0cmluZyB8IG51bGw7IC8vICdzdG9yeScsICdmbGFzaCcsICdnbGltcHNlJywgJ21vdmllJyBmb3IgdmlkZW87IG51bGwgZm9yIHBob3RvXHJcbiAgdGl0bGU/OiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XHJcbiAgdGFncz86IHN0cmluZ1tdO1xyXG4gIGR1cmF0aW9uPzogbnVtYmVyOyAvLyBmb3IgdmlkZW9zIG9ubHlcclxuICBwZXJzb25hbF9kZXRhaWxzPzoge1xyXG4gICAgY2FwdGlvbj86IHN0cmluZztcclxuICAgIGxpZmVfcGFydG5lcj86IHN0cmluZztcclxuICAgIHdlZGRpbmdfc3R5bGU/OiBzdHJpbmc7XHJcbiAgICBwbGFjZT86IHN0cmluZztcclxuICAgIGFkZGl0aW9uYWxfZGV0YWlscz86IFJlY29yZDxzdHJpbmcsIHN0cmluZz47XHJcbiAgfTtcclxuICB2ZW5kb3JfZGV0YWlscz86IHtcclxuICAgIHZlbnVlX25hbWU/OiBzdHJpbmc7XHJcbiAgICB2ZW51ZV9jb250YWN0Pzogc3RyaW5nO1xyXG4gICAgcGhvdG9ncmFwaGVyX25hbWU/OiBzdHJpbmc7XHJcbiAgICBwaG90b2dyYXBoZXJfY29udGFjdD86IHN0cmluZztcclxuICAgIG1ha2V1cF9hcnRpc3RfbmFtZT86IHN0cmluZztcclxuICAgIG1ha2V1cF9hcnRpc3RfY29udGFjdD86IHN0cmluZztcclxuICAgIGRlY29yYXRpb25fbmFtZT86IHN0cmluZztcclxuICAgIGRlY29yYXRpb25fY29udGFjdD86IHN0cmluZztcclxuICAgIGNhdGVyZXJfbmFtZT86IHN0cmluZztcclxuICAgIGNhdGVyZXJfY29udGFjdD86IHN0cmluZztcclxuICAgIFtrZXk6IHN0cmluZ106IHN0cmluZyB8IHVuZGVmaW5lZDtcclxuICB9O1xyXG4gIHRyYW5zY29kZWRfdmVyc2lvbnM/OiBhbnlbXTsgLy8gZm9yIHZpZGVvcyBvbmx5XHJcbiAgaXNfc3Rvcnk/OiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIENvbXBsZXRlVXBsb2FkUmVzcG9uc2Uge1xyXG4gIG1lc3NhZ2U6IHN0cmluZztcclxuICBwaG90b19pZD86IHN0cmluZztcclxuICB2aWRlb19pZD86IHN0cmluZztcclxuICB1cmw6IHN0cmluZztcclxuICB0aHVtYm5haWxfdXJsOiBzdHJpbmcgfCBudWxsO1xyXG4gIHN0YXR1cz86IHN0cmluZztcclxuICBtZWRpYV9pZD86IHN0cmluZztcclxuICBtZWRpYV90eXBlPzogJ3Bob3RvJyB8ICd2aWRlbyc7XHJcbiAgYWNjZXNzX3VybHM/OiB7XHJcbiAgICBtYWluOiBzdHJpbmc7XHJcbiAgICB0aHVtYm5haWw/OiBzdHJpbmc7XHJcbiAgICBwcmV2aWV3Pzogc3RyaW5nO1xyXG4gIH07XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRmFjZVZlcmlmaWNhdGlvblJlc3BvbnNlIHtcclxuICBtZXNzYWdlOiBzdHJpbmc7XHJcbiAgZmFjZV92ZXJpZmllZDogYm9vbGVhbjtcclxuICBmYWNlX2ltYWdlX3VybD86IHN0cmluZztcclxufVxyXG5cclxuLy8gVXBsb2FkIHNlcnZpY2VzXHJcbmV4cG9ydCBjb25zdCB1cGxvYWRTZXJ2aWNlID0ge1xyXG4gIC8qKlxyXG4gICAqIFZlcmlmeSB1c2VyJ3MgZmFjZSB1c2luZyBjYXB0dXJlZCBpbWFnZVxyXG4gICAqIEBwYXJhbSBmYWNlSW1hZ2UgQmFzZTY0IGVuY29kZWQgaW1hZ2UgZGF0YSAod2l0aG91dCBkYXRhIFVSTCBwcmVmaXgpXHJcbiAgICovXHJcbiAgdmVyaWZ5RmFjZTogYXN5bmMgKGZhY2VJbWFnZTogc3RyaW5nKTogUHJvbWlzZTxGYWNlVmVyaWZpY2F0aW9uUmVzcG9uc2U+ID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdTZW5kaW5nIGZhY2UgdmVyaWZpY2F0aW9uIHJlcXVlc3QnKTtcclxuICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG5cclxuICAgICAgLy8gQ3JlYXRlIHRoZSByZXF1ZXN0IHBheWxvYWRcclxuICAgICAgY29uc3QgcGF5bG9hZCA9IHsgZmFjZV9pbWFnZTogZmFjZUltYWdlIH07XHJcbiAgICAgIGNvbnN0IHBheWxvYWRTaXplID0gSlNPTi5zdHJpbmdpZnkocGF5bG9hZCkubGVuZ3RoO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1JlcXVlc3QgcGF5bG9hZCBzaXplOicsIHBheWxvYWRTaXplKTtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIHBheWxvYWQgaXMgdG9vIGxhcmdlXHJcbiAgICAgIGlmIChwYXlsb2FkU2l6ZSA+IDEwMDAwMDApIHtcclxuICAgICAgICBjb25zb2xlLndhcm4oJ1dhcm5pbmc6IFBheWxvYWQgaXMgdmVyeSBsYXJnZSwgd2hpY2ggbWF5IGNhdXNlIGlzc3VlcyB3aXRoIHRoZSBBUEknKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gTm8gbW9jayBpbXBsZW1lbnRhdGlvbiAtIGFsd2F5cyB1c2UgdGhlIHJlYWwgQVBJXHJcbiAgICAgIGNvbnNvbGUubG9nKCdVc2luZyByZWFsIGZhY2UgdmVyaWZpY2F0aW9uIEFQSScpO1xyXG5cclxuICAgICAgLy8gTWFrZSB0aGUgcmVhbCBBUEkgY2FsbFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYCR7QVBJX0JBU0VfVVJMfS92ZXJpZnktZmFjZWAsIHBheWxvYWQsIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiB0b2tlbiA/IGBCZWFyZXIgJHt0b2tlbn1gIDogJydcclxuICAgICAgICB9LFxyXG4gICAgICAgIHRpbWVvdXQ6IDYwMDAwLCAvLyA2MCBzZWNvbmRzXHJcbiAgICAgICAgd2l0aENyZWRlbnRpYWxzOiBmYWxzZVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdGYWNlIHZlcmlmaWNhdGlvbiByZXNwb25zZTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHZlcmlmeWluZyBmYWNlOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ZhY2UgdmVyaWZpY2F0aW9uIGZhaWxlZCcgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvKipcclxuICAgKiBHZXQgcHJlLXNpZ25lZCBVUkxzIGZvciB1cGxvYWRpbmcgbWVkaWEgdG8gUzNcclxuICAgKi9cclxuICBnZXRQcmVzaWduZWRVcmw6IGFzeW5jIChyZXF1ZXN0OiBQcmVzaWduZWRVcmxSZXF1ZXN0KTogUHJvbWlzZTxQcmVzaWduZWRVcmxSZXNwb25zZT4gPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ0dldHRpbmcgcHJlc2lnbmVkIFVSTCB3aXRoIHJlcXVlc3Q6JywgcmVxdWVzdCk7XHJcbiAgICAgIGNvbnN0IHRva2VuID0gZ2V0VG9rZW4oKTtcclxuICAgICAgaWYgKCF0b2tlbikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQgd2hlbiBnZXR0aW5nIHByZXNpZ25lZCBVUkwnKTtcclxuICAgICAgfVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYCR7QVBJX0JBU0VfVVJMfS9nZXQtdXBsb2FkLXVybGAsIHJlcXVlc3QsIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiB0b2tlbiA/IGBCZWFyZXIgJHt0b2tlbn1gIDogJydcclxuICAgICAgICB9LFxyXG4gICAgICAgIHRpbWVvdXQ6IDMwMDAwIC8vIDMwIHNlY29uZHMgdGltZW91dFxyXG4gICAgICB9KTtcclxuICAgICAgY29uc29sZS5sb2coJ1ByZXNpZ25lZCBVUkwgcmVzcG9uc2U6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgIC8vIFZhbGlkYXRlIHRoZSBuZXcgbXVsdGlwYXJ0IHJlc3BvbnNlXHJcbiAgICAgIGlmICghcmVzcG9uc2UuZGF0YS5tZWRpYV9pZCB8fCAhcmVzcG9uc2UuZGF0YS5tdWx0aXBhcnQgfHwgIXJlc3BvbnNlLmRhdGEucGFydF91cmxzIHx8ICFyZXNwb25zZS5kYXRhLnVwbG9hZF9pZCB8fCAhcmVzcG9uc2UuZGF0YS5tYWluX2tleSkge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCByZXNwb25zZSBmcm9tIGdldC11cGxvYWQtdXJsIEFQSScpO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHByZXNpZ25lZCBVUkw6JywgZXJyb3IpO1xyXG4gICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgdGhyb3cgeyBlcnJvcjogJ0F1dGhlbnRpY2F0aW9uIGZhaWxlZC4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi4nIH07XHJcbiAgICAgIH1cclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ZhaWxlZCB0byBnZXQgdXBsb2FkIFVSTCcgfTtcclxuICAgIH1cclxuICB9LFxyXG5cclxuICAvKipcclxuICAgKiBDb21wbGV0ZSB0aGUgdXBsb2FkIHByb2Nlc3MgYnkgbm90aWZ5aW5nIHRoZSBiYWNrZW5kXHJcbiAgICovXHJcbiAgY29tcGxldGVVcGxvYWQ6IGFzeW5jIChyZXF1ZXN0OiBDb21wbGV0ZVVwbG9hZFJlcXVlc3QpOiBQcm9taXNlPENvbXBsZXRlVXBsb2FkUmVzcG9uc2U+ID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdDb21wbGV0aW5nIHVwbG9hZCB3aXRoIHJlcXVlc3Q6JywgSlNPTi5zdHJpbmdpZnkocmVxdWVzdCwgbnVsbCwgMikpO1xyXG4gICAgICAvLyBWYWxpZGF0ZSB0aGUgcmVxdWVzdFxyXG4gICAgICBpZiAoIXJlcXVlc3QubWVkaWFfaWQpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdNaXNzaW5nIG1lZGlhX2lkIGluIGNvbXBsZXRlVXBsb2FkIHJlcXVlc3QnKTtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgbWVkaWFfaWQgaW4gcmVxdWVzdCcpO1xyXG4gICAgICB9XHJcbiAgICAgIGNvbnN0IHRva2VuID0gZ2V0VG9rZW4oKTtcclxuICAgICAgbGV0IHBheWxvYWQgPSB7IC4uLnJlcXVlc3QgfTtcclxuICAgICAgaWYgKHJlcXVlc3QubWVkaWFfdHlwZSA9PT0gJ3ZpZGVvJyAmJiBBcnJheS5pc0FycmF5KHJlcXVlc3QudmVuZG9yX2RldGFpbHMpKSB7XHJcbiAgICAgICAgY29uc3QgdmVuZG9yQXJyID0gcmVxdWVzdC52ZW5kb3JfZGV0YWlscztcclxuICAgICAgICBjb25zdCB2ZW5kb3JPYmo6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7fTtcclxuICAgICAgICBjb25zdCBrZXlzID0gW1xyXG4gICAgICAgICAgJ3ZlbnVlJyxcclxuICAgICAgICAgICdwaG90b2dyYXBoZXInLFxyXG4gICAgICAgICAgJ21ha2V1cF9hcnRpc3QnLFxyXG4gICAgICAgICAgJ2RlY29yYXRpb24nLFxyXG4gICAgICAgICAgJ2NhdGVyZXInLFxyXG4gICAgICAgIF07XHJcbiAgICAgICAga2V5cy5mb3JFYWNoKChrLCBpKSA9PiB7XHJcbiAgICAgICAgICBpZiAodmVuZG9yQXJyW2ldKSB7XHJcbiAgICAgICAgICAgIHZlbmRvck9ialtgJHtrfV9uYW1lYF0gPSB2ZW5kb3JBcnJbaV0ubmFtZSB8fCAnJztcclxuICAgICAgICAgICAgdmVuZG9yT2JqW2Ake2t9X2NvbnRhY3RgXSA9IHZlbmRvckFycltpXS5waG9uZSB8fCAnJztcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBmb3IgKGxldCBpID0gNTsgaSA8IHZlbmRvckFyci5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgdmVuZG9yT2JqW2BhZGRpdGlvbmFsX3ZlbmRvcl8ke2kgLSA0fV9uYW1lYF0gPSB2ZW5kb3JBcnJbaV0ubmFtZSB8fCAnJztcclxuICAgICAgICAgIHZlbmRvck9ialtgYWRkaXRpb25hbF92ZW5kb3JfJHtpIC0gNH1fY29udGFjdGBdID0gdmVuZG9yQXJyW2ldLnBob25lIHx8ICcnO1xyXG4gICAgICAgIH1cclxuICAgICAgICBwYXlsb2FkID0ge1xyXG4gICAgICAgICAgLi4ucGF5bG9hZCxcclxuICAgICAgICAgIHZlbmRvcl9kZXRhaWxzOiB2ZW5kb3JPYmosXHJcbiAgICAgICAgfTtcclxuICAgICAgfVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYCR7QVBJX0JBU0VfVVJMfS9jb21wbGV0ZS11cGxvYWRgLCBwYXlsb2FkLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogdG9rZW4gPyBgQmVhcmVyICR7dG9rZW59YCA6ICcnXHJcbiAgICAgICAgfSxcclxuICAgICAgICB0aW1lb3V0OiA2MDAwMFxyXG4gICAgICB9KTtcclxuICAgICAgaWYgKCFyZXNwb25zZS5kYXRhLm1lc3NhZ2UpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcmVzcG9uc2UgZnJvbSBjb21wbGV0ZS11cGxvYWQgQVBJJyk7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNvbXBsZXRpbmcgdXBsb2FkOicsIGVycm9yKTtcclxuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAgIHRocm93IHsgZXJyb3I6ICdBdXRoZW50aWNhdGlvbiBmYWlsZWQuIFBsZWFzZSBsb2cgaW4gYWdhaW4uJyB9O1xyXG4gICAgICB9XHJcbiAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHsgZXJyb3I6ICdGYWlsZWQgdG8gY29tcGxldGUgdXBsb2FkJyB9O1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8qKlxyXG4gICAqIFVwbG9hZCBhIGZpbGUgdG8gYSBwcmVzaWduZWQgVVJMIHdpdGggb3B0aW1pemVkIHBlcmZvcm1hbmNlXHJcbiAgICovXHJcbiAgdXBsb2FkVG9QcmVzaWduZWRVcmw6IGFzeW5jIChcclxuICAgIHVybDogc3RyaW5nLFxyXG4gICAgZmlsZTogRmlsZSxcclxuICAgIG9uUHJvZ3Jlc3M/OiAocHJvZ3Jlc3M6IG51bWJlcikgPT4gdm9pZFxyXG4gICk6IFByb21pc2U8dm9pZD4gPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIHNpbXBsZSBkaXJlY3QgdXBsb2FkIGZvciBmaWxlOicsIGZpbGUubmFtZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdGaWxlIHR5cGU6JywgZmlsZS50eXBlKTtcclxuICAgICAgY29uc29sZS5sb2coJ0ZpbGUgc2l6ZTonLCAoZmlsZS5zaXplIC8gKDEwMjQgKiAxMDI0KSkudG9GaXhlZCgyKSArICcgTUInKTtcclxuICAgICAgY29uc29sZS5sb2coJ1VwbG9hZCBVUkw6JywgdXJsKTtcclxuXHJcbiAgICAgIC8vIFJlcG9ydCBpbml0aWFsIHByb2dyZXNzXHJcbiAgICAgIGlmIChvblByb2dyZXNzKSBvblByb2dyZXNzKDEwKTtcclxuXHJcbiAgICAgIC8vIFN0YXJ0IHRpbWluZyB0aGUgdXBsb2FkXHJcbiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XHJcbiAgICAgIGxldCBsYXN0UHJvZ3Jlc3NVcGRhdGUgPSAwO1xyXG5cclxuICAgICAgLy8gVXNlIHNpbXBsZSBkaXJlY3QgdXBsb2FkIGZvciBhbGwgZmlsZXNcclxuICAgICAgY29uc29sZS5sb2coJ1VzaW5nIHNpbXBsZSBkaXJlY3QgdXBsb2FkJyk7XHJcblxyXG4gICAgICAvLyBDcmVhdGUgc2ltcGxlIGhlYWRlcnMgZm9yIGRpcmVjdCB1cGxvYWRcclxuICAgICAgY29uc3QgaGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogZmlsZS50eXBlLFxyXG4gICAgICAgICdDb250ZW50LUxlbmd0aCc6IGZpbGUuc2l6ZS50b1N0cmluZygpLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgLy8gUGVyZm9ybSBzaW1wbGUgZGlyZWN0IHVwbG9hZCB0byB0aGUgcHJlc2lnbmVkIFVSTFxyXG4gICAgICBhd2FpdCBheGlvcy5wdXQodXJsLCBmaWxlLCB7XHJcbiAgICAgICAgaGVhZGVycyxcclxuICAgICAgICB0aW1lb3V0OiAzNjAwMDAwLCAvLyAxIGhvdXIgdGltZW91dFxyXG4gICAgICAgIG1heEJvZHlMZW5ndGg6IEluZmluaXR5LFxyXG4gICAgICAgIG1heENvbnRlbnRMZW5ndGg6IEluZmluaXR5LFxyXG4gICAgICAgIG9uVXBsb2FkUHJvZ3Jlc3M6IChwcm9ncmVzc0V2ZW50KSA9PiB7XHJcbiAgICAgICAgICBpZiAob25Qcm9ncmVzcyAmJiBwcm9ncmVzc0V2ZW50LnRvdGFsKSB7XHJcbiAgICAgICAgICAgIC8vIENhbGN1bGF0ZSByYXcgcGVyY2VudGFnZVxyXG4gICAgICAgICAgICBjb25zdCByYXdQZXJjZW50ID0gTWF0aC5yb3VuZCgocHJvZ3Jlc3NFdmVudC5sb2FkZWQgKiAxMDApIC8gcHJvZ3Jlc3NFdmVudC50b3RhbCk7XHJcblxyXG4gICAgICAgICAgICAvLyBVcGRhdGUgVUkgaW4gNSUgaW5jcmVtZW50cyBmb3Igc21vb3RoZXIgcHJvZ3Jlc3NcclxuICAgICAgICAgICAgaWYgKHJhd1BlcmNlbnQgPj0gbGFzdFByb2dyZXNzVXBkYXRlICsgNSB8fCByYXdQZXJjZW50ID09PSAxMDApIHtcclxuICAgICAgICAgICAgICBsYXN0UHJvZ3Jlc3NVcGRhdGUgPSByYXdQZXJjZW50O1xyXG5cclxuICAgICAgICAgICAgICAvLyBNYXAgdG8gMTAtOTUlIHJhbmdlIGZvciBVSVxyXG4gICAgICAgICAgICAgIGNvbnN0IHBlcmNlbnRDb21wbGV0ZWQgPSAxMCArIE1hdGguZmxvb3IoKHJhd1BlcmNlbnQgKiA4NSkgLyAxMDApO1xyXG4gICAgICAgICAgICAgIG9uUHJvZ3Jlc3MocGVyY2VudENvbXBsZXRlZCk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBhbmQgbG9nIHVwbG9hZCBzcGVlZFxyXG4gICAgICAgICAgICAgIGNvbnN0IGVsYXBzZWRTZWNvbmRzID0gKERhdGUubm93KCkgLSBzdGFydFRpbWUpIC8gMTAwMDtcclxuICAgICAgICAgICAgICBpZiAoZWxhcHNlZFNlY29uZHMgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBzcGVlZE1CcHMgPSAoKHByb2dyZXNzRXZlbnQubG9hZGVkIC8gZWxhcHNlZFNlY29uZHMpIC8gKDEwMjQgKiAxMDI0KSkudG9GaXhlZCgyKTtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBVcGxvYWQgcHJvZ3Jlc3M6ICR7cGVyY2VudENvbXBsZXRlZH0lIGF0ICR7c3BlZWRNQnBzfU1CL3NgKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gQ2FsY3VsYXRlIGZpbmFsIHN0YXRzXHJcbiAgICAgIGNvbnN0IGVuZFRpbWUgPSBEYXRlLm5vdygpO1xyXG4gICAgICBjb25zdCBlbGFwc2VkU2Vjb25kcyA9IChlbmRUaW1lIC0gc3RhcnRUaW1lKSAvIDEwMDA7XHJcbiAgICAgIGNvbnN0IHVwbG9hZFNwZWVkID0gKGZpbGUuc2l6ZSAvIGVsYXBzZWRTZWNvbmRzIC8gKDEwMjQgKiAxMDI0KSkudG9GaXhlZCgyKTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKGBVcGxvYWQgY29tcGxldGVkIGluICR7ZWxhcHNlZFNlY29uZHMudG9GaXhlZCgyKX1zIGF0ICR7dXBsb2FkU3BlZWR9TUIvc2ApO1xyXG4gICAgICBjb25zb2xlLmxvZygnUmVzcG9uc2Ugc3RhdHVzOiAyMDAgKHN1Y2Nlc3MpJyk7XHJcblxyXG4gICAgICAvLyBSZXBvcnQgY29tcGxldGlvblxyXG4gICAgICBpZiAob25Qcm9ncmVzcykgb25Qcm9ncmVzcygxMDApO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ0ZpbGUgdXBsb2FkZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwbG9hZGluZyBmaWxlOicsIGVycm9yKTtcclxuXHJcbiAgICAgIC8vIFByb3ZpZGUgbW9yZSBkZXRhaWxlZCBlcnJvciBpbmZvcm1hdGlvblxyXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0ZhaWxlZCB0byB1cGxvYWQgZmlsZSc7XHJcblxyXG4gICAgICBpZiAoZXJyb3IubWVzc2FnZSkge1xyXG4gICAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdOZXR3b3JrIEVycm9yJykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnQ09SUycpKSB7XHJcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnTmV0d29yayBlcnJvciBvciBDT1JTIGlzc3VlLiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNvbnRhY3Qgc3VwcG9ydC4nO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBgVXBsb2FkIGVycm9yOiAke2Vycm9yLm1lc3NhZ2V9YDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3BvbnNlIHN0YXR1czonLCBlcnJvci5yZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3BvbnNlIGhlYWRlcnM6JywgZXJyb3IucmVzcG9uc2UuaGVhZGVycyk7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignUmVzcG9uc2UgZGF0YTonLCBlcnJvci5yZXNwb25zZS5kYXRhKTtcclxuXHJcbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlLnN0YXR1cyA9PT0gNDAzKSB7XHJcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnUGVybWlzc2lvbiBkZW5pZWQuIFRoZSB1cGxvYWQgVVJMIG1heSBoYXZlIGV4cGlyZWQuJztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHRocm93IHsgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8qKlxyXG4gICAqIEhhbmRsZSB0aGUgY29tcGxldGUgdXBsb2FkIHByb2Nlc3NcclxuICAgKi9cclxuICBoYW5kbGVVcGxvYWQ6IGFzeW5jIChcclxuICAgIGZpbGU6IEZpbGUsXHJcbiAgICBtZWRpYVR5cGU6ICdwaG90bycgfCAndmlkZW8nLFxyXG4gICAgY2F0ZWdvcnk6IHN0cmluZyxcclxuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nLFxyXG4gICAgdGFncz86IHN0cmluZ1tdLFxyXG4gICAgZGV0YWlscz86IFJlY29yZDxzdHJpbmcsIHN0cmluZz4sXHJcbiAgICBkdXJhdGlvbj86IG51bWJlcixcclxuICAgIHRodW1ibmFpbD86IEZpbGUgfCBudWxsLFxyXG4gICAgb25Qcm9ncmVzcz86IChwcm9ncmVzczogbnVtYmVyKSA9PiB2b2lkXHJcbiAgKTogUHJvbWlzZTxDb21wbGV0ZVVwbG9hZFJlc3BvbnNlPiA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBoYW5kbGVVcGxvYWQgY2FsbGVkIHdpdGggcGFyYW1zOicsIHtcclxuICAgICAgICBmaWxlTmFtZTogZmlsZT8ubmFtZSxcclxuICAgICAgICBmaWxlU2l6ZTogTWF0aC5yb3VuZChmaWxlLnNpemUgLyAoMTAyNCAqIDEwMjQpICogMTAwKSAvIDEwMCArICcgTUInLFxyXG4gICAgICAgIG1lZGlhVHlwZSxcclxuICAgICAgICBjYXRlZ29yeSxcclxuICAgICAgICBkZXNjcmlwdGlvbixcclxuICAgICAgICB0YWdzQ291bnQ6IHRhZ3M/Lmxlbmd0aCxcclxuICAgICAgICBkZXRhaWxzQ291bnQ6IGRldGFpbHMgPyBPYmplY3Qua2V5cyhkZXRhaWxzKS5sZW5ndGggOiAwLFxyXG4gICAgICAgIHZpZGVvQ2F0ZWdvcnk6IGRldGFpbHM/LnZpZGVvX2NhdGVnb3J5IHx8ICdOb3Qgc2V0JyxcclxuICAgICAgICBkdXJhdGlvbixcclxuICAgICAgICBoYXNUaHVtYm5haWw6ICEhdGh1bWJuYWlsXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gTG9nIHRoZSB2aWRlb19jYXRlZ29yeSBmcm9tIGRldGFpbHNcclxuICAgICAgaWYgKG1lZGlhVHlwZSA9PT0gJ3ZpZGVvJykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIFZpZGVvIGNhdGVnb3J5IGZyb20gZGV0YWlsczonLCBkZXRhaWxzPy52aWRlb19jYXRlZ29yeSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gQWxsIGRldGFpbCBmaWVsZHM6JywgZGV0YWlscyA/IEpTT04uc3RyaW5naWZ5KGRldGFpbHMpIDogJ05vIGRldGFpbHMnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKCFmaWxlKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBmaWxlIHByb3ZpZGVkJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIExvZyB0aGUgZmlsZSBzaXplIGFuZCBzZWxlY3RlZCBjYXRlZ29yeSB3aXRob3V0IG92ZXJyaWRpbmcgdGhlIHVzZXIncyBzZWxlY3Rpb25cclxuICAgICAgaWYgKG1lZGlhVHlwZSA9PT0gJ3ZpZGVvJykge1xyXG4gICAgICAgIGNvbnN0IGZpbGVTaXplTUIgPSBNYXRoLnJvdW5kKGZpbGUuc2l6ZSAvICgxMDI0ICogMTAyNCkgKiAxMDApIC8gMTAwO1xyXG4gICAgICAgIGNvbnN0IGNhdGVnb3J5TGltaXRzID0ge1xyXG4gICAgICAgICAgJ3N0b3J5JzogNTAsXHJcbiAgICAgICAgICAnZmxhc2gnOiAxMDAsXHJcbiAgICAgICAgICAnZ2xpbXBzZSc6IDI1MCxcclxuICAgICAgICAgICdtb3ZpZSc6IDIwMDBcclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBjb25zdCBzaXplTGltaXQgPSBjYXRlZ29yeUxpbWl0c1tjYXRlZ29yeSBhcyBrZXlvZiB0eXBlb2YgY2F0ZWdvcnlMaW1pdHNdIHx8IDI1MDtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gRmlsZSBzaXplOiAke2ZpbGVTaXplTUJ9IE1CLCBTZWxlY3RlZCBjYXRlZ29yeTogJHtjYXRlZ29yeX1gKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBTaXplIGxpbWl0IGZvciAke2NhdGVnb3J5fTogJHtzaXplTGltaXR9IE1CYCk7XHJcblxyXG4gICAgICAgIC8vIFdhcm5pbmcgaXMgbm93IGhhbmRsZWQgaW4gdGhlIHByZXNpZ25lZFVybFJlcXVlc3Qgc2VjdGlvblxyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBTdGFydGluZyB1cGxvYWQgcHJvY2VzcyBmb3I6JywgZmlsZS5uYW1lKTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSBwcm9ncmVzcyB0byAxMCVcclxuICAgICAgaWYgKG9uUHJvZ3Jlc3MpIG9uUHJvZ3Jlc3MoMTApO1xyXG5cclxuICAgICAgLy8gU3RlcCAxOiBHZXQgcHJlc2lnbmVkIFVSTHNcclxuICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gQ3JlYXRpbmcgcHJlc2lnbmVkIFVSTCByZXF1ZXN0IHdpdGggY2F0ZWdvcnk6JywgY2F0ZWdvcnkpO1xyXG5cclxuICAgICAgLy8gRW5zdXJlIHdlJ3JlIHVzaW5nIHRoZSBjb3JyZWN0IGJhY2tlbmQgY2F0ZWdvcnkgbmFtZXNcclxuICAgICAgLy8gRnJvbnRlbmQ6IGZsYXNoZXMsIGdsaW1wc2VzLCBtb3ZpZXMsIG1vbWVudHNcclxuICAgICAgLy8gQmFja2VuZDogZmxhc2gsIGdsaW1wc2UsIG1vdmllLCBzdG9yeVxyXG4gICAgICBsZXQgYmFja2VuZENhdGVnb3J5ID0gbWVkaWFUeXBlID09PSAncGhvdG8nXHJcbiAgICAgICAgPyAoY2F0ZWdvcnkgPT09ICdzdG9yeScgPyAnc3RvcnknIDogJ3Bvc3QnKSAgLy8gRm9yIHBob3RvczogZWl0aGVyICdzdG9yeScgb3IgJ3Bvc3QnXHJcbiAgICAgICAgOiBjYXRlZ29yeTsgIC8vIEZvciB2aWRlb3M6IGtlZXAgdGhlIG9yaWdpbmFsIGNhdGVnb3J5XHJcblxyXG4gICAgICAvLyBEb3VibGUtY2hlY2sgdGhhdCB3ZSBoYXZlIGEgdmFsaWQgY2F0ZWdvcnlcclxuICAgICAgY29uc3QgdmFsaWRDYXRlZ29yaWVzID0gWydzdG9yeScsICdmbGFzaCcsICdnbGltcHNlJywgJ21vdmllJywgJ3Bvc3QnXTtcclxuICAgICAgaWYgKCF2YWxpZENhdGVnb3JpZXMuaW5jbHVkZXMoYmFja2VuZENhdGVnb3J5KSkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIFdBUk5JTkc6IEludmFsaWQgY2F0ZWdvcnkgJyR7YmFja2VuZENhdGVnb3J5fScuIFVzaW5nICdmbGFzaCcgYXMgZmFsbGJhY2suYCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gVmFsaWQgY2F0ZWdvcmllcyBhcmU6ICR7dmFsaWRDYXRlZ29yaWVzLmpvaW4oJywgJyl9YCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gQ2F0ZWdvcnkgdHlwZTogJHt0eXBlb2YgYmFja2VuZENhdGVnb3J5fWApO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIENhdGVnb3J5IHZhbHVlOiAnJHtiYWNrZW5kQ2F0ZWdvcnl9J2ApO1xyXG5cclxuICAgICAgICAvLyBVc2UgJ2ZsYXNoJyBhcyB0aGUgZGVmYXVsdCBmb3IgdmlkZW9zIGluc3RlYWQgb2YgJ2dsaW1wc2UnXHJcbiAgICAgICAgYmFja2VuZENhdGVnb3J5ID0gJ2ZsYXNoJztcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gT3JpZ2luYWwgY2F0ZWdvcnkgZnJvbSBjb250ZXh0OiAke2NhdGVnb3J5fWApO1xyXG4gICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBVc2luZyBiYWNrZW5kIGNhdGVnb3J5OiAke2JhY2tlbmRDYXRlZ29yeX1gKTtcclxuXHJcbiAgICAgIC8vIExvZyB0aGUgZmlsZSBzaXplIHRvIGhlbHAgd2l0aCBkZWJ1Z2dpbmdcclxuICAgICAgY29uc3QgZmlsZVNpemVNQiA9IE1hdGgucm91bmQoZmlsZS5zaXplIC8gKDEwMjQgKiAxMDI0KSAqIDEwMCkgLyAxMDA7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIEZpbGUgc2l6ZTogJHtmaWxlU2l6ZU1CfSBNQmApO1xyXG5cclxuICAgICAgLy8gTG9nIHRoZSBjYXRlZ29yeSBsaW1pdHNcclxuICAgICAgY29uc3QgY2F0ZWdvcnlMaW1pdHMgPSB7XHJcbiAgICAgICAgJ3N0b3J5JzogNTAsXHJcbiAgICAgICAgJ2ZsYXNoJzogMTAwLFxyXG4gICAgICAgICdnbGltcHNlJzogMjUwLFxyXG4gICAgICAgICdtb3ZpZSc6IDIwMDBcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHNpemVMaW1pdCA9IGNhdGVnb3J5TGltaXRzW2JhY2tlbmRDYXRlZ29yeSBhcyBrZXlvZiB0eXBlb2YgY2F0ZWdvcnlMaW1pdHNdIHx8IDI1MDtcclxuICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gU2l6ZSBsaW1pdCBmb3IgJHtiYWNrZW5kQ2F0ZWdvcnl9OiAke3NpemVMaW1pdH0gTUJgKTtcclxuXHJcbiAgICAgIC8vIExvZyBhIHdhcm5pbmcgaWYgdGhlIGZpbGUgc2l6ZSBleGNlZWRzIHRoZSBsaW1pdFxyXG4gICAgICBpZiAoZmlsZVNpemVNQiA+IHNpemVMaW1pdCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIFdBUk5JTkc6IEZpbGUgc2l6ZSAoJHtmaWxlU2l6ZU1CfSBNQikgZXhjZWVkcyB0aGUgbGltaXQgZm9yICR7YmFja2VuZENhdGVnb3J5fSAoJHtzaXplTGltaXR9IE1CKS5gKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBUaGUgdXBsb2FkIG1heSBmYWlsLiBDb25zaWRlciBzZWxlY3RpbmcgYSBjYXRlZ29yeSB3aXRoIGEgaGlnaGVyIHNpemUgbGltaXQuYCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFdoZW4gY29uc3RydWN0aW5nIHByZXNpZ25lZFVybFJlcXVlc3QgZm9yIHBob3RvLCBzZXQgbWVkaWFfc3VidHlwZSB0byBudWxsXHJcbiAgICAgIGNvbnN0IHByZXNpZ25lZFVybFJlcXVlc3Q6IFByZXNpZ25lZFVybFJlcXVlc3QgPSB7XHJcbiAgICAgICAgbWVkaWFfdHlwZTogbWVkaWFUeXBlLFxyXG4gICAgICAgIG1lZGlhX3N1YnR5cGU6IG1lZGlhVHlwZSA9PT0gJ3Bob3RvJyA/IG51bGwgOiBiYWNrZW5kQ2F0ZWdvcnksXHJcbiAgICAgICAgZmlsZW5hbWU6IGZpbGUubmFtZSxcclxuICAgICAgICBjb250ZW50X3R5cGU6IGZpbGUudHlwZSxcclxuICAgICAgICBmaWxlX3NpemU6IGZpbGUuc2l6ZSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIC8vIExvZyB0aGUgcHJlc2lnbmVkIFVSTCByZXF1ZXN0XHJcbiAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIFNlbmRpbmcgcHJlc2lnbmVkIFVSTCByZXF1ZXN0IHdpdGggbWVkaWFfc3VidHlwZTogJHtiYWNrZW5kQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAvLyBBZGQgdmlkZW9fY2F0ZWdvcnkgZm9yIHZpZGVvc1xyXG4gICAgICBpZiAobWVkaWFUeXBlID09PSAndmlkZW8nKSB7XHJcbiAgICAgICAgLy8gR2V0IHRoZSB2aWRlb19jYXRlZ29yeSBmcm9tIGRldGFpbHMgLSBubyBkZWZhdWx0IHZhbHVlXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gRGV0YWlscyBvYmplY3Q6JywgZGV0YWlscyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gRGV0YWlscyBrZXlzOicsIGRldGFpbHMgPyBPYmplY3Qua2V5cyhkZXRhaWxzKSA6ICdObyBkZXRhaWxzJyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHZpZGVvQ2F0ZWdvcnkgPSBkZXRhaWxzPy52aWRlb19jYXRlZ29yeTtcclxuICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBWaWRlbyBjYXRlZ29yeSBmcm9tIGRldGFpbHM6JywgdmlkZW9DYXRlZ29yeSk7XHJcblxyXG4gICAgICAgIC8vIE1ha2Ugc3VyZSB3ZSdyZSB1c2luZyBhIHZhbGlkIHZpZGVvX2NhdGVnb3J5XHJcbiAgICAgICAgY29uc3QgYWxsb3dlZENhdGVnb3JpZXMgPSBbJ215X3dlZGRpbmcnLCAnd2VkZGluZ192bG9nJ107XHJcblxyXG4gICAgICAgIC8vIElmIG5vIHZpZGVvX2NhdGVnb3J5IGlzIHByb3ZpZGVkLCB1c2UgYSBkZWZhdWx0IG9uZVxyXG4gICAgICAgIGlmICghdmlkZW9DYXRlZ29yeSkge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihgQVBJIFNFUlZJQ0UgLSBNaXNzaW5nIHZpZGVvX2NhdGVnb3J5LiBVc2luZyBkZWZhdWx0ICdteV93ZWRkaW5nJy5gKTtcclxuICAgICAgICAgIC8vIFVzZSAnbXlfd2VkZGluZycgYXMgdGhlIGRlZmF1bHQgdmlkZW9fY2F0ZWdvcnlcclxuICAgICAgICAgIHByZXNpZ25lZFVybFJlcXVlc3QudmlkZW9fY2F0ZWdvcnkgPSAnbXlfd2VkZGluZyc7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBVc2luZyBkZWZhdWx0IHZpZGVvX2NhdGVnb3J5OiBteV93ZWRkaW5nJyk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIE1hcCB0aGUgVUkgY2F0ZWdvcnkgdG8gdGhlIGJhY2tlbmQgY2F0ZWdvcnkgaWYgbmVlZGVkXHJcbiAgICAgICAgICBsZXQgYmFja2VuZFZpZGVvQ2F0ZWdvcnkgPSB2aWRlb0NhdGVnb3J5O1xyXG5cclxuICAgICAgICAgIC8vIElmIHRoZSBjYXRlZ29yeSBpcyBpbiBVSSBmb3JtYXQsIG1hcCBpdCB0byBiYWNrZW5kIGZvcm1hdFxyXG4gICAgICAgICAgaWYgKHZpZGVvQ2F0ZWdvcnkgPT09ICdteV93ZWRkaW5nX3ZpZGVvcycpIHtcclxuICAgICAgICAgICAgYmFja2VuZFZpZGVvQ2F0ZWdvcnkgPSAnbXlfd2VkZGluZyc7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIE1hcHBlZCBVSSBjYXRlZ29yeSB0byBiYWNrZW5kIGNhdGVnb3J5OicsIGJhY2tlbmRWaWRlb0NhdGVnb3J5KTtcclxuICAgICAgICAgIH0gZWxzZSBpZiAodmlkZW9DYXRlZ29yeSA9PT0gJ3dlZGRpbmdfdmxvZ192aWRlb3MnKSB7XHJcbiAgICAgICAgICAgIGJhY2tlbmRWaWRlb0NhdGVnb3J5ID0gJ3dlZGRpbmdfdmxvZyc7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIE1hcHBlZCBVSSBjYXRlZ29yeSB0byBiYWNrZW5kIGNhdGVnb3J5OicsIGJhY2tlbmRWaWRlb0NhdGVnb3J5KTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBJZiB0aGUgdmlkZW9fY2F0ZWdvcnkgaXMgbm90IGFsbG93ZWQsIHRocm93IGFuIGVycm9yXHJcbiAgICAgICAgICBpZiAoIWFsbG93ZWRDYXRlZ29yaWVzLmluY2x1ZGVzKGJhY2tlbmRWaWRlb0NhdGVnb3J5KSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBBUEkgU0VSVklDRSAtIEludmFsaWQgdmlkZW9fY2F0ZWdvcnk6ICR7YmFja2VuZFZpZGVvQ2F0ZWdvcnl9LiBNdXN0IGJlIG9uZSBvZiAke0pTT04uc3RyaW5naWZ5KGFsbG93ZWRDYXRlZ29yaWVzKX1gKTtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHZpZGVvIGNhdGVnb3J5LiBNdXN0IGJlIG9uZSBvZiAke0pTT04uc3RyaW5naWZ5KGFsbG93ZWRDYXRlZ29yaWVzKX1gKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBVc2UgdGhlIHByb3ZpZGVkIHZpZGVvX2NhdGVnb3J5XHJcbiAgICAgICAgICBwcmVzaWduZWRVcmxSZXF1ZXN0LnZpZGVvX2NhdGVnb3J5ID0gYmFja2VuZFZpZGVvQ2F0ZWdvcnk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBVc2luZyB2aWRlb19jYXRlZ29yeTonLCBiYWNrZW5kVmlkZW9DYXRlZ29yeSk7XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gRmluYWwgdmlkZW8gY2F0ZWdvcnk6ICR7YmFja2VuZFZpZGVvQ2F0ZWdvcnl9YCk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBDb21wbGV0ZSBwcmVzaWduZWQgVVJMIHJlcXVlc3Q6YCwgSlNPTi5zdHJpbmdpZnkocHJlc2lnbmVkVXJsUmVxdWVzdCwgbnVsbCwgMikpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gTG9nIHRoZSBjYXRlZ29yeSBhbmQgZmlsZSBzaXplIGxpbWl0c1xyXG4gICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIE9yaWdpbmFsIGNhdGVnb3J5IGZyb20gVUk6ICR7Y2F0ZWdvcnl9YCk7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gVXNpbmcgYmFja2VuZCBtZWRpYSBzdWJ0eXBlOiAke2JhY2tlbmRDYXRlZ29yeX1gKTtcclxuXHJcbiAgICAgICAgLy8gTG9nIHNpemUgbGltaXRzIGJhc2VkIG9uIGNhdGVnb3J5IHdpdGhvdXQgb3ZlcnJpZGluZyB0aGUgdXNlcidzIHNlbGVjdGlvblxyXG4gICAgICAgIGNvbnN0IGNhdGVnb3J5TGltaXRzID0ge1xyXG4gICAgICAgICAgJ3N0b3J5JzogNTAsXHJcbiAgICAgICAgICAnZmxhc2gnOiAxMDAsXHJcbiAgICAgICAgICAnZ2xpbXBzZSc6IDI1MCxcclxuICAgICAgICAgICdtb3ZpZSc6IDIwMDBcclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBjb25zdCBzaXplTGltaXQgPSBjYXRlZ29yeUxpbWl0c1tiYWNrZW5kQ2F0ZWdvcnkgYXMga2V5b2YgdHlwZW9mIGNhdGVnb3J5TGltaXRzXSB8fCAyNTA7XHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gU2l6ZSBsaW1pdCBmb3IgJHtiYWNrZW5kQ2F0ZWdvcnl9OiAke3NpemVMaW1pdH0gTUIgKGZpbGUgc2l6ZTogJHtmaWxlU2l6ZU1CfSBNQilgKTtcclxuXHJcbiAgICAgICAgLy8gTG9nIGEgd2FybmluZyBpZiB0aGUgZmlsZSBzaXplIGV4Y2VlZHMgdGhlIGxpbWl0IGZvciB0aGUgc2VsZWN0ZWQgY2F0ZWdvcnlcclxuICAgICAgICBpZiAoZmlsZVNpemVNQiA+IHNpemVMaW1pdCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gV0FSTklORzogRmlsZSBzaXplICgke2ZpbGVTaXplTUJ9IE1CKSBleGNlZWRzIHRoZSBsaW1pdCBmb3IgJHtiYWNrZW5kQ2F0ZWdvcnl9ICgke3NpemVMaW1pdH0gTUIpLmApO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gVGhlIHVwbG9hZCBtYXkgZmFpbC4gQ29uc2lkZXIgc2VsZWN0aW5nIGEgY2F0ZWdvcnkgd2l0aCBhIGhpZ2hlciBzaXplIGxpbWl0LmApO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQWRkIGR1cmF0aW9uIGlmIGF2YWlsYWJsZVxyXG4gICAgICAgIGlmIChkdXJhdGlvbikge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYFZpZGVvIGR1cmF0aW9uOiAke2R1cmF0aW9ufSBzZWNvbmRzYCk7XHJcbiAgICAgICAgICAvLyBZb3UgY291bGQgYWRkIHRoaXMgdG8gdGhlIHJlcXVlc3QgaWYgdGhlIEFQSSBzdXBwb3J0cyBpdFxyXG4gICAgICAgICAgLy8gcHJlc2lnbmVkVXJsUmVxdWVzdC5kdXJhdGlvbiA9IGR1cmF0aW9uO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcHJlc2lnbmVkVXJsUmVzcG9uc2UgPSBhd2FpdCB1cGxvYWRTZXJ2aWNlLmdldFByZXNpZ25lZFVybChwcmVzaWduZWRVcmxSZXF1ZXN0KTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSBwcm9ncmVzcyB0byAyMCVcclxuICAgICAgaWYgKG9uUHJvZ3Jlc3MpIG9uUHJvZ3Jlc3MoMjApO1xyXG5cclxuICAgICAgLy8gU3RlcCAyOiBVcGxvYWQgdGhlIGZpbGUgdXNpbmcgbXVsdGlwYXJ0IHVwbG9hZCAoMjAtNzAlIG9mIHByb2dyZXNzKVxyXG4gICAgICBjb25zdCBwYXJ0cyA9IGF3YWl0IG11bHRpcGFydFVwbG9hZChcclxuICAgICAgICBmaWxlLFxyXG4gICAgICAgIHByZXNpZ25lZFVybFJlc3BvbnNlLnBhcnRfdXJscyxcclxuICAgICAgICAocHJvZ3Jlc3M6IE11bHRpcGFydFVwbG9hZFByb2dyZXNzKSA9PiB7XHJcbiAgICAgICAgICAvLyBNYXAgdGhlIHVwbG9hZCBwcm9ncmVzcyBmcm9tIDAtMTAwIHRvIDIwLTcwIGluIG91ciBvdmVyYWxsIHByb2dyZXNzXHJcbiAgICAgICAgICBpZiAob25Qcm9ncmVzcykgb25Qcm9ncmVzcygyMCArIChwcm9ncmVzcy5wZXJjZW50YWdlICogMC41KSk7XHJcbiAgICAgICAgfVxyXG4gICAgICApO1xyXG5cclxuICAgICAgLy8gQ29tcGxldGUgbXVsdGlwYXJ0IHVwbG9hZFxyXG4gICAgICBhd2FpdCB1cGxvYWRTZXJ2aWNlLmNvbXBsZXRlTXVsdGlwYXJ0VXBsb2FkKHtcclxuICAgICAgICBtZWRpYV9pZDogcHJlc2lnbmVkVXJsUmVzcG9uc2UubWVkaWFfaWQsXHJcbiAgICAgICAgdXBsb2FkX2lkOiBwcmVzaWduZWRVcmxSZXNwb25zZS51cGxvYWRfaWQsXHJcbiAgICAgICAgcGFydHM6IHBhcnRzXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gU3RlcCAzOiBVcGxvYWQgdGh1bWJuYWlsIGlmIGF2YWlsYWJsZSAoNzAtOTAlIG9mIHByb2dyZXNzKVxyXG4gICAgICBpZiAodGh1bWJuYWlsICYmIHByZXNpZ25lZFVybFJlc3BvbnNlLnRodW1ibmFpbF91cmwpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVXBsb2FkaW5nIHRodW1ibmFpbDonLCB0aHVtYm5haWwubmFtZSk7XHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBwcm9ncmVzcyB0byA3MCVcclxuICAgICAgICBpZiAob25Qcm9ncmVzcykgb25Qcm9ncmVzcyg3MCk7XHJcblxyXG4gICAgICAgIGF3YWl0IHVwbG9hZFNlcnZpY2UudXBsb2FkVG9QcmVzaWduZWRVcmwoXHJcbiAgICAgICAgICBwcmVzaWduZWRVcmxSZXNwb25zZS50aHVtYm5haWxfdXJsLFxyXG4gICAgICAgICAgdGh1bWJuYWlsLFxyXG4gICAgICAgICAgKHVwbG9hZFByb2dyZXNzKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIE1hcCB0aGUgdXBsb2FkIHByb2dyZXNzIGZyb20gMC0xMDAgdG8gNzAtOTAgaW4gb3VyIG92ZXJhbGwgcHJvZ3Jlc3NcclxuICAgICAgICAgICAgaWYgKG9uUHJvZ3Jlc3MpIG9uUHJvZ3Jlc3MoNzAgKyAodXBsb2FkUHJvZ3Jlc3MgKiAwLjIpKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBVcGRhdGUgcHJvZ3Jlc3MgdG8gOTAlXHJcbiAgICAgIGlmIChvblByb2dyZXNzKSBvblByb2dyZXNzKDkwKTtcclxuXHJcbiAgICAgIC8vIFN0ZXAgNDogQ29tcGxldGUgdGhlIHVwbG9hZCAoOTAtMTAwJSBvZiBwcm9ncmVzcylcclxuICAgICAgdHJ5IHtcclxuICAgICAgICAvLyBXaGVuIGNvbnN0cnVjdGluZyBjb21wbGV0ZVJlcXVlc3QgZm9yIHBob3RvLCBkbyBub3QgaW5jbHVkZSB0aXRsZVxyXG4gICAgICAgIGNvbnN0IGNvbXBsZXRlUmVxdWVzdDogQ29tcGxldGVVcGxvYWRSZXF1ZXN0ID0ge1xyXG4gICAgICAgICAgbWVkaWFfaWQ6IHByZXNpZ25lZFVybFJlc3BvbnNlLm1lZGlhX2lkLFxyXG4gICAgICAgICAgbWVkaWFfdHlwZTogbWVkaWFUeXBlLFxyXG4gICAgICAgICAgbWVkaWFfc3VidHlwZTogbWVkaWFUeXBlID09PSAncGhvdG8nID8gbnVsbCA6IGJhY2tlbmRDYXRlZ29yeSxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBkZXNjcmlwdGlvbiB8fCAnJyxcclxuICAgICAgICAgIHRhZ3M6IHRhZ3MgfHwgW10sXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gU2V0dGluZyBtZWRpYV9zdWJ0eXBlIGluIGNvbXBsZXRlUmVxdWVzdDogJHtiYWNrZW5kQ2F0ZWdvcnl9YCk7XHJcblxyXG4gICAgICAgIC8vIExvZyB0aGUgZGVzY3JpcHRpb24gYmVpbmcgc2VudFxyXG4gICAgICAgIGNvbnNvbGUubG9nKCdTZW5kaW5nIGRlc2NyaXB0aW9uOicsIGRlc2NyaXB0aW9uIHx8ICcnKTtcclxuXHJcbiAgICAgICAgLy8gQWRkIGR1cmF0aW9uIGZvciB2aWRlb3MgaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgaWYgKG1lZGlhVHlwZSA9PT0gJ3ZpZGVvJyAmJiBkdXJhdGlvbikge1xyXG4gICAgICAgICAgY29tcGxldGVSZXF1ZXN0LmR1cmF0aW9uID0gZHVyYXRpb247XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBFeHRyYWN0IHBlcnNvbmFsIGRldGFpbHMgZnJvbSB0aGUgZGV0YWlscyBvYmplY3RcclxuICAgICAgICBjb25zdCBwZXJzb25hbERldGFpbHM6IENvbXBsZXRlVXBsb2FkUmVxdWVzdFsncGVyc29uYWxfZGV0YWlscyddID0ge1xyXG4gICAgICAgICAgY2FwdGlvbjogZGV0YWlscz8uY2FwdGlvbiB8fCAnJyxcclxuICAgICAgICAgIGxpZmVfcGFydG5lcjogZGV0YWlscz8ucGVyc29uYWxfbGlmZV9wYXJ0bmVyIHx8IGRldGFpbHM/LmxpZmVQYXJ0bmVyIHx8ICcnLFxyXG4gICAgICAgICAgd2VkZGluZ19zdHlsZTogZGV0YWlscz8ucGVyc29uYWxfd2VkZGluZ19zdHlsZSB8fCBkZXNjcmlwdGlvbiB8fCAnJyxcclxuICAgICAgICAgIHBsYWNlOiBkZXRhaWxzPy5wZXJzb25hbF9wbGFjZSB8fCBkZXRhaWxzPy5sb2NhdGlvbiB8fCAnJ1xyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIC8vIEV4dHJhY3QgdmVuZG9yIGRldGFpbHMgZnJvbSB0aGUgZGV0YWlscyBvYmplY3RcclxuICAgICAgICBjb25zdCB2ZW5kb3JEZXRhaWxzOiBDb21wbGV0ZVVwbG9hZFJlcXVlc3RbJ3ZlbmRvcl9kZXRhaWxzJ10gPSB7fTtcclxuXHJcbiAgICAgICAgLy8gUHJvY2VzcyB2ZW5kb3IgZGV0YWlscyBmcm9tIHRoZSBkZXRhaWxzIG9iamVjdFxyXG4gICAgICAgIGlmIChkZXRhaWxzKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBQcm9jZXNzaW5nIHZlbmRvciBkZXRhaWxzIGZyb20gZGV0YWlscyBvYmplY3QnKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIFJhdyBkZXRhaWxzIG9iamVjdDonLCBKU09OLnN0cmluZ2lmeShkZXRhaWxzLCBudWxsLCAyKSk7XHJcblxyXG4gICAgICAgICAgLy8gQ291bnQgdmVuZG9yIGZpZWxkcyBpbiB0aGUgZGV0YWlscyBvYmplY3RcclxuICAgICAgICAgIGxldCB2ZW5kb3JGaWVsZENvdW50ID0gMDtcclxuICAgICAgICAgIE9iamVjdC5rZXlzKGRldGFpbHMpLmZvckVhY2goa2V5ID0+IHtcclxuICAgICAgICAgICAgaWYgKGtleS5zdGFydHNXaXRoKCd2ZW5kb3JfJykpIHtcclxuICAgICAgICAgICAgICB2ZW5kb3JGaWVsZENvdW50Kys7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gRm91bmQgJHt2ZW5kb3JGaWVsZENvdW50fSB2ZW5kb3ItcmVsYXRlZCBmaWVsZHMgaW4gZGV0YWlscyBvYmplY3RgKTtcclxuXHJcbiAgICAgICAgICAvLyBLZWVwIHRyYWNrIG9mIHdoaWNoIHZlbmRvciB0eXBlcyB3ZSd2ZSBhbHJlYWR5IHByb2Nlc3NlZFxyXG4gICAgICAgICAgY29uc3QgcHJvY2Vzc2VkVmVuZG9ycyA9IG5ldyBTZXQoKTtcclxuXHJcbiAgICAgICAgICBPYmplY3QuZW50cmllcyhkZXRhaWxzKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcclxuICAgICAgICAgICAgaWYgKGtleS5zdGFydHNXaXRoKCd2ZW5kb3JfJykgJiYgdmFsdWUpIHtcclxuICAgICAgICAgICAgICBjb25zdCBwYXJ0cyA9IGtleS5zcGxpdCgnXycpO1xyXG4gICAgICAgICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPj0gMykge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmVuZG9yVHlwZSA9IHBhcnRzWzFdO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRUeXBlID0gcGFydHMuc2xpY2UoMikuam9pbignXycpO1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIE5vcm1hbGl6ZSB2ZW5kb3IgdHlwZSB0byBhdm9pZCBkdXBsaWNhdGVzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkVHlwZSA9IHZlbmRvclR5cGUgPT09ICdtYWtldXBBcnRpc3QnID8gJ21ha2V1cF9hcnRpc3QnIDpcclxuICAgICAgICAgICAgICAgICAgdmVuZG9yVHlwZSA9PT0gJ2RlY29yYXRpb25zJyA/ICdkZWNvcmF0aW9uJyA6IHZlbmRvclR5cGU7XHJcblxyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gUHJvY2Vzc2luZyB2ZW5kb3IgZmllbGQ6ICR7a2V5fSA9ICR7dmFsdWV9YCwge1xyXG4gICAgICAgICAgICAgICAgICB2ZW5kb3JUeXBlLFxyXG4gICAgICAgICAgICAgICAgICBmaWVsZFR5cGUsXHJcbiAgICAgICAgICAgICAgICAgIG5vcm1hbGl6ZWRUeXBlLFxyXG4gICAgICAgICAgICAgICAgICBhbHJlYWR5UHJvY2Vzc2VkOiBwcm9jZXNzZWRWZW5kb3JzLmhhcyhub3JtYWxpemVkVHlwZSlcclxuICAgICAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgICAgIC8vIFNraXAgaWYgd2UndmUgYWxyZWFkeSBwcm9jZXNzZWQgdGhpcyB2ZW5kb3IgdHlwZVxyXG4gICAgICAgICAgICAgICAgaWYgKHByb2Nlc3NlZFZlbmRvcnMuaGFzKG5vcm1hbGl6ZWRUeXBlKSkge1xyXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBTa2lwcGluZyAke2tleX0gYXMgJHtub3JtYWxpemVkVHlwZX0gaXMgYWxyZWFkeSBwcm9jZXNzZWRgKTtcclxuICAgICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIGlmIChmaWVsZFR5cGUgPT09ICduYW1lJykge1xyXG4gICAgICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake25vcm1hbGl6ZWRUeXBlfV9uYW1lYF0gPSB2YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gU2V0ICR7bm9ybWFsaXplZFR5cGV9X25hbWUgPSAke3ZhbHVlfWApO1xyXG5cclxuICAgICAgICAgICAgICAgICAgLy8gQWxzbyBzZXQgdGhlIGNvbnRhY3QgaWYgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbnRhY3RLZXkgPSBgdmVuZG9yXyR7dmVuZG9yVHlwZX1fY29udGFjdGA7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChkZXRhaWxzW2NvbnRhY3RLZXldKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHtub3JtYWxpemVkVHlwZX1fY29udGFjdGBdID0gZGV0YWlsc1tjb250YWN0S2V5XTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBBbHNvIHNldCAke25vcm1hbGl6ZWRUeXBlfV9jb250YWN0ID0gJHtkZXRhaWxzW2NvbnRhY3RLZXldfWApO1xyXG4gICAgICAgICAgICAgICAgICAgIHByb2Nlc3NlZFZlbmRvcnMuYWRkKG5vcm1hbGl6ZWRUeXBlKTtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBNYXJrZWQgJHtub3JtYWxpemVkVHlwZX0gYXMgcHJvY2Vzc2VkIChoYXMgYm90aCBuYW1lIGFuZCBjb250YWN0KWApO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGZpZWxkVHlwZSA9PT0gJ2NvbnRhY3QnKSB7XHJcbiAgICAgICAgICAgICAgICAgIHZlbmRvckRldGFpbHNbYCR7bm9ybWFsaXplZFR5cGV9X2NvbnRhY3RgXSA9IHZhbHVlO1xyXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBTZXQgJHtub3JtYWxpemVkVHlwZX1fY29udGFjdCA9ICR7dmFsdWV9YCk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAvLyBBbHNvIHNldCB0aGUgbmFtZSBpZiBhdmFpbGFibGVcclxuICAgICAgICAgICAgICAgICAgY29uc3QgbmFtZUtleSA9IGB2ZW5kb3JfJHt2ZW5kb3JUeXBlfV9uYW1lYDtcclxuICAgICAgICAgICAgICAgICAgaWYgKGRldGFpbHNbbmFtZUtleV0pIHtcclxuICAgICAgICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake25vcm1hbGl6ZWRUeXBlfV9uYW1lYF0gPSBkZXRhaWxzW25hbWVLZXldO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIEFsc28gc2V0ICR7bm9ybWFsaXplZFR5cGV9X25hbWUgPSAke2RldGFpbHNbbmFtZUtleV19YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgcHJvY2Vzc2VkVmVuZG9ycy5hZGQobm9ybWFsaXplZFR5cGUpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIE1hcmtlZCAke25vcm1hbGl6ZWRUeXBlfSBhcyBwcm9jZXNzZWQgKGhhcyBib3RoIG5hbWUgYW5kIGNvbnRhY3QpYCk7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIExvZyB0aGUgcHJvY2Vzc2VkIHZlbmRvciBkZXRhaWxzXHJcbiAgICAgICAgICAvLyBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBQcm9jZXNzZWQgdmVuZG9yIGRldGFpbHM6JywgSlNPTi5zdHJpbmdpZnkodmVuZG9yRGV0YWlscywgbnVsbCwgMikpO1xyXG4gICAgICAgICAgLy8gY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gUHJvY2Vzc2VkICR7cHJvY2Vzc2VkVmVuZG9ycy5zaXplfSBjb21wbGV0ZSB2ZW5kb3IgdHlwZXM6ICR7QXJyYXkuZnJvbShwcm9jZXNzZWRWZW5kb3JzKS5qb2luKCcsICcpfWApO1xyXG5cclxuICAgICAgICAgIC8vIEZpbmFsIGNoZWNrIHRvIGVuc3VyZSB3ZSBoYXZlIGJvdGggbmFtZSBhbmQgY29udGFjdCBmb3IgZWFjaCB2ZW5kb3JcclxuICAgICAgICAgIGNvbnN0IHZlbmRvck5hbWVzID0gbmV3IFNldCgpO1xyXG4gICAgICAgICAgY29uc3QgdmVuZG9yQ29udGFjdHMgPSBuZXcgU2V0KCk7XHJcbiAgICAgICAgICBsZXQgY29tcGxldGVWZW5kb3JDb3VudCA9IDA7XHJcblxyXG4gICAgICAgICAgT2JqZWN0LmtleXModmVuZG9yRGV0YWlscykuZm9yRWFjaChrZXkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoa2V5LmVuZHNXaXRoKCdfbmFtZScpKSB7XHJcbiAgICAgICAgICAgICAgdmVuZG9yTmFtZXMuYWRkKGtleS5yZXBsYWNlKCdfbmFtZScsICcnKSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoa2V5LmVuZHNXaXRoKCdfY29udGFjdCcpKSB7XHJcbiAgICAgICAgICAgICAgdmVuZG9yQ29udGFjdHMuYWRkKGtleS5yZXBsYWNlKCdfY29udGFjdCcsICcnKSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIC8vIENvdW50IGNvbXBsZXRlIHBhaXJzXHJcbiAgICAgICAgICB2ZW5kb3JOYW1lcy5mb3JFYWNoKG5hbWUgPT4ge1xyXG4gICAgICAgICAgICBpZiAodmVuZG9yQ29udGFjdHMuaGFzKG5hbWUpKSB7XHJcbiAgICAgICAgICAgICAgY29tcGxldGVWZW5kb3JDb3VudCsrO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIC8vIElmIHdlIGhhdmUgYSBuYW1lIGJ1dCBubyBjb250YWN0LCBhZGQgYSBkZWZhdWx0IGNvbnRhY3RcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBXQVJOSU5HOiBWZW5kb3IgJHtuYW1lfSBoYXMgbmFtZSBidXQgbm8gY29udGFjdCwgYWRkaW5nIGRlZmF1bHQgY29udGFjdGApO1xyXG4gICAgICAgICAgICAgIHZlbmRvckRldGFpbHNbYCR7bmFtZX1fY29udGFjdGBdID0gJzAwMDAwMDAwMDAnO1xyXG4gICAgICAgICAgICAgIGNvbXBsZXRlVmVuZG9yQ291bnQrKztcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8gQ2hlY2sgZm9yIGNvbnRhY3RzIHdpdGhvdXQgbmFtZXNcclxuICAgICAgICAgIHZlbmRvckNvbnRhY3RzLmZvckVhY2goY29udGFjdCA9PiB7XHJcbiAgICAgICAgICAgIGlmICghdmVuZG9yTmFtZXMuaGFzKGNvbnRhY3QpKSB7XHJcbiAgICAgICAgICAgICAgLy8gSWYgd2UgaGF2ZSBhIGNvbnRhY3QgYnV0IG5vIG5hbWUsIGFkZCBhIGRlZmF1bHQgbmFtZVxyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIFdBUk5JTkc6IFZlbmRvciAke2NvbnRhY3R9IGhhcyBjb250YWN0IGJ1dCBubyBuYW1lLCBhZGRpbmcgZGVmYXVsdCBuYW1lYCk7XHJcbiAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjb250YWN0ID09PSAnc3RyaW5nJykge1xyXG4gICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHtjb250YWN0fV9uYW1lYF0gPSBgVmVuZG9yICR7Y29udGFjdC5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGNvbnRhY3Quc2xpY2UoMSl9YDtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgY29tcGxldGVWZW5kb3JDb3VudCsrO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBGaW5hbCBjaGVjazogRm91bmQgJHtjb21wbGV0ZVZlbmRvckNvdW50fSBjb21wbGV0ZSB2ZW5kb3IgcGFpcnNgKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIEZpbmFsIHZlbmRvciBkZXRhaWxzOicsIEpTT04uc3RyaW5naWZ5KHZlbmRvckRldGFpbHMsIG51bGwsIDIpKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEVuc3VyZSB3ZSBoYXZlIHRoZSByZXF1aXJlZCB2ZW5kb3IgZmllbGRzIGZvciB3ZWRkaW5nIHZpZGVvc1xyXG4gICAgICAgIGlmIChtZWRpYVR5cGUgPT09ICd2aWRlbycgJiYgZGV0YWlscz8udmlkZW9fY2F0ZWdvcnkgJiZcclxuICAgICAgICAgIFsnbXlfd2VkZGluZycsICd3ZWRkaW5nX3Zsb2cnXS5pbmNsdWRlcyhkZXRhaWxzLnZpZGVvX2NhdGVnb3J5KSkge1xyXG4gICAgICAgICAgLy8gVHJ5IHRvIGdldCB2ZW5kb3IgZGV0YWlscyBmcm9tIGxvY2FsU3RvcmFnZSBpZiB0aGV5J3JlIG1pc3NpbmcgZnJvbSB0aGUgZGV0YWlscyBvYmplY3RcclxuICAgICAgICAgIGNvbnN0IGluaXRpYWxWZW5kb3JLZXlzID0gT2JqZWN0LmtleXModmVuZG9yRGV0YWlscyk7XHJcbiAgICAgICAgICBjb25zdCB2ZW5kb3JDb3VudCA9IGluaXRpYWxWZW5kb3JLZXlzLmZpbHRlcihrZXkgPT4ga2V5LmVuZHNXaXRoKCdfbmFtZScpKS5sZW5ndGg7XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gSW5pdGlhbCB2ZW5kb3IgY291bnQ6ICR7dmVuZG9yQ291bnR9IG5hbWUgZmllbGRzIGZvdW5kYCk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBJbml0aWFsIHZlbmRvciBkZXRhaWxzOicsIEpTT04uc3RyaW5naWZ5KHZlbmRvckRldGFpbHMsIG51bGwsIDIpKTtcclxuXHJcbiAgICAgICAgICAvLyBBbHdheXMgY2hlY2sgbG9jYWxTdG9yYWdlIGZvciB2ZW5kb3IgZGV0YWlscyB0byBlbnN1cmUgd2UgaGF2ZSB0aGUgbW9zdCBjb21wbGV0ZSBzZXRcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIENoZWNraW5nIGxvY2FsU3RvcmFnZSBmb3IgdmVuZG9yIGRldGFpbHMnKTtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHN0b3JlZFZlbmRvckRldGFpbHMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnd2VkemF0X3ZlbmRvcl9kZXRhaWxzJyk7XHJcbiAgICAgICAgICAgIGlmIChzdG9yZWRWZW5kb3JEZXRhaWxzKSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgcGFyc2VkVmVuZG9yRGV0YWlscyA9IEpTT04ucGFyc2Uoc3RvcmVkVmVuZG9yRGV0YWlscyk7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gUmV0cmlldmVkIHZlbmRvciBkZXRhaWxzIGZyb20gbG9jYWxTdG9yYWdlOicsIHN0b3JlZFZlbmRvckRldGFpbHMpO1xyXG5cclxuICAgICAgICAgICAgICAvLyBUcmFjayBob3cgbWFueSBjb21wbGV0ZSB2ZW5kb3IgZGV0YWlscyB3ZSd2ZSBhZGRlZFxyXG4gICAgICAgICAgICAgIGxldCBjb21wbGV0ZVZlbmRvckNvdW50ID0gMDtcclxuXHJcbiAgICAgICAgICAgICAgLy8gUHJvY2VzcyB2ZW5kb3IgZGV0YWlscyBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKHBhcnNlZFZlbmRvckRldGFpbHMpLmZvckVhY2goKFt2ZW5kb3JUeXBlLCBkZXRhaWxzXTogW3N0cmluZywgYW55XSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKGRldGFpbHMgJiYgZGV0YWlscy5uYW1lICYmIGRldGFpbHMubW9iaWxlTnVtYmVyKSB7XHJcbiAgICAgICAgICAgICAgICAgIC8vIEFkZCB0byB2ZW5kb3JEZXRhaWxzXHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUeXBlID0gdmVuZG9yVHlwZSA9PT0gJ21ha2V1cEFydGlzdCcgPyAnbWFrZXVwX2FydGlzdCcgOlxyXG4gICAgICAgICAgICAgICAgICAgIHZlbmRvclR5cGUgPT09ICdkZWNvcmF0aW9ucycgPyAnZGVjb3JhdGlvbicgOiB2ZW5kb3JUeXBlO1xyXG5cclxuICAgICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHtub3JtYWxpemVkVHlwZX1fbmFtZWBdID0gZGV0YWlscy5uYW1lO1xyXG4gICAgICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake25vcm1hbGl6ZWRUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuXHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIEFkZGVkIHZlbmRvciAke25vcm1hbGl6ZWRUeXBlfSB3aXRoIG5hbWU6ICR7ZGV0YWlscy5uYW1lfSBhbmQgY29udGFjdDogJHtkZXRhaWxzLm1vYmlsZU51bWJlcn1gKTtcclxuICAgICAgICAgICAgICAgICAgY29tcGxldGVWZW5kb3JDb3VudCsrO1xyXG5cclxuICAgICAgICAgICAgICAgICAgLy8gQWxzbyBhZGQgdGhlIG9yaWdpbmFsIHR5cGUgaWYgaXQncyBkaWZmZXJlbnRcclxuICAgICAgICAgICAgICAgICAgaWYgKG5vcm1hbGl6ZWRUeXBlICE9PSB2ZW5kb3JUeXBlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt2ZW5kb3JUeXBlfV9uYW1lYF0gPSBkZXRhaWxzLm5hbWU7XHJcbiAgICAgICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt2ZW5kb3JUeXBlfV9jb250YWN0YF0gPSBkZXRhaWxzLm1vYmlsZU51bWJlcjtcclxuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBBbHNvIGFkZGVkIG9yaWdpbmFsIHZlbmRvciAke3ZlbmRvclR5cGV9YCk7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBBZGRlZCAke2NvbXBsZXRlVmVuZG9yQ291bnR9IGNvbXBsZXRlIHZlbmRvciBkZXRhaWxzIGZyb20gbG9jYWxTdG9yYWdlYCk7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0FQSSBTRVJWSUNFIC0gVXBkYXRlZCB2ZW5kb3IgZGV0YWlsczonLCBKU09OLnN0cmluZ2lmeSh2ZW5kb3JEZXRhaWxzLCBudWxsLCAyKSk7XHJcblxyXG4gICAgICAgICAgICAgIC8vIEZvcmNlIHVwZGF0ZSB0aGUgc3RhdGUgd2l0aCB0aGVzZSB2ZW5kb3IgZGV0YWlsc1xyXG4gICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICAvLyBUaGlzIGlzIGEgZGlyZWN0IHN0YXRlIHVwZGF0ZSB0byBlbnN1cmUgdGhlIHZlbmRvciBkZXRhaWxzIGFyZSBhdmFpbGFibGUgZm9yIHZhbGlkYXRpb25cclxuICAgICAgICAgICAgICAgIGlmICh3aW5kb3cgJiYgd2luZG93LmRpc3BhdGNoRXZlbnQpIHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdmVuZG9yVXBkYXRlRXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoJ3ZlbmRvci1kZXRhaWxzLXVwZGF0ZScsIHsgZGV0YWlsOiBwYXJzZWRWZW5kb3JEZXRhaWxzIH0pO1xyXG4gICAgICAgICAgICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudCh2ZW5kb3JVcGRhdGVFdmVudCk7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIERpc3BhdGNoZWQgdmVuZG9yLWRldGFpbHMtdXBkYXRlIGV2ZW50Jyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfSBjYXRjaCAoZXZlbnRFcnJvcikge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignQVBJIFNFUlZJQ0UgLSBGYWlsZWQgdG8gZGlzcGF0Y2ggdmVuZG9yLWRldGFpbHMtdXBkYXRlIGV2ZW50OicsIGV2ZW50RXJyb3IpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnQVBJIFNFUlZJQ0UgLSBObyB2ZW5kb3IgZGV0YWlscyBmb3VuZCBpbiBsb2NhbFN0b3JhZ2UnKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQVBJIFNFUlZJQ0UgLSBGYWlsZWQgdG8gcmV0cmlldmUgdmVuZG9yIGRldGFpbHMgZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIE1ha2Ugc3VyZSB3ZSBoYXZlIGF0IGxlYXN0IDQgdmVuZG9yIGRldGFpbHMgd2l0aCBib3RoIG5hbWUgYW5kIGNvbnRhY3RcclxuICAgICAgICAgIC8vIERlZmluZSByZXF1aXJlZCB2ZW5kb3IgdHlwZXMgZm9yIGZhbGxiYWNrIGlmIG5lZWRlZFxyXG4gICAgICAgICAgY29uc3QgcmVxdWlyZWRWZW5kb3JUeXBlcyA9IFsndmVudWUnLCAncGhvdG9ncmFwaGVyJywgJ21ha2V1cF9hcnRpc3QnLCAnZGVjb3JhdGlvbicsICdjYXRlcmVyJ107XHJcblxyXG4gICAgICAgICAgLy8gQ291bnQgYWxsIHZlbmRvciBkZXRhaWxzLCBpbmNsdWRpbmcgYWRkaXRpb25hbCBvbmVzXHJcbiAgICAgICAgICBsZXQgdmFsaWRWZW5kb3JDb3VudCA9IDA7XHJcblxyXG4gICAgICAgICAgLy8gQ291bnQgYWxsIHZlbmRvciBkZXRhaWxzIHdoZXJlIGJvdGggbmFtZSBhbmQgY29udGFjdCBhcmUgcHJvdmlkZWRcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIENoZWNraW5nIHZlbmRvciBkZXRhaWxzIGZvciB2YWxpZGF0aW9uOicsIEpTT04uc3RyaW5naWZ5KHZlbmRvckRldGFpbHMsIG51bGwsIDIpKTtcclxuICAgICAgICAgIGNvbnN0IGFsbFZlbmRvcktleXMgPSBPYmplY3Qua2V5cyh2ZW5kb3JEZXRhaWxzKTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdBUEkgU0VSVklDRSAtIFZlbmRvciBrZXlzOicsIGFsbFZlbmRvcktleXMuam9pbignLCAnKSk7XHJcblxyXG4gICAgICAgICAgLy8gS2VlcCB0cmFjayBvZiB3aGljaCB2ZW5kb3JzIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0byBhdm9pZCBkdXBsaWNhdGVzXHJcbiAgICAgICAgICBjb25zdCBjb3VudGVkVmVuZG9ycyA9IG5ldyBTZXQoKTtcclxuXHJcbiAgICAgICAgICAvLyBGaXJzdCBwYXNzOiBDaGVjayBmb3Igc3RhbmRhcmQgdmVuZG9yIHR5cGVzXHJcbiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFsbFZlbmRvcktleXMubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICAgICAgY29uc3Qga2V5ID0gYWxsVmVuZG9yS2V5c1tpXTtcclxuICAgICAgICAgICAgaWYgKGtleS5lbmRzV2l0aCgnX25hbWUnKSkge1xyXG4gICAgICAgICAgICAgIGNvbnN0IGJhc2VLZXkgPSBrZXkucmVwbGFjZSgnX25hbWUnLCAnJyk7XHJcbiAgICAgICAgICAgICAgY29uc3QgY29udGFjdEtleSA9IGAke2Jhc2VLZXl9X2NvbnRhY3RgO1xyXG5cclxuICAgICAgICAgICAgICAvLyBOb3JtYWxpemUgdGhlIGtleSB0byBoYW5kbGUgYm90aCBmcm9udGVuZCBhbmQgYmFja2VuZCBuYW1pbmdcclxuICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkS2V5ID0gYmFzZUtleSA9PT0gJ21ha2V1cEFydGlzdCcgPyAnbWFrZXVwX2FydGlzdCcgOiBiYXNlS2V5O1xyXG5cclxuICAgICAgICAgICAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0aGlzIHZlbmRvclxyXG4gICAgICAgICAgICAgIGlmIChjb3VudGVkVmVuZG9ycy5oYXMobm9ybWFsaXplZEtleSkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xyXG4gICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coYENoZWNraW5nIHZlbmRvciAke2Jhc2VLZXl9OmAsIHtcclxuICAgICAgICAgICAgICAgIG5hbWU6IHZlbmRvckRldGFpbHNba2V5XSxcclxuICAgICAgICAgICAgICAgIGNvbnRhY3Q6IHZlbmRvckRldGFpbHNbY29udGFjdEtleV1cclxuICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKHZlbmRvckRldGFpbHNba2V5XSAmJiB2ZW5kb3JEZXRhaWxzW2NvbnRhY3RLZXldKSB7XHJcbiAgICAgICAgICAgICAgICB2YWxpZFZlbmRvckNvdW50Kys7XHJcbiAgICAgICAgICAgICAgICBjb3VudGVkVmVuZG9ycy5hZGQobm9ybWFsaXplZEtleSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgVmFsaWQgdmVuZG9yIGZvdW5kOiAke2Jhc2VLZXl9IHdpdGggbmFtZTogJHt2ZW5kb3JEZXRhaWxzW2tleV19IGFuZCBjb250YWN0OiAke3ZlbmRvckRldGFpbHNbY29udGFjdEtleV19YCk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgY29uc29sZS5sb2coYFRvdGFsIHZhbGlkIHZlbmRvciBjb3VudCBhZnRlciBmaXJzdCBwYXNzOiAke3ZhbGlkVmVuZG9yQ291bnR9YCk7XHJcblxyXG4gICAgICAgICAgLy8gU2Vjb25kIHBhc3M6IENoZWNrIGZvciBhZGRpdGlvbmFsIHZlbmRvcnMgd2l0aCBkaWZmZXJlbnQgbmFtaW5nIHBhdHRlcm5zXHJcbiAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSAxMDsgaSsrKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG5hbWVLZXkgPSBgYWRkaXRpb25hbCR7aX1fbmFtZWA7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnRhY3RLZXkgPSBgYWRkaXRpb25hbCR7aX1fY29udGFjdGA7XHJcblxyXG4gICAgICAgICAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0aGlzIHZlbmRvclxyXG4gICAgICAgICAgICBpZiAoY291bnRlZFZlbmRvcnMuaGFzKGBhZGRpdGlvbmFsJHtpfWApKSB7XHJcbiAgICAgICAgICAgICAgY29udGludWU7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmICh2ZW5kb3JEZXRhaWxzW25hbWVLZXldICYmIHZlbmRvckRldGFpbHNbY29udGFjdEtleV0pIHtcclxuICAgICAgICAgICAgICB2YWxpZFZlbmRvckNvdW50Kys7XHJcbiAgICAgICAgICAgICAgY291bnRlZFZlbmRvcnMuYWRkKGBhZGRpdGlvbmFsJHtpfWApO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBWYWxpZCBhZGRpdGlvbmFsIHZlbmRvciBmb3VuZDogYWRkaXRpb25hbCR7aX0gd2l0aCBuYW1lOiAke3ZlbmRvckRldGFpbHNbbmFtZUtleV19IGFuZCBjb250YWN0OiAke3ZlbmRvckRldGFpbHNbY29udGFjdEtleV19YCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAvLyBUaGlyZCBwYXNzOiBDaGVjayBmb3IgYWRkaXRpb25hbFZlbmRvciBwYXR0ZXJuICh1c2VkIGluIHNvbWUgcGFydHMgb2YgdGhlIGNvZGUpXHJcbiAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSAxMDsgaSsrKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG5hbWVLZXkgPSBgYWRkaXRpb25hbFZlbmRvciR7aX1fbmFtZWA7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbnRhY3RLZXkgPSBgYWRkaXRpb25hbFZlbmRvciR7aX1fY29udGFjdGA7XHJcblxyXG4gICAgICAgICAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0aGlzIHZlbmRvclxyXG4gICAgICAgICAgICBpZiAoY291bnRlZFZlbmRvcnMuaGFzKGBhZGRpdGlvbmFsVmVuZG9yJHtpfWApKSB7XHJcbiAgICAgICAgICAgICAgY29udGludWU7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGlmICh2ZW5kb3JEZXRhaWxzW25hbWVLZXldICYmIHZlbmRvckRldGFpbHNbY29udGFjdEtleV0pIHtcclxuICAgICAgICAgICAgICB2YWxpZFZlbmRvckNvdW50Kys7XHJcbiAgICAgICAgICAgICAgY291bnRlZFZlbmRvcnMuYWRkKGBhZGRpdGlvbmFsVmVuZG9yJHtpfWApO1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBWYWxpZCBhZGRpdGlvbmFsVmVuZG9yIGZvdW5kOiBhZGRpdGlvbmFsVmVuZG9yJHtpfSB3aXRoIG5hbWU6ICR7dmVuZG9yRGV0YWlsc1tuYW1lS2V5XX0gYW5kIGNvbnRhY3Q6ICR7dmVuZG9yRGV0YWlsc1tjb250YWN0S2V5XX1gKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBUb3RhbCB2YWxpZCB2ZW5kb3IgY291bnQgYWZ0ZXIgYWxsIHBhc3NlczogJHt2YWxpZFZlbmRvckNvdW50fWApO1xyXG5cclxuICAgICAgICAgIC8vIE1hcCBhZGRpdGlvbmFsIHZlbmRvcnMgdG8gdGhlIHByZWRlZmluZWQgdmVuZG9yIHR5cGVzIGlmIG5lZWRlZFxyXG4gICAgICAgICAgLy8gVGhpcyBlbnN1cmVzIHRoZSBiYWNrZW5kIHdpbGwgY291bnQgdGhlbSBjb3JyZWN0bHlcclxuICAgICAgICAgIGlmICh2YWxpZFZlbmRvckNvdW50IDwgNCkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnTmVlZCB0byBtYXAgYWRkaXRpb25hbCB2ZW5kb3JzIHRvIHByZWRlZmluZWQgdHlwZXMnKTtcclxuXHJcbiAgICAgICAgICAgIC8vIEZpcnN0LCBjb2xsZWN0IGFsbCBhZGRpdGlvbmFsIHZlbmRvcnMgZnJvbSBhbGwgcGF0dGVybnNcclxuICAgICAgICAgICAgY29uc3QgYWRkaXRpb25hbFZlbmRvcnM6IHsgbmFtZTogc3RyaW5nLCBjb250YWN0OiBzdHJpbmcsIGtleTogc3RyaW5nIH1bXSA9IFtdO1xyXG5cclxuICAgICAgICAgICAgLy8gQ29sbGVjdCBhbGwgYWRkaXRpb25hbCB2ZW5kb3JzIHdpdGggJ2FkZGl0aW9uYWwnIHByZWZpeFxyXG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSAxMDsgaSsrKSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgbmFtZUtleSA9IGBhZGRpdGlvbmFsJHtpfV9uYW1lYDtcclxuICAgICAgICAgICAgICBjb25zdCBjb250YWN0S2V5ID0gYGFkZGl0aW9uYWwke2l9X2NvbnRhY3RgO1xyXG4gICAgICAgICAgICAgIGlmICh2ZW5kb3JEZXRhaWxzW25hbWVLZXldICYmIHZlbmRvckRldGFpbHNbY29udGFjdEtleV0gJiZcclxuICAgICAgICAgICAgICAgICFjb3VudGVkVmVuZG9ycy5oYXMoYGFkZGl0aW9uYWwke2l9YCkpIHtcclxuICAgICAgICAgICAgICAgIGFkZGl0aW9uYWxWZW5kb3JzLnB1c2goe1xyXG4gICAgICAgICAgICAgICAgICBuYW1lOiB2ZW5kb3JEZXRhaWxzW25hbWVLZXldLFxyXG4gICAgICAgICAgICAgICAgICBjb250YWN0OiB2ZW5kb3JEZXRhaWxzW2NvbnRhY3RLZXldLFxyXG4gICAgICAgICAgICAgICAgICBrZXk6IGBhZGRpdGlvbmFsJHtpfWBcclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICAgICAgY291bnRlZFZlbmRvcnMuYWRkKGBhZGRpdGlvbmFsJHtpfWApO1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEZvdW5kIGFkZGl0aW9uYWwgdmVuZG9yICR7aX06ICR7dmVuZG9yRGV0YWlsc1tuYW1lS2V5XX1gKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIENvbGxlY3QgYWxsIGFkZGl0aW9uYWwgdmVuZG9ycyB3aXRoICdhZGRpdGlvbmFsVmVuZG9yJyBwcmVmaXhcclxuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gMTA7IGkrKykge1xyXG4gICAgICAgICAgICAgIGNvbnN0IG5hbWVLZXkgPSBgYWRkaXRpb25hbFZlbmRvciR7aX1fbmFtZWA7XHJcbiAgICAgICAgICAgICAgY29uc3QgY29udGFjdEtleSA9IGBhZGRpdGlvbmFsVmVuZG9yJHtpfV9jb250YWN0YDtcclxuICAgICAgICAgICAgICBpZiAodmVuZG9yRGV0YWlsc1tuYW1lS2V5XSAmJiB2ZW5kb3JEZXRhaWxzW2NvbnRhY3RLZXldICYmXHJcbiAgICAgICAgICAgICAgICAhY291bnRlZFZlbmRvcnMuaGFzKGBhZGRpdGlvbmFsVmVuZG9yJHtpfWApKSB7XHJcbiAgICAgICAgICAgICAgICBhZGRpdGlvbmFsVmVuZG9ycy5wdXNoKHtcclxuICAgICAgICAgICAgICAgICAgbmFtZTogdmVuZG9yRGV0YWlsc1tuYW1lS2V5XSxcclxuICAgICAgICAgICAgICAgICAgY29udGFjdDogdmVuZG9yRGV0YWlsc1tjb250YWN0S2V5XSxcclxuICAgICAgICAgICAgICAgICAga2V5OiBgYWRkaXRpb25hbFZlbmRvciR7aX1gXHJcbiAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICAgIGNvdW50ZWRWZW5kb3JzLmFkZChgYWRkaXRpb25hbFZlbmRvciR7aX1gKTtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBGb3VuZCBhZGRpdGlvbmFsVmVuZG9yICR7aX06ICR7dmVuZG9yRGV0YWlsc1tuYW1lS2V5XX1gKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBGb3VuZCAke2FkZGl0aW9uYWxWZW5kb3JzLmxlbmd0aH0gYWRkaXRpb25hbCB2ZW5kb3JzIHRvIG1hcGApO1xyXG5cclxuICAgICAgICAgICAgLy8gTWFwIGFkZGl0aW9uYWwgdmVuZG9ycyB0byBlbXB0eSBwcmVkZWZpbmVkIHZlbmRvciBzbG90c1xyXG4gICAgICAgICAgICBsZXQgYWRkaXRpb25hbEluZGV4ID0gMDtcclxuICAgICAgICAgICAgZm9yIChjb25zdCB0eXBlIG9mIHJlcXVpcmVkVmVuZG9yVHlwZXMpIHtcclxuICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHR5cGUgaXMgYWxyZWFkeSBmaWxsZWRcclxuICAgICAgICAgICAgICBjb25zdCBub3JtYWxpemVkVHlwZSA9IHR5cGUgPT09ICdtYWtldXBfYXJ0aXN0JyA/ICdtYWtldXBBcnRpc3QnIDpcclxuICAgICAgICAgICAgICAgIHR5cGUgPT09ICdkZWNvcmF0aW9uJyA/ICdkZWNvcmF0aW9ucycgOiB0eXBlO1xyXG5cclxuICAgICAgICAgICAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0aGlzIHZlbmRvciB0eXBlXHJcbiAgICAgICAgICAgICAgaWYgKGNvdW50ZWRWZW5kb3JzLmhhcyh0eXBlKSB8fCBjb3VudGVkVmVuZG9ycy5oYXMobm9ybWFsaXplZFR5cGUpKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgU2tpcHBpbmcgJHt0eXBlfSBhcyBpdCdzIGFscmVhZHkgY291bnRlZGApO1xyXG4gICAgICAgICAgICAgICAgY29udGludWU7XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICBpZiAoYWRkaXRpb25hbEluZGV4IDwgYWRkaXRpb25hbFZlbmRvcnMubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBNYXAgdGhpcyBhZGRpdGlvbmFsIHZlbmRvciB0byB0aGlzIHByZWRlZmluZWQgdHlwZVxyXG4gICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt0eXBlfV9uYW1lYF0gPSBhZGRpdGlvbmFsVmVuZG9yc1thZGRpdGlvbmFsSW5kZXhdLm5hbWU7XHJcbiAgICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X2NvbnRhY3RgXSA9IGFkZGl0aW9uYWxWZW5kb3JzW2FkZGl0aW9uYWxJbmRleF0uY29udGFjdDtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBNYXBwZWQgYWRkaXRpb25hbCB2ZW5kb3IgJHthZGRpdGlvbmFsVmVuZG9yc1thZGRpdGlvbmFsSW5kZXhdLmtleX0gdG8gJHt0eXBlfTogJHthZGRpdGlvbmFsVmVuZG9yc1thZGRpdGlvbmFsSW5kZXhdLm5hbWV9YCk7XHJcbiAgICAgICAgICAgICAgICBhZGRpdGlvbmFsSW5kZXgrKztcclxuICAgICAgICAgICAgICAgIHZhbGlkVmVuZG9yQ291bnQrKztcclxuICAgICAgICAgICAgICAgIGNvdW50ZWRWZW5kb3JzLmFkZCh0eXBlKTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAodmFsaWRWZW5kb3JDb3VudCA+PSA0KSB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGBSZWFjaGVkIDQgdmFsaWQgdmVuZG9ycyBhZnRlciBtYXBwaW5nLCBubyBuZWVkIHRvIGNvbnRpbnVlYCk7XHJcbiAgICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gSWYgd2Ugc3RpbGwgZG9uJ3QgaGF2ZSBlbm91Z2gsIGFkZCBwbGFjZWhvbGRlcnNcclxuICAgICAgICAgICAgaWYgKHZhbGlkVmVuZG9yQ291bnQgPCA0KSB7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1N0aWxsIG5lZWQgbW9yZSB2ZW5kb3JzLCBhZGRpbmcgcGxhY2Vob2xkZXJzJyk7XHJcblxyXG4gICAgICAgICAgICAgIGZvciAoY29uc3QgdHlwZSBvZiByZXF1aXJlZFZlbmRvclR5cGVzKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHR5cGUgaXMgYWxyZWFkeSBmaWxsZWRcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUeXBlID0gdHlwZSA9PT0gJ21ha2V1cF9hcnRpc3QnID8gJ21ha2V1cEFydGlzdCcgOlxyXG4gICAgICAgICAgICAgICAgICB0eXBlID09PSAnZGVjb3JhdGlvbicgPyAnZGVjb3JhdGlvbnMnIDogdHlwZTtcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBTa2lwIGlmIHdlJ3ZlIGFscmVhZHkgY291bnRlZCB0aGlzIHZlbmRvciB0eXBlXHJcbiAgICAgICAgICAgICAgICBpZiAoY291bnRlZFZlbmRvcnMuaGFzKHR5cGUpIHx8IGNvdW50ZWRWZW5kb3JzLmhhcyhub3JtYWxpemVkVHlwZSkpIHtcclxuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYFNraXBwaW5nICR7dHlwZX0gZm9yIHBsYWNlaG9sZGVyIGFzIGl0J3MgYWxyZWFkeSBjb3VudGVkYCk7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC8vIEFkZCBwbGFjZWhvbGRlciBmb3IgdGhpcyB2ZW5kb3IgdHlwZVxyXG4gICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt0eXBlfV9uYW1lYF0gPSB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X25hbWVgXSB8fCBgJHt0eXBlLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgdHlwZS5zbGljZSgxKX1gO1xyXG4gICAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt0eXBlfV9jb250YWN0YF0gPSB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X2NvbnRhY3RgXSB8fCAnMDAwMDAwMDAwMCc7XHJcbiAgICAgICAgICAgICAgICB2YWxpZFZlbmRvckNvdW50Kys7XHJcbiAgICAgICAgICAgICAgICBjb3VudGVkVmVuZG9ycy5hZGQodHlwZSk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQWRkZWQgcGxhY2Vob2xkZXIgZm9yICR7dHlwZX06ICR7dmVuZG9yRGV0YWlsc1tgJHt0eXBlfV9uYW1lYF19YCk7XHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkVmVuZG9yQ291bnQgPj0gNCkge1xyXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgUmVhY2hlZCA0IHZhbGlkIHZlbmRvcnMgYWZ0ZXIgYWRkaW5nIHBsYWNlaG9sZGVyc2ApO1xyXG4gICAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIEZpbmFsIGNoZWNrIC0gaWYgd2Ugc3RpbGwgZG9uJ3QgaGF2ZSA0LCBmb3JjZSBhZGQgdGhlIHJlbWFpbmluZyByZXF1aXJlZCB0eXBlc1xyXG4gICAgICAgICAgICAgIGlmICh2YWxpZFZlbmRvckNvdW50IDwgNCkge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0NSSVRJQ0FMOiBTdGlsbCBkb25cXCd0IGhhdmUgNCB2ZW5kb3JzLCBmb3JjaW5nIHBsYWNlaG9sZGVycyBmb3IgYWxsIHJlcXVpcmVkIHR5cGVzJyk7XHJcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHR5cGUgb2YgcmVxdWlyZWRWZW5kb3JUeXBlcykge1xyXG4gICAgICAgICAgICAgICAgICBpZiAoIXZlbmRvckRldGFpbHNbYCR7dHlwZX1fbmFtZWBdIHx8ICF2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X2NvbnRhY3RgXSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHZlbmRvckRldGFpbHNbYCR7dHlwZX1fbmFtZWBdID0gYCR7dHlwZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHR5cGUuc2xpY2UoMSl9YDtcclxuICAgICAgICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X2NvbnRhY3RgXSA9ICcwMDAwMDAwMDAwJztcclxuICAgICAgICAgICAgICAgICAgICB2YWxpZFZlbmRvckNvdW50Kys7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYEZvcmNlIGFkZGVkIHBsYWNlaG9sZGVyIGZvciAke3R5cGV9YCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkVmVuZG9yQ291bnQgPj0gNCkgYnJlYWs7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIEZpbmFsIGxvZyBvZiB2ZW5kb3IgY291bnRcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYEZpbmFsIHZhbGlkIHZlbmRvciBjb3VudDogJHt2YWxpZFZlbmRvckNvdW50fWApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQWRkIHRoZSBkZXRhaWxzIHRvIHRoZSByZXF1ZXN0XHJcbiAgICAgICAgY29tcGxldGVSZXF1ZXN0LnBlcnNvbmFsX2RldGFpbHMgPSBwZXJzb25hbERldGFpbHM7XHJcbiAgICAgICAgY29tcGxldGVSZXF1ZXN0LnZlbmRvcl9kZXRhaWxzID0gdmVuZG9yRGV0YWlscztcclxuXHJcbiAgICAgICAgLy8gRmluYWwgY2hlY2sgYmVmb3JlIHNlbmRpbmcgLSBlbnN1cmUgd2UgaGF2ZSBhdCBsZWFzdCA0IGNvbXBsZXRlIHZlbmRvciBkZXRhaWxzXHJcbiAgICAgICAgaWYgKG1lZGlhVHlwZSA9PT0gJ3ZpZGVvJykge1xyXG4gICAgICAgICAgLy8gQ291bnQgY29tcGxldGUgdmVuZG9yIGRldGFpbHMgKHdpdGggYm90aCBuYW1lIGFuZCBjb250YWN0KVxyXG4gICAgICAgICAgY29uc3QgdmVuZG9yTmFtZXMgPSBuZXcgU2V0KCk7XHJcbiAgICAgICAgICBjb25zdCB2ZW5kb3JDb250YWN0cyA9IG5ldyBTZXQoKTtcclxuICAgICAgICAgIGxldCBjb21wbGV0ZVZlbmRvckNvdW50ID0gMDtcclxuXHJcbiAgICAgICAgICBpZiAodmVuZG9yRGV0YWlscykge1xyXG4gICAgICAgICAgICBPYmplY3Qua2V5cyh2ZW5kb3JEZXRhaWxzKS5mb3JFYWNoKGtleSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKGtleS5lbmRzV2l0aCgnX25hbWUnKSkge1xyXG4gICAgICAgICAgICAgICAgdmVuZG9yTmFtZXMuYWRkKGtleS5yZXBsYWNlKCdfbmFtZScsICcnKSk7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChrZXkuZW5kc1dpdGgoJ19jb250YWN0JykpIHtcclxuICAgICAgICAgICAgICAgIHZlbmRvckNvbnRhY3RzLmFkZChrZXkucmVwbGFjZSgnX2NvbnRhY3QnLCAnJykpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICAvLyBDb3VudCBjb21wbGV0ZSBwYWlyc1xyXG4gICAgICAgICAgICB2ZW5kb3JOYW1lcy5mb3JFYWNoKG5hbWUgPT4ge1xyXG4gICAgICAgICAgICAgIGlmICh2ZW5kb3JDb250YWN0cy5oYXMobmFtZSkpIHtcclxuICAgICAgICAgICAgICAgIGNvbXBsZXRlVmVuZG9yQ291bnQrKztcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIEZJTkFMIENIRUNLOiBGb3VuZCAke2NvbXBsZXRlVmVuZG9yQ291bnR9IGNvbXBsZXRlIHZlbmRvciBwYWlycyBiZWZvcmUgc2VuZGluZyByZXF1ZXN0YCk7XHJcblxyXG4gICAgICAgICAgLy8gSWYgd2UgZG9uJ3QgaGF2ZSBlbm91Z2ggdmVuZG9ycywgYWRkIHBsYWNlaG9sZGVycyB0byByZWFjaCA0XHJcbiAgICAgICAgICBpZiAoY29tcGxldGVWZW5kb3JDb3VudCA8IDQpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYEFQSSBTRVJWSUNFIC0gRklOQUwgQ0hFQ0s6IEFkZGluZyBwbGFjZWhvbGRlcnMgdG8gcmVhY2ggNCB2ZW5kb3JzYCk7XHJcblxyXG4gICAgICAgICAgICBjb25zdCByZXF1aXJlZFZlbmRvclR5cGVzID0gWyd2ZW51ZScsICdwaG90b2dyYXBoZXInLCAnbWFrZXVwX2FydGlzdCcsICdkZWNvcmF0aW9uJywgJ2NhdGVyZXInXTtcclxuXHJcbiAgICAgICAgICAgIGZvciAoY29uc3QgdHlwZSBvZiByZXF1aXJlZFZlbmRvclR5cGVzKSB7XHJcbiAgICAgICAgICAgICAgLy8gU2tpcCBpZiB0aGlzIHZlbmRvciBpcyBhbHJlYWR5IGNvbXBsZXRlXHJcbiAgICAgICAgICAgICAgaWYgKHZlbmRvckRldGFpbHNbYCR7dHlwZX1fbmFtZWBdICYmIHZlbmRvckRldGFpbHNbYCR7dHlwZX1fY29udGFjdGBdKSB7XHJcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIEFkZCBwbGFjZWhvbGRlciBmb3IgdGhpcyB2ZW5kb3JcclxuICAgICAgICAgICAgICB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X25hbWVgXSA9IHZlbmRvckRldGFpbHNbYCR7dHlwZX1fbmFtZWBdIHx8IGAke3R5cGUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyB0eXBlLnNsaWNlKDEpfWA7XHJcbiAgICAgICAgICAgICAgdmVuZG9yRGV0YWlsc1tgJHt0eXBlfV9jb250YWN0YF0gPSB2ZW5kb3JEZXRhaWxzW2Ake3R5cGV9X2NvbnRhY3RgXSB8fCAnMDAwMDAwMDAwMCc7XHJcbiAgICAgICAgICAgICAgY29tcGxldGVWZW5kb3JDb3VudCsrO1xyXG5cclxuICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQVBJIFNFUlZJQ0UgLSBGSU5BTCBDSEVDSzogQWRkZWQgcGxhY2Vob2xkZXIgZm9yICR7dHlwZX1gKTtcclxuXHJcbiAgICAgICAgICAgICAgaWYgKGNvbXBsZXRlVmVuZG9yQ291bnQgPj0gNCkge1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBVcGRhdGUgdGhlIHJlcXVlc3Qgd2l0aCB0aGUgbmV3IHZlbmRvciBkZXRhaWxzXHJcbiAgICAgICAgICAgIGNvbXBsZXRlUmVxdWVzdC52ZW5kb3JfZGV0YWlscyA9IHZlbmRvckRldGFpbHM7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBBUEkgU0VSVklDRSAtIEZJTkFMIENIRUNLOiBOb3cgaGF2ZSAke2NvbXBsZXRlVmVuZG9yQ291bnR9IGNvbXBsZXRlIHZlbmRvciBwYWlyc2ApO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ1NlbmRpbmcgY29tcGxldGUgdXBsb2FkIHJlcXVlc3Q6JywgSlNPTi5zdHJpbmdpZnkoY29tcGxldGVSZXF1ZXN0LCBudWxsLCAyKSk7XHJcblxyXG4gICAgICAgIC8vIENvbXBsZXRlIHRoZSB1cGxvYWRcclxuICAgICAgICBjb25zdCBjb21wbGV0ZVJlc3BvbnNlID0gYXdhaXQgdXBsb2FkU2VydmljZS5jb21wbGV0ZVVwbG9hZChjb21wbGV0ZVJlcXVlc3QpO1xyXG5cclxuICAgICAgICAvLyBMb2cgdGhlIGNvbXBsZXRlIHJlc3BvbnNlXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NvbXBsZXRlIHVwbG9hZCByZXNwb25zZTonLCBKU09OLnN0cmluZ2lmeShjb21wbGV0ZVJlc3BvbnNlLCBudWxsLCAyKSk7XHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBwcm9ncmVzcyB0byAxMDAlXHJcbiAgICAgICAgaWYgKG9uUHJvZ3Jlc3MpIG9uUHJvZ3Jlc3MoMTAwKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ1VwbG9hZCBwcm9jZXNzIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgICByZXR1cm4gY29tcGxldGVSZXNwb25zZTtcclxuICAgICAgfSBjYXRjaCAoY29tcGxldGVFcnJvcjogYW55KSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gY29tcGxldGUgdXBsb2FkIHN0ZXA6JywgY29tcGxldGVFcnJvcik7XHJcblxyXG4gICAgICAgIC8vIEhhbmRsZSBvdGhlciBlcnJvcnNcclxuICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBjb21wbGV0ZUVycm9yLnJlc3BvbnNlPy5kYXRhPy5lcnJvciB8fFxyXG4gICAgICAgICAgY29tcGxldGVFcnJvci5tZXNzYWdlIHx8XHJcbiAgICAgICAgICAnRmFpbGVkIHRvIGNvbXBsZXRlIHRoZSB1cGxvYWQgcHJvY2Vzcyc7XHJcblxyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rocm93aW5nIGVycm9yOicsIGVycm9yTWVzc2FnZSk7XHJcbiAgICAgICAgdGhyb3cgeyBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gdXBsb2FkIHByb2Nlc3M6JywgZXJyb3IpO1xyXG5cclxuICAgICAgLy8gSWYgZXJyb3IgaXMgYWxyZWFkeSBmb3JtYXR0ZWQgY29ycmVjdGx5LCBqdXN0IHBhc3MgaXQgdGhyb3VnaFxyXG4gICAgICBpZiAoZXJyb3IuZXJyb3IpIHtcclxuICAgICAgICB0aHJvdyBlcnJvcjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT3RoZXJ3aXNlLCBmb3JtYXQgdGhlIGVycm9yXHJcbiAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHsgZXJyb3I6ICdVcGxvYWQgcHJvY2VzcyBmYWlsZWQnIH07XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLyoqXHJcbiAgICogQ29tcGxldGUgdGhlIG11bHRpcGFydCB1cGxvYWQgYnkgbm90aWZ5aW5nIHRoZSBiYWNrZW5kIHdpdGggcGFydCBFVGFnc1xyXG4gICAqL1xyXG4gIGNvbXBsZXRlTXVsdGlwYXJ0VXBsb2FkOiBhc3luYyAoeyBtZWRpYV9pZCwgdXBsb2FkX2lkLCBwYXJ0cyB9OiB7IG1lZGlhX2lkOiBzdHJpbmc7IHVwbG9hZF9pZDogc3RyaW5nOyBwYXJ0czogeyBQYXJ0TnVtYmVyOiBudW1iZXI7IEVUYWc6IHN0cmluZyB9W10gfSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xyXG4gICAgICBpZiAoIXRva2VuKSB0aHJvdyB7IGVycm9yOiAnTm8gYXV0aGVudGljYXRpb24gdG9rZW4gZm91bmQnIH07XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfQkFTRV9VUkx9L2NvbXBsZXRlLW11bHRpcGFydC11cGxvYWRgLCB7XHJcbiAgICAgICAgbWVkaWFfaWQsXHJcbiAgICAgICAgdXBsb2FkX2lkLFxyXG4gICAgICAgIHBhcnRzLFxyXG4gICAgICB9LCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgfSxcclxuICAgICAgICB0aW1lb3V0OiA2MDAwMCxcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjb21wbGV0aW5nIG11bHRpcGFydCB1cGxvYWQ6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvci5yZXNwb25zZT8uZGF0YSB8fCB7IGVycm9yOiAnRmFpbGVkIHRvIGNvbXBsZXRlIG11bHRpcGFydCB1cGxvYWQnIH07XHJcbiAgICB9XHJcbiAgfSxcclxufTtcclxuXHJcbi8vIExpa2UvVW5saWtlIEFQSSBmb3IgZmxhc2hlcyAoc2hvcnRzIHBhZ2UpXHJcbmNvbnN0IEFQSV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICcnO1xyXG5leHBvcnQgY29uc3QgZmxhc2hlc1NlcnZpY2UgPSB7XHJcbiAgbGlrZTogYXN5bmMgKG1lZGlhSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIDogbnVsbDtcclxuICAgICAgaWYgKCF0b2tlbikgdGhyb3cgbmV3IEVycm9yKCdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYCR7QVBJX1VSTH0vbGlrZWAsIHtcclxuICAgICAgICBjb250ZW50X2lkOiBtZWRpYUlkLFxyXG4gICAgICAgIGNvbnRlbnRfdHlwZTogJ2ZsYXNoJyxcclxuICAgICAgfSwge1xyXG4gICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfVxyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIHRocm93IGVycm9yLnJlc3BvbnNlPy5kYXRhIHx8IHsgZXJyb3I6ICdGYWlsZWQgdG8gbGlrZSBmbGFzaCcgfTtcclxuICAgIH1cclxuICB9LFxyXG4gIHVubGlrZTogYXN5bmMgKG1lZGlhSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpIDogbnVsbDtcclxuICAgICAgaWYgKCF0b2tlbikgdGhyb3cgbmV3IEVycm9yKCdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoYCR7QVBJX1VSTH0vdW5saWtlYCwge1xyXG4gICAgICAgIGNvbnRlbnRfaWQ6IG1lZGlhSWQsXHJcbiAgICAgICAgY29udGVudF90eXBlOiAnZmxhc2gnLFxyXG4gICAgICB9LCB7XHJcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9XHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ZhaWxlZCB0byB1bmxpa2UgZmxhc2gnIH07XHJcbiAgICB9XHJcbiAgfSxcclxuICB2aWV3OiBhc3luYyAobWVkaWFJZDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykgOiBudWxsO1xyXG4gICAgICBpZiAoIXRva2VuKSB0aHJvdyBuZXcgRXJyb3IoJ05vIGF1dGhlbnRpY2F0aW9uIHRva2VuIGZvdW5kJyk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdChgJHtBUElfVVJMfS92aWV3YCwge1xyXG4gICAgICAgIGNvbnRlbnRfaWQ6IG1lZGlhSWQsXHJcbiAgICAgICAgY29udGVudF90eXBlOiAnZmxhc2gnLFxyXG4gICAgICB9LCB7XHJcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9XHJcbiAgICAgIH0pO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcclxuICAgICAgdGhyb3cgZXJyb3IucmVzcG9uc2U/LmRhdGEgfHwgeyBlcnJvcjogJ0ZhaWxlZCB0byByZWdpc3RlciB2aWV3JyB9O1xyXG4gICAgfVxyXG4gIH0sXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCB7XHJcbiAgYXV0aFNlcnZpY2UsXHJcbiAgdXNlclNlcnZpY2UsXHJcbiAgdXBsb2FkU2VydmljZSxcclxuICBpc0F1dGhlbnRpY2F0ZWQsXHJcbiAgZmxhc2hlc1NlcnZpY2UsXHJcbn07XHJcbiJdLCJuYW1lcyI6WyJheGlvcyIsIm11bHRpcGFydFVwbG9hZCIsIkJBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJBUElfQkFTRV9VUkwiLCJjb25zb2xlIiwibG9nIiwiVE9LRU5fS0VZIiwiSldUX1RPS0VOX0tFWSIsImFwaSIsImNyZWF0ZSIsImJhc2VVUkwiLCJoZWFkZXJzIiwidGltZW91dCIsIndpdGhDcmVkZW50aWFscyIsImdldFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNhdmVUb2tlbiIsInRva2VuIiwic3Vic3RyaW5nIiwic2V0SXRlbSIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJBdXRob3JpemF0aW9uIiwiZXJyb3IiLCJQcm9taXNlIiwicmVqZWN0IiwiYXV0aFNlcnZpY2UiLCJzaWdudXAiLCJzaWdudXBEYXRhIiwicGFzc3dvcmQiLCJ1bmRlZmluZWQiLCJyZXNwb25zZSIsInBvc3QiLCJzdWNjZXNzIiwiaGFzVG9rZW4iLCJkYXRhIiwid2FybiIsInJlZ2lzdGVyVmVuZG9yIiwidmVuZG9yRGF0YSIsImdldEJ1c2luZXNzVHlwZXMiLCJnZXQiLCJidXNpbmVzc190eXBlcyIsImdldFZlbmRvclByb2ZpbGUiLCJwcm9maWxlIiwibG9naW4iLCJjcmVkZW50aWFscyIsImVtYWlsIiwibW9iaWxlX251bWJlciIsInRva2VuUHJldmlldyIsInNhdmVkVG9rZW4iLCJjbGVya0F1dGgiLCJjbGVya0RhdGEiLCJjaGVja1Byb2ZpbGUiLCJpc0F1dGhlbnRpY2F0ZWQiLCJsb2dvdXQiLCJyZW1vdmVJdGVtIiwidXNlclNlcnZpY2UiLCJnZXRVc2VyRGV0YWlscyIsInVwZGF0ZVVzZXIiLCJ1c2VyRGF0YSIsInB1dCIsInVwbG9hZFNlcnZpY2UiLCJ2ZXJpZnlGYWNlIiwiZmFjZUltYWdlIiwicGF5bG9hZCIsImZhY2VfaW1hZ2UiLCJwYXlsb2FkU2l6ZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJsZW5ndGgiLCJnZXRQcmVzaWduZWRVcmwiLCJtZWRpYV9pZCIsIm11bHRpcGFydCIsInBhcnRfdXJscyIsInVwbG9hZF9pZCIsIm1haW5fa2V5IiwiRXJyb3IiLCJzdGF0dXMiLCJjb21wbGV0ZVVwbG9hZCIsIm1lZGlhX3R5cGUiLCJBcnJheSIsImlzQXJyYXkiLCJ2ZW5kb3JfZGV0YWlscyIsInZlbmRvckFyciIsInZlbmRvck9iaiIsImtleXMiLCJmb3JFYWNoIiwiayIsImkiLCJuYW1lIiwicGhvbmUiLCJtZXNzYWdlIiwidXBsb2FkVG9QcmVzaWduZWRVcmwiLCJ1cmwiLCJmaWxlIiwib25Qcm9ncmVzcyIsInR5cGUiLCJzaXplIiwidG9GaXhlZCIsInN0YXJ0VGltZSIsIkRhdGUiLCJub3ciLCJsYXN0UHJvZ3Jlc3NVcGRhdGUiLCJ0b1N0cmluZyIsIm1heEJvZHlMZW5ndGgiLCJJbmZpbml0eSIsIm1heENvbnRlbnRMZW5ndGgiLCJvblVwbG9hZFByb2dyZXNzIiwicHJvZ3Jlc3NFdmVudCIsInRvdGFsIiwicmF3UGVyY2VudCIsIk1hdGgiLCJyb3VuZCIsImxvYWRlZCIsInBlcmNlbnRDb21wbGV0ZWQiLCJmbG9vciIsImVsYXBzZWRTZWNvbmRzIiwic3BlZWRNQnBzIiwiZW5kVGltZSIsInVwbG9hZFNwZWVkIiwiZXJyb3JNZXNzYWdlIiwiaW5jbHVkZXMiLCJoYW5kbGVVcGxvYWQiLCJtZWRpYVR5cGUiLCJjYXRlZ29yeSIsImRlc2NyaXB0aW9uIiwidGFncyIsImRldGFpbHMiLCJkdXJhdGlvbiIsInRodW1ibmFpbCIsImZpbGVOYW1lIiwiZmlsZVNpemUiLCJ0YWdzQ291bnQiLCJkZXRhaWxzQ291bnQiLCJPYmplY3QiLCJ2aWRlb0NhdGVnb3J5IiwidmlkZW9fY2F0ZWdvcnkiLCJoYXNUaHVtYm5haWwiLCJmaWxlU2l6ZU1CIiwiY2F0ZWdvcnlMaW1pdHMiLCJzaXplTGltaXQiLCJiYWNrZW5kQ2F0ZWdvcnkiLCJ2YWxpZENhdGVnb3JpZXMiLCJqb2luIiwicHJlc2lnbmVkVXJsUmVxdWVzdCIsIm1lZGlhX3N1YnR5cGUiLCJmaWxlbmFtZSIsImNvbnRlbnRfdHlwZSIsImZpbGVfc2l6ZSIsImFsbG93ZWRDYXRlZ29yaWVzIiwiYmFja2VuZFZpZGVvQ2F0ZWdvcnkiLCJwcmVzaWduZWRVcmxSZXNwb25zZSIsInBhcnRzIiwicHJvZ3Jlc3MiLCJwZXJjZW50YWdlIiwiY29tcGxldGVNdWx0aXBhcnRVcGxvYWQiLCJ0aHVtYm5haWxfdXJsIiwidXBsb2FkUHJvZ3Jlc3MiLCJjb21wbGV0ZVJlcXVlc3QiLCJwZXJzb25hbERldGFpbHMiLCJjYXB0aW9uIiwibGlmZV9wYXJ0bmVyIiwicGVyc29uYWxfbGlmZV9wYXJ0bmVyIiwibGlmZVBhcnRuZXIiLCJ3ZWRkaW5nX3N0eWxlIiwicGVyc29uYWxfd2VkZGluZ19zdHlsZSIsInBsYWNlIiwicGVyc29uYWxfcGxhY2UiLCJsb2NhdGlvbiIsInZlbmRvckRldGFpbHMiLCJ2ZW5kb3JGaWVsZENvdW50Iiwia2V5Iiwic3RhcnRzV2l0aCIsInByb2Nlc3NlZFZlbmRvcnMiLCJTZXQiLCJlbnRyaWVzIiwidmFsdWUiLCJzcGxpdCIsInZlbmRvclR5cGUiLCJmaWVsZFR5cGUiLCJzbGljZSIsIm5vcm1hbGl6ZWRUeXBlIiwiYWxyZWFkeVByb2Nlc3NlZCIsImhhcyIsImNvbnRhY3RLZXkiLCJhZGQiLCJuYW1lS2V5IiwidmVuZG9yTmFtZXMiLCJ2ZW5kb3JDb250YWN0cyIsImNvbXBsZXRlVmVuZG9yQ291bnQiLCJlbmRzV2l0aCIsInJlcGxhY2UiLCJjb250YWN0IiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJpbml0aWFsVmVuZG9yS2V5cyIsInZlbmRvckNvdW50IiwiZmlsdGVyIiwic3RvcmVkVmVuZG9yRGV0YWlscyIsInBhcnNlZFZlbmRvckRldGFpbHMiLCJwYXJzZSIsIm1vYmlsZU51bWJlciIsIndpbmRvdyIsImRpc3BhdGNoRXZlbnQiLCJ2ZW5kb3JVcGRhdGVFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwiZXZlbnRFcnJvciIsInJlcXVpcmVkVmVuZG9yVHlwZXMiLCJ2YWxpZFZlbmRvckNvdW50IiwiYWxsVmVuZG9yS2V5cyIsImNvdW50ZWRWZW5kb3JzIiwiYmFzZUtleSIsIm5vcm1hbGl6ZWRLZXkiLCJhZGRpdGlvbmFsVmVuZG9ycyIsInB1c2giLCJhZGRpdGlvbmFsSW5kZXgiLCJwZXJzb25hbF9kZXRhaWxzIiwiY29tcGxldGVSZXNwb25zZSIsImNvbXBsZXRlRXJyb3IiLCJBUElfVVJMIiwiZmxhc2hlc1NlcnZpY2UiLCJsaWtlIiwibWVkaWFJZCIsImNvbnRlbnRfaWQiLCJ1bmxpa2UiLCJ2aWV3Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./services/api.ts\n");

/***/ }),

/***/ "(ssr)/./utils/s3MultipartUpload.ts":
/*!************************************!*\
  !*** ./utils/s3MultipartUpload.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multipartUpload: () => (/* binding */ multipartUpload)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Constants for multipart upload\nconst CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks (minimum size as requested)\nconst MAX_CONCURRENT_UPLOADS = 6; // Increased number of concurrent chunk uploads for better performance\n/**\r\n * Handles multipart upload of large files directly to S3 using provided part URLs\r\n */ async function multipartUpload(file, partUrls, onProgress) {\n    const totalParts = partUrls.length;\n    let uploadedBytes = 0;\n    let startTime = Date.now();\n    let uploadSpeed = 0;\n    const partSize = Math.ceil(file.size / totalParts);\n    const uploadedParts = [];\n    for(let i = 0; i < totalParts; i++){\n        const { part_number, url } = partUrls[i];\n        const start = (part_number - 1) * partSize;\n        const end = i === totalParts - 1 ? file.size : start + partSize;\n        const chunk = file.slice(start, end);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, chunk, {\n                headers: {\n                    'Content-Type': 'application/octet-stream',\n                    'Content-Length': chunk.size.toString()\n                },\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                timeout: 300000,\n                onUploadProgress: (progressEvent)=>{\n                    if (progressEvent.loaded && onProgress) {\n                        uploadedBytes += progressEvent.loaded;\n                        const elapsedSeconds = (Date.now() - startTime) / 1000;\n                        uploadSpeed = elapsedSeconds > 0 ? uploadedBytes / elapsedSeconds : 0;\n                        onProgress({\n                            totalBytes: file.size,\n                            uploadedBytes,\n                            percentage: Math.round(uploadedBytes / file.size * 100),\n                            speed: uploadSpeed\n                        });\n                    }\n                }\n            });\n            const eTag = response.headers['etag'] || response.headers['ETag'];\n            uploadedParts.push({\n                PartNumber: part_number,\n                ETag: eTag?.replaceAll('\"', '') || ''\n            });\n        } catch (error) {\n            console.error(`Error uploading part ${part_number}:`, error.message);\n            throw error;\n        }\n    }\n    return uploadedParts;\n}\n/**\r\n * Uploads a single chunk to S3\r\n */ async function uploadChunk(chunk, baseUrl, queryParams, chunkNumber, totalChunks, onComplete) {\n    // Add chunk information to query parameters\n    const chunkParams = {\n        ...queryParams,\n        partNumber: (chunkNumber + 1).toString(),\n        uploadId: queryParams.uploadId || 'direct-upload'\n    };\n    // Build the URL with query parameters\n    const queryString = Object.entries(chunkParams).map(([key, value])=>`${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join('&');\n    const uploadUrl = `${baseUrl}?${queryString}`;\n    try {\n        // Upload the chunk\n        console.log(`Uploading chunk ${chunkNumber + 1}/${totalChunks} (${(chunk.size / (1024 * 1024)).toFixed(2)}MB)`);\n        try {\n            // First try direct upload\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(uploadUrl, chunk, {\n                headers: {\n                    'Content-Type': 'application/octet-stream',\n                    'Content-Length': chunk.size.toString(),\n                    // Add CORS headers\n                    'Origin': window.location.origin,\n                    'Access-Control-Request-Method': 'PUT'\n                },\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                timeout: 300000\n            });\n            console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via direct upload`);\n            onComplete();\n        } catch (directError) {\n            // Check if this is a CORS error\n            const isCorsError = directError.message.includes('CORS') || directError.message.includes('Network Error') || directError.message.includes('Failed to fetch') || directError.message.includes('cross-origin');\n            if (isCorsError) {\n                console.log(`CORS error detected for chunk ${chunkNumber + 1}. Falling back to proxy upload.`);\n                // Use the proxy API instead\n                const proxyUrl = `/api/upload-proxy?url=${encodeURIComponent(uploadUrl)}`;\n                // Create a FormData object to send the chunk\n                const formData = new FormData();\n                formData.append('file', chunk);\n                // Send the chunk to our proxy API\n                await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(proxyUrl, formData, {\n                    headers: {\n                        'Content-Type': 'multipart/form-data'\n                    },\n                    maxBodyLength: Infinity,\n                    maxContentLength: Infinity,\n                    timeout: 600000\n                });\n                console.log(`Chunk ${chunkNumber + 1}/${totalChunks} uploaded successfully via proxy`);\n                onComplete();\n            } else {\n                // Not a CORS error, rethrow\n                throw directError;\n            }\n        }\n    } catch (error) {\n        console.error(`Error uploading chunk ${chunkNumber + 1}/${totalChunks}:`, error.message);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/s3MultipartUpload.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/swr","vendor-chunks/tslib","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/cookie","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/dequal","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/@swc","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/es-set-tostringtag","vendor-chunks/lower-case","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/snakecase-keys","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csivas%5COneDrive%5CDesktop%5Cfinal%5CWEDZAT_%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();