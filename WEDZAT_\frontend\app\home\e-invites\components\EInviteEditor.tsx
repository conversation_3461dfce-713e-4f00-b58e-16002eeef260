"use client";
import React, { useState, useEffect } from "react";
import { ArrowLeft, Upload, Search, Loader2 } from "lucide-react";
import IndianTraditionalTemplate from "./templates/IndianTraditionalTemplate";

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  design_settings: {
    colors?: {
      primary?: string;
      secondary?: string;
      background?: string;
      text?: string;
      [key: string]: string | undefined;
    };
    fonts?: {
      heading?: string;
      body?: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
      [key: string]: string | undefined;
    };
    customImage?: string;
    couple_names?: string;
    type?: string;
  };
  deployed_url: string;
  is_published: boolean;
}

interface EInviteEditorProps {
  templates: Template[];
  einvite: EInvite | null;
  onSave: (einviteData: any) => void;
  onCancel: () => void;
  loading: boolean;
}

const EInviteEditor: React.FC<EInviteEditorProps> = ({
  templates,
  einvite,
  onSave,
  onCancel,
  loading
}) => {
  const [title, setTitle] = useState(einvite?.title || "Our Wedding E-Invite");
  const [weddingDate, setWeddingDate] = useState(einvite?.wedding_date || "");
  const [weddingLocation, setWeddingLocation] = useState(einvite?.wedding_location || "");
  const [aboutCouple, setAboutCouple] = useState(einvite?.about_couple || "");
  const [coupleNames, setCoupleNames] = useState(einvite?.couple_names || einvite?.design_settings?.couple_names || "");
  const [designSettings, setDesignSettings] = useState(einvite?.design_settings || {
    colors: {
      primary: "#B31B1E",
      secondary: "#333333",
      background: "#FFFFFF",
      text: "#000000"
    },
    fonts: {
      heading: "Playfair Display",
      body: "Open Sans",
      coupleNames: "Dancing Script",
      date: "Open Sans",
      location: "Open Sans",
      aboutCouple: "Open Sans"
    },
    customImage: "",
    type: "einvite"
  });

  const [activeTab, setActiveTab] = useState<'content' | 'design'>('content');
  const [uploadingImage, setUploadingImage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchingImages, setSearchingImages] = useState(false);

  // Find the selected template based on the einvite's template_id
  const selectedTemplate = templates.find(t => t.template_id === einvite?.template_id) || templates[0];

  const fontOptions = [
    "Arial", "Helvetica", "Times New Roman", "Georgia", "Verdana",
    "Playfair Display", "Open Sans", "Lato", "Roboto", "Dancing Script",
    "Great Vibes", "Pacifico", "Lobster", "Montserrat", "Poppins"
  ];

  const handleColorChange = (colorType: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  const handleFontChange = (fontType: string, value: string) => {
    setDesignSettings(prev => ({
      ...prev,
      fonts: {
        ...prev.fonts,
        [fontType]: value
      }
    }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadingImage(true);
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setDesignSettings(prev => ({
          ...prev,
          customImage: data.imageUrl
        }));
      } else {
        console.error('Failed to upload image');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  const searchImages = async () => {
    if (!searchQuery.trim()) return;

    setSearchingImages(true);
    try {
      const response = await fetch(`/api/search-images?q=${encodeURIComponent(searchQuery)}`);
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.images || []);
      }
    } catch (error) {
      console.error('Error searching images:', error);
    } finally {
      setSearchingImages(false);
    }
  };

  const selectSearchImage = (imageUrl: string) => {
    setDesignSettings(prev => ({
      ...prev,
      customImage: imageUrl
    }));
    setSearchResults([]);
    setSearchQuery("");
  };



  // Function to render the appropriate template
  const renderTemplate = () => {
    const templateData = {
      partner1_name: designSettings.couple_names?.split(' & ')[0] || coupleNames?.split(' & ')[0] || 'Navneet',
      partner2_name: designSettings.couple_names?.split(' & ')[1] || coupleNames?.split(' & ')[1] || 'Suknya',
      wedding_date: weddingDate || 'Monday, 22th Aug 2022',
      wedding_venue: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',
      muhurtam_time: '7:00 PM',
      reception_date: weddingDate || 'Monday, 23th Aug 2022',
      reception_venue: weddingLocation || 'Unitech Unihomes Plots Sec -Mu Greater Noida',
      reception_time: '10:00 To 11:00 AM',
      couple_photo: designSettings.customImage || '',
      custom_message: aboutCouple || 'May your love story be as magical and charming as in fairy tales!',
      colors: designSettings.colors,
      fonts: designSettings.fonts
    };

    // Check if this is the Indian Traditional template
    if (selectedTemplate?.template_id === 'indian-traditional-001' || selectedTemplate?.name === 'Indian Traditional') {
      return <IndianTraditionalTemplate data={templateData} />;
    }

    // Default template rendering for other templates
    return (
      <div
        className="aspect-[3/4] bg-white rounded-lg shadow-lg overflow-hidden relative"
        style={{
          backgroundImage: designSettings.customImage ? `url(${designSettings.customImage})` : 'none',
          backgroundColor: designSettings.colors?.background || '#FFFFFF',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div className="absolute inset-0 p-6 flex flex-col justify-center items-center text-center">
          <div className="mb-4">
            <p
              className="text-sm mb-2"
              style={{
                color: designSettings.colors?.text || '#000000',
                fontFamily: designSettings.fonts?.heading || 'serif'
              }}
            >
              You are invited to celebrate
            </p>
            <h1
              className="text-2xl font-bold mb-2"
              style={{
                color: designSettings.colors?.primary || '#B31B1E',
                fontFamily: designSettings.fonts?.coupleNames || 'serif'
              }}
            >
              {designSettings.couple_names || coupleNames || 'Couple Names'}
            </h1>
          </div>
          <div className="mb-4">
            <p
              className="text-lg font-semibold mb-1"
              style={{
                color: designSettings.colors?.primary || '#B31B1E',
                fontFamily: designSettings.fonts?.date || 'serif'
              }}
            >
              {weddingDate || 'Wedding Date'}
            </p>
            <p
              className="text-sm"
              style={{
                color: designSettings.colors?.text || '#000000',
                fontFamily: designSettings.fonts?.location || 'sans-serif'
              }}
            >
              {weddingLocation || 'Wedding Location'}
            </p>
          </div>
          {aboutCouple && (
            <p
              className="text-xs italic"
              style={{
                color: designSettings.colors?.text || '#000000',
                fontFamily: designSettings.fonts?.aboutCouple || 'sans-serif'
              }}
            >
              {aboutCouple}
            </p>
          )}
        </div>
      </div>
    );
  };

  const handleSave = () => {
    console.log('Save button clicked');

    // Process colors to ensure they're valid hex codes
    const processedColors = { ...designSettings.colors };
    Object.keys(processedColors).forEach(key => {
      const color = processedColors[key];
      if (color && !color.startsWith('#')) {
        processedColors[key] = `#${color}`;
      }
    });

    // Process fonts to ensure they're valid
    const processedFonts = { ...designSettings.fonts };
    Object.keys(processedFonts).forEach(key => {
      if (!processedFonts[key]) {
        processedFonts[key] = fontOptions[0];
      }
    });

    // Get template_id and REJECT any "default-" IDs
    let templateId = einvite?.template_id || templates[0]?.template_id;

    // REJECT any "default-" template IDs completely
    if (templateId && templateId.includes('default-')) {
      console.error('REJECTING invalid template ID in editor:', templateId);
      templateId = templates.find(t => !t.template_id.includes('default-'))?.template_id;
    }

    if (!templateId) {
      alert('No valid template available. Please refresh the page.');
      return;
    }

    // Prepare the data - EXACTLY like website editor
    const einviteData = {
      title,
      template_id: templateId, // Use validated template ID
      wedding_date: weddingDate || null, // Ensure empty string is sent as null
      wedding_location: weddingLocation,
      about_couple: aboutCouple,
      couple_names: coupleNames,
      design_settings: {
        colors: processedColors,
        fonts: processedFonts,
        // Use only the custom image from upload or search
        customImage: designSettings.customImage || '',
        // Store couple_names in design_settings for backward compatibility
        couple_names: coupleNames,
        // Add type marker for e-invites
        type: 'einvite'
      },
      is_published: true
    };

    console.log('Saving e-invite data:', einviteData);
    onSave(einviteData);
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-4">
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
              <div className="w-4 h-4 bg-gray-400 rounded"></div>
            </div>
            <h2 className="text-2xl font-bold text-black">
              Our Most Trending <span className="text-[#B31B1E]">Invites</span>
            </h2>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center p-6">
        {/* E-Invite Preview */}
        <div className="w-full max-w-md mx-auto mb-6">
          {renderTemplate()}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => setActiveTab(activeTab === 'design' ? 'content' : 'design')}
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Upload size={16} />
            {activeTab === 'design' ? 'Hide Settings' : 'Colors & Background'}
          </button>
          <button
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Search size={16} />
            Resize
          </button>
          <button
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <ArrowLeft size={16} />
            Undo
          </button>
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
          >
            Back to Templates
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="flex items-center gap-2 px-6 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {loading && <Loader2 size={16} className="animate-spin" />}
            Save & Continue Editing
          </button>
        </div>

        {/* Settings Panel - Show when activeTab is 'design' */}
        {activeTab === 'design' && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Colors Section */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-black">Colors</h4>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(designSettings.colors || {}).map(([colorType, colorValue]) => (
                    <div key={colorType}>
                      <label className="block text-sm font-medium text-black mb-2 capitalize">
                        {colorType}
                      </label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={colorValue || '#000000'}
                          onChange={(e) => handleColorChange(colorType, e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={colorValue || ''}
                          onChange={(e) => handleColorChange(colorType, e.target.value)}
                          className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm text-black"
                          placeholder="#000000"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Background Image Section */}
              <div>
                <h4 className="text-lg font-semibold mb-4 text-black">Background Image</h4>

                <div className="mb-4">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors cursor-pointer"
                  >
                    {uploadingImage ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Upload size={16} />
                    )}
                    Upload Image
                  </label>
                </div>

                {designSettings.customImage && (
                  <div className="mt-4">
                    <p className="text-sm font-medium text-black mb-2">Current Background:</p>
                    <div className="w-32 h-20 bg-gray-200 rounded overflow-hidden">
                      <img
                        src={designSettings.customImage}
                        alt="Background"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  );
};

export default EInviteEditor;
