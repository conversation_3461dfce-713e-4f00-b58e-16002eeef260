import React, { useState, useEffect } from "react";
import { uploadService } from "../../services/api";
import { uploadToS3 } from "../../utils/directS3Upload";
import { multipartUpload } from "../../utils/s3MultipartUpload";
import CitySearchInput from "../ui/CitySearchInput";
import Webcam from "react-webcam";

const EVENT_TYPE_OPTIONS = [
  { value: "pre_wedding_shoot", label: "Pre Wedding Shoot" },
  { value: "save_the_date", label: "Save The Date" },
  { value: "engagement", label: "Engagement" },
  { value: "haldi", label: "Hal<PERSON>" },
  { value: "mehndi", label: "<PERSON>hn<PERSON>" },
  { value: "sangeet", label: "Sangeet" },
  { value: "bridal_makeup", label: "Bridal Makeup" },
  { value: "groom_prep", label: "Groom Prep" },
  { value: "groom_entry", label: "Groom Entry" },
  { value: "bridal_entry", label: "Bridal Entry" },
  { value: "couple_entry", label: "Couple Entry" },
  { value: "varmala", label: "<PERSON>arma<PERSON>" },
  { value: "wedding", label: "Wedding" },
  { value: "reception", label: "Reception" },
  { value: "post_wedding_rituals", label: "Post Wedding Rituals" },
  { value: "pre_wedding_rituals", label: "Pre Wedding Rituals" },
  { value: "wedding_film", label: "Wedding Film" },
  { value: "wedding_reel", label: "Wedding Reel" },
  { value: "wedding_teaser", label: "Wedding Teaser" },
  { value: "couple_dance", label: "Couple Dance" },
  { value: "family_dance", label: "Family Dance" },
  { value: "behind_the_scenes", label: "Behind The Scenes" },
  { value: "venue_decor", label: "Venue Decor" },
  { value: "wedding_venue_tour", label: "Wedding Venue Tour" },
  { value: "invitation_unboxing", label: "Invitation Unboxing" },
  { value: "gift_unboxing", label: "Gift Unboxing" },
  { value: "honeymoon", label: "Honeymoon" },
  { value: "couple_story", label: "Couple Story" },
  { value: "travel", label: "Travel" },
  { value: "wedding_shopping", label: "Wedding Shopping" },
  { value: "bachelor_party", label: "Bachelor Party" },
];

interface PhotoUploadModalProps {
  open: boolean;
  onClose: () => void;
}

const WebcamWrapper = React.forwardRef<any, any>((props, ref) =>
  React.createElement(Webcam as any, { ref, ...props })
);

const FaceVerificationModal = ({
  open,
  onVerified,
  onClose,
}: {
  open: boolean;
  onVerified: () => void;
  onClose: () => void;
}) => {
  const webcamRef = React.useRef<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const capture = React.useCallback(async () => {
    if (!webcamRef.current) return;
    setLoading(true);
    setError(null);
    const imageSrc = webcamRef.current.getScreenshot();
    if (!imageSrc) {
      setError("Could not capture image. Please try again.");
      setLoading(false);
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(
        "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/verify-face",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ face_image: imageSrc }),
        }
      );
      if (res.status === 200) {
        onVerified();
      } else {
        const data = await res.json();
        setError(data.error || "Face verification failed. Try again.");
      }
    } catch (e: any) {
      setError("Face verification failed. Try again.");
    } finally {
      setLoading(false);
    }
  }, [onVerified]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
      <div
        style={{
          position: "relative",
          width: "606px",
          height: "663.08px",
          background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
          borderRadius: "20px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "40px",
          gap: "60px",
          isolation: "isolate",
        }}
      >
        {/* Close button */}
        <button
          className="absolute"
          style={{
            top: "20px",
            right: "20px",
            width: "24px",
            height: "24px",
            zIndex: 1,
          }}
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
              stroke="#292D32"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Main content container */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "0px",
            gap: "40px",
            width: "526px",
            height: "583.08px",
            flex: "none",
            order: 0,
            alignSelf: "stretch",
            flexGrow: 0,
            zIndex: 0,
          }}
        >
          {/* Header section */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "20px",
              width: "526px",
              height: "87.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Title row with logo and text */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "20px",
                width: "280px",
                height: "37.08px",
                flex: "none",
                order: 0,
                flexGrow: 0,
              }}
            >
              {/* Heart logo */}
              <div
                style={{
                  width: "47px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="47"
                  height="38"
                  viewBox="0 0 47 38"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                    fill="#B31B1E"
                  />
                </svg>
              </div>

              {/* Title and icon */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "10px",
                  width: "213px",
                  height: "27px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <h2
                  style={{
                    width: "179px",
                    height: "27px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 600,
                    fontSize: "22px",
                    lineHeight: "27px",
                    textAlign: "center",
                    color: "#000000",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Face Verification
                </h2>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.1205 14.0698C12.1005 14.0698 12.0705 14.0698 12.0505 14.0698C12.0205 14.0698 11.9805 14.0698 11.9505 14.0698C9.68047 13.9998 7.98047 12.2298 7.98047 10.0498C7.98047 7.82978 9.79047 6.01978 12.0105 6.01978C14.2305 6.01978 16.0405 7.82978 16.0405 10.0498C16.0305 12.2398 14.3205 13.9998 12.1505 14.0698C12.1305 14.0698 12.1305 14.0698 12.1205 14.0698ZM12.0005 7.50978C10.6005 7.50978 9.47047 8.64978 9.47047 10.0398C9.47047 11.4098 10.5405 12.5198 11.9005 12.5698C11.9305 12.5598 12.0305 12.5598 12.1305 12.5698C13.4705 12.4998 14.5205 11.3998 14.5305 10.0398C14.5305 8.64978 13.4005 7.50978 12.0005 7.50978Z"
                      fill="#292D32"
                    />
                    <path
                      d="M11.9998 23.2899C9.30984 23.2899 6.73984 22.2899 4.74984 20.4699C4.56984 20.3099 4.48984 20.0699 4.50984 19.8399C4.63984 18.6499 5.37984 17.5399 6.60984 16.7199C9.58984 14.7399 14.4198 14.7399 17.3898 16.7199C18.6198 17.5499 19.3598 18.6499 19.4898 19.8399C19.5198 20.0799 19.4298 20.3099 19.2498 20.4699C17.2598 22.2899 14.6898 23.2899 11.9998 23.2899ZM6.07984 19.6399C7.73984 21.0299 9.82984 21.7899 11.9998 21.7899C14.1698 21.7899 16.2598 21.0299 17.9198 19.6399C17.7398 19.0299 17.2598 18.4399 16.5498 17.9599C14.0898 16.3199 9.91984 16.3199 7.43984 17.9599C6.72984 18.4399 6.25984 19.0299 6.07984 19.6399Z"
                      fill="#292D32"
                    />
                    <path
                      d="M12 23.2898C6.07 23.2898 1.25 18.4698 1.25 12.5398C1.25 6.6098 6.07 1.78979 12 1.78979C17.93 1.78979 22.75 6.6098 22.75 12.5398C22.75 18.4698 17.93 23.2898 12 23.2898ZM12 3.28979C6.9 3.28979 2.75 7.4398 2.75 12.5398C2.75 17.6398 6.9 21.7898 12 21.7898C17.1 21.7898 21.25 17.6398 21.25 12.5398C21.25 7.4398 17.1 3.28979 12 3.28979Z"
                      fill="#292D32"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Subtitle */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "8px",
                width: "526px",
                height: "30px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 12.8296C8.83 12.8296 6.25 10.2496 6.25 7.07959C6.25 3.90959 8.83 1.32959 12 1.32959C15.17 1.32959 17.75 3.90959 17.75 7.07959C17.75 10.2496 15.17 12.8296 12 12.8296ZM12 2.82959C9.66 2.82959 7.75 4.73959 7.75 7.07959C7.75 9.41959 9.66 11.3296 12 11.3296C14.34 11.3296 16.25 9.41959 16.25 7.07959C16.25 4.73959 14.34 2.82959 12 2.82959Z"
                    fill="#292D32"
                  />
                  <path
                    d="M3.41016 22.8296C3.00016 22.8296 2.66016 22.4896 2.66016 22.0796C2.66016 17.8096 6.85015 14.3296 12.0002 14.3296C13.0102 14.3296 14.0001 14.4596 14.9601 14.7296C15.3601 14.8396 15.5902 15.2496 15.4802 15.6496C15.3702 16.0496 14.9601 16.2796 14.5602 16.1696C13.7402 15.9396 12.8802 15.8296 12.0002 15.8296C7.68015 15.8296 4.16016 18.6296 4.16016 22.0796C4.16016 22.4896 3.82016 22.8296 3.41016 22.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M18 22.8296C16.34 22.8296 14.78 21.9496 13.94 20.5196C13.49 19.7996 13.25 18.9496 13.25 18.0796C13.25 16.6196 13.9 15.2696 15.03 14.3696C15.87 13.6996 16.93 13.3296 18 13.3296C20.62 13.3296 22.75 15.4596 22.75 18.0796C22.75 18.9496 22.51 19.7996 22.06 20.5296C21.81 20.9496 21.49 21.3296 21.11 21.6496C20.28 22.4096 19.17 22.8296 18 22.8296ZM18 14.8296C17.26 14.8296 16.56 15.0796 15.97 15.5496C15.2 16.1596 14.75 17.0896 14.75 18.0796C14.75 18.6696 14.91 19.2496 15.22 19.7496C15.8 20.7296 16.87 21.3296 18 21.3296C18.79 21.3296 19.55 21.0396 20.13 20.5196C20.39 20.2996 20.61 20.0396 20.77 19.7596C21.09 19.2496 21.25 18.6696 21.25 18.0796C21.25 16.2896 19.79 14.8296 18 14.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M17.4299 19.8196C17.2399 19.8196 17.0499 19.7497 16.8999 19.5997L15.9099 18.6097C15.6199 18.3197 15.6199 17.8396 15.9099 17.5496C16.1999 17.2596 16.6799 17.2596 16.9699 17.5496L17.4499 18.0297L19.0499 16.5496C19.3499 16.2696 19.8299 16.2897 20.1099 16.5897C20.3899 16.8897 20.3699 17.3697 20.0699 17.6497L17.9399 19.6196C17.7899 19.7496 17.6099 19.8196 17.4299 19.8196Z"
                    fill="#292D32"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "157px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Verify your face ID
              </span>
            </div>
          </div>

          {/* Camera section */}
          <div
            style={{
              boxSizing: "border-box",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              padding: "20px 40px",
              gap: "8px",
              width: "526px",
              height: "354px",
              border: "1px solid #D9D9D9",
              borderRadius: "20px",
              flex: "none",
              order: 1,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Camera circle */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                padding: "90px 40px",
                gap: "8px",
                width: "204px",
                height: "204px",
                border: "1px solid #D9D9D9",
                borderRadius: "130px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                position: "relative",
                overflow: "hidden",
              }}
            >
              <WebcamWrapper
                audio={false}
                ref={webcamRef}
                screenshotFormat="image/jpeg"
                width={204}
                height={204}
                videoConstraints={{ facingMode: "user" }}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  borderRadius: "130px",
                }}
              />
            </div>

            {/* Open Camera text */}
            <span
              style={{
                width: "92px",
                height: "17px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "17px",
                color: "#262626",
                flex: "none",
                order: 1,
                flexGrow: 0,
              }}
            >
              Open Camera
            </span>

            {/* Description text */}
            <p
              style={{
                width: "372px",
                height: "56px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 300,
                fontSize: "18px",
                lineHeight: "28px",
                textAlign: "center",
                color: "#000000",
                flex: "none",
                order: 1,
                flexGrow: 0,
                marginTop: "16px",
              }}
            >
              This is a one-time face verification process. It won't appear
              again next time.
            </p>

            {error && (
              <div
                style={{
                  color: "#B31B1E",
                  fontSize: "14px",
                  textAlign: "center",
                  marginTop: "8px",
                }}
              >
                {error}
              </div>
            )}
          </div>

          {/* Bottom buttons */}
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "526px",
              height: "62px",
              flex: "none",
              order: 2,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Back button */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "143px",
                height: "62px",
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                cursor: "pointer",
              }}
              onClick={onClose}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  transform: "rotate(-180deg)",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ transform: "rotate(-180deg)" }}
                >
                  <path
                    d="M11.47 21.58C11.01 21.58 10.57 21.39 10.25 21.07L3.18 14C2.53 13.35 2.53 12.31 3.18 11.66L10.25 4.59C10.9 3.94 11.94 3.94 12.59 4.59C13.24 5.24 13.24 6.28 12.59 6.93L7.66 11.86L20.5 11.86C21.33 11.86 22 12.53 22 13.36C22 14.19 21.33 14.86 20.5 14.86L7.66 14.86L12.59 19.79C13.24 20.44 13.24 21.48 12.59 22.13C12.27 22.45 11.83 22.64 11.47 21.58Z"
                    fill="#262626"
                  />
                  <path
                    d="M11.46 12.17L20.5 12.17C20.91 12.17 21.25 12.53 21.25 12.94C21.25 13.35 20.91 13.71 20.5 13.71L11.46 13.71C11.05 13.71 10.71 13.35 10.71 12.94C10.71 12.53 11.05 12.17 11.46 12.17Z"
                    fill="#262626"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "49px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#292D32",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Back
              </span>
            </div>

            {/* Upload button */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "164px",
                height: "62px",
                background: loading ? "#999999" : "#B31B1E",
                borderRadius: "10px",
                flex: "none",
                order: 1,
                flexGrow: 0,
                cursor: loading ? "not-allowed" : "pointer",
              }}
              onClick={loading ? undefined : capture}
            >
              <span
                style={{
                  width: "70px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#FDFCFF",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {loading ? "Verifying..." : "Upload"}
              </span>
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.43 18.9C14.24 18.9 14.05 18.83 13.9 18.68C13.61 18.39 13.61 17.91 13.9 17.62L19.44 12.08L13.9 6.54C13.61 6.25 13.61 5.77 13.9 5.48C14.19 5.19 14.67 5.19 14.96 5.48L21.03 11.55C21.32 11.84 21.32 12.32 21.03 12.61L14.96 18.68C14.81 18.83 14.62 18.9 14.43 18.9Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M20.33 12.83H3.5C3.09 12.83 2.75 12.49 2.75 12.08C2.75 11.67 3.09 11.33 3.5 11.33H20.33C20.74 11.33 21.08 11.67 21.08 12.08C21.08 12.49 20.74 12.83 20.33 12.83Z"
                    fill="#FFFFFF"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PhotoUploadModal: React.FC<PhotoUploadModalProps> = ({
  open,
  onClose,
}) => {
  const [step, setStep] = useState<
    "file" | "details" | "face" | "uploading" | "success"
  >("file");
  const [file, setFile] = useState<File | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [caption, setCaption] = useState("");
  const [place, setPlace] = useState("");
  const [eventType, setEventType] = useState("");
  const [faceVerified, setFaceVerified] = useState<boolean | null>(null);
  const [userFaceVerified, setUserFaceVerified] = useState<boolean | null>(
    null
  );
  const [checkingFace, setCheckingFace] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  useEffect(() => {
    if (!open) {
      setStep("file");
      setFile(null);
      setFileUrl(null);
      setCaption("");
      setPlace("");
      setEventType("");
      setFaceVerified(null);
      setUserFaceVerified(null);
      setCheckingFace(false);
      setUploading(false);
      setProgress(0);
      setUploadError(null);
      setUploadSuccess(false);
    }
  }, [open]);

  // Step 1: File selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setFile(f);
    setFileUrl(URL.createObjectURL(f));
    setStep("details");
  };

  // Step 2: Details and preview
  const handleDetailsNext = () => {
    if (!caption || !place || !eventType) return;
    setStep("face");
  };

  // Step 3: Face verification check
  useEffect(() => {
    if (step === "face" && userFaceVerified === null) {
      fetch(
        "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user",
        {
          headers: { Authorization: `Bearer ${localStorage.getItem("token")}` },
        }
      )
        .then((res) => res.json())
        .then((data) => setUserFaceVerified(!!data.face_verified))
        .catch(() => setUserFaceVerified(false));
    }
  }, [step, userFaceVerified]);

  const handleFaceVerification = () => {
    setFaceVerified(true);
  };

  // Step 4: Upload logic
  const startUpload = async () => {
    if (!file) return;
    setUploading(true);
    setProgress(5);
    setUploadError(null);
    setUploadSuccess(false);
    try {
      // 1. Get presigned URL
      const presigned = await uploadService.getPresignedUrl({
        media_type: "photo",
        media_subtype: null,
        filename: file.name,
        content_type: file.type,
        file_size: file.size,
      });
      setProgress(20);
      // 2. Upload file using multipart upload
      const parts = await multipartUpload(file, presigned.part_urls, (progress) => {
        setProgress(20 + Math.round(progress.percentage * 0.7));
      });

      // Complete multipart upload
      await uploadService.completeMultipartUpload({
        media_id: presigned.media_id,
        upload_id: presigned.upload_id,
        parts: parts
      });
      setProgress(90);
      // 3. Complete upload
      const completeReq = {
        media_id: presigned.media_id,
        media_type: "photo" as "photo",
        media_subtype: null,
        caption,
        place,
        event_type: eventType,
      };
      await uploadService.completeUpload(completeReq);
      setProgress(100);
      setUploadSuccess(true);
      setTimeout(() => {
        setUploading(false);
        onClose();
      }, 1200);
    } catch (err: any) {
      setUploading(false);
      setUploadError(err?.error || err?.message || "Upload failed.");
    }
  };

  useEffect(() => {
    if (step === "face" && userFaceVerified) {
      setStep("uploading");
      startUpload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [step, userFaceVerified]);

  const setPlaceWithLog = (val: string) => {
    console.log("PhotoUploadModal setPlace:", val);
    setPlace(val);
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
      <div
        className="relative"
        style={{
          width: "700px",
          height: "559.08px",
          background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
          borderRadius: "20px",
          padding: "40px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "60px",
          isolation: "isolate",
        }}
      >
        {/* Close button */}
        <button
          className="absolute text-gray-600 hover:text-gray-800 text-2xl font-light"
          style={{
            top: "20px",
            right: "20px",
            width: "24px",
            height: "24px",
          }}
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
              stroke="#292D32"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {step === "file" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "620px",
              height: "479.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Header */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "0px",
                gap: "20px",
                width: "620px",
                height: "87.08px",
                flex: "none",
                order: 0,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "222px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Heart logo */}
                <div
                  style={{
                    width: "47px",
                    height: "37.08px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title + icon */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "200px",
                    height: "27px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <h2
                    style={{
                      width: "150px",
                      height: "27px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 600,
                      fontSize: "22px",
                      lineHeight: "27px",
                      textAlign: "center",
                      color: "#000000",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    Upload Photo
                  </h2>
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="20"
                      height="21"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.5 18.2047H12.5C16.25 18.2047 17.5 16.9547 17.5 13.2047V8.20469C17.5 4.45469 16.25 3.20469 12.5 3.20469H7.5C3.75 3.20469 2.5 4.45469 2.5 8.20469V13.2047C2.5 16.9547 3.75 18.2047 7.5 18.2047Z"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M7.5 8.95469C8.32843 8.95469 9 8.28312 9 7.45469C9 6.62626 8.32843 5.95469 7.5 5.95469C6.67157 5.95469 6 6.62626 6 7.45469C6 8.28312 6.67157 8.95469 7.5 8.95469Z"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2.67004 15.4547L6.10004 12.8047C6.59004 12.4047 7.35004 12.4547 7.78004 12.9047L8.02004 13.1547C8.50004 13.6547 9.35004 13.6547 9.83004 13.1547L12.55 10.4047C13.03 9.90469 13.88 9.90469 14.36 10.4047L17.5 13.5547"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Subtitle */}
              <p
                style={{
                  width: "620px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                Select a photo to upload
              </p>
            </div>

            {/* Content area */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "250px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Upload section */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "centre",
                  padding: "0px",
                  gap: "30px",
                  width: "280px",
                  height: "250px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0, // ✅ changed from 1 to 0
                  margin: "0 auto",
                }}
              >
                {/* Upload Photos section */}
                <div
                  style={{
                    boxSizing: "border-box",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "20px 40px",
                    gap: "8px",
                    width: "280px",
                    height: "110px",
                    background: "#000000",
                    border: "1px solid #FFFFFF",
                    borderRadius: "20px",
                    flex: "none",
                    order: 0,
                    alignSelf: "stretch",
                    flexGrow: 0,
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = "image/*";
                    input.onchange = handleFileChange;
                    input.click();
                  }}
                >
                  <div
                    style={{
                      width: "24px",
                      height: "24px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </div>
                  <span
                    style={{
                      width: "98px",
                      height: "17px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 500,
                      fontSize: "14px",
                      lineHeight: "17px",
                      color: "#FFFFFF",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    Upload Photos
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom buttons */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "flex-start",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "62px",
                flex: "none",
                order: 2,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Back button */}
              <div
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "143px",
                  height: "62px",
                  border: "1px solid #D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                  cursor: "pointer",
                }}
                onClick={onClose}
              >
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                      fill="#262626"
                    />
                    <path
                      d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                      fill="#262626"
                    />
                  </svg>
                </div>
                <span
                  style={{
                    width: "49px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: "#292D32",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Back
                </span>
              </div>

              {/* Next button */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "140px",
                  height: "62px",
                  background: file ? "#B31B1E" : "#D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                  cursor: file ? "pointer" : "not-allowed",
                }}
                onClick={() => {
                  if (file) {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = "image/*";
                    input.onchange = handleFileChange;
                    input.click();
                  }
                }}
              >
                <span
                  style={{
                    width: "46px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: file ? "#FDFCFF" : "#999999",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Next
                </span>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                      fill={file ? "#FFFFFF" : "#999999"}
                    />
                    <path
                      d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                      fill={file ? "#FFFFFF" : "#999999"}
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === "details" && fileUrl && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "620px",
              height: "479.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Header */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "0px",
                gap: "20px",
                width: "620px",
                height: "87.08px",
                flex: "none",
                order: 0,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "222px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Heart logo */}
                <div
                  style={{
                    width: "47px",
                    height: "37.08px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title + icon */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "155px",
                    height: "27px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <h2
                    style={{
                      width: "130px",
                      height: "27px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 600,
                      fontSize: "22px",
                      lineHeight: "27px",
                      textAlign: "center",
                      color: "#000000",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    Photo Details
                  </h2>
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="20"
                      height="21"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#262626"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#262626"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#262626"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Subtitle */}
              <p
                style={{
                  width: "620px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                Add details to your photo
              </p>
            </div>

            {/* Content area */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "250px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Left side - Form */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "300px",
                  height: "250px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <input
                  style={{
                    width: "100%",
                    padding: "12px 16px",
                    border: "1px solid #D9D9D9",
                    borderRadius: "10px",
                    fontFamily: "Inter",
                    fontSize: "16px",
                    outline: "none",
                  }}
                  placeholder="Add Caption"
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                />
                <div style={{ width: "100%" }}>
                  <CitySearchInput
                    value={place}
                    onChange={setPlaceWithLog}
                    label={undefined}
                    placeholder="Select or search city..."
                  />
                </div>
                <select
                  style={{
                    width: "100%",
                    padding: "12px 16px",
                    border: "1px solid #D9D9D9",
                    borderRadius: "10px",
                    fontFamily: "Inter",
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "24px",
                    color: eventType ? "#262626" : "#999999",
                    background:
                      "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
                    appearance: "none",
                    WebkitAppearance: "none",
                    MozAppearance: "none",
                    backgroundImage:
                      "url(\"data:image/svg+xml;utf8,<svg fill='%23262626' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>\")",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 12px center",
                    backgroundSize: "16px 16px",
                    cursor: "pointer",
                  }}
                  value={eventType}
                  onChange={(e) => setEventType(e.target.value)}
                >
                  <option value="" disabled>
                    Select Event Type
                  </option>
                  {EVENT_TYPE_OPTIONS.map((opt) => (
                    <option key={opt.value} value={opt.value}>
                      {opt.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Right side - Photo preview */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "0px",
                  gap: "10px",
                  width: "280px",
                  height: "250px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <img
                  src={fileUrl}
                  alt="Preview"
                  style={{
                    width: "280px",
                    height: "200px",
                    borderRadius: "10px",
                    objectFit: "cover",
                  }}
                />
                <span
                  style={{
                    width: "280px",
                    height: "20px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "20px",
                    textAlign: "center",
                    color: "#262626",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Photo Preview
                </span>
              </div>
            </div>

            {/* Bottom buttons */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "flex-start",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "62px",
                flex: "none",
                order: 2,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Back button */}
              <div
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "143px",
                  height: "62px",
                  border: "1px solid #D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                  cursor: "pointer",
                }}
                onClick={() => setStep("file")}
              >
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                      fill="#262626"
                    />
                    <path
                      d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                      fill="#262626"
                    />
                  </svg>
                </div>
                <span
                  style={{
                    width: "49px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: "#292D32",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Back
                </span>
              </div>

              {/* Next button */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "140px",
                  height: "62px",
                  background:
                    caption && place && eventType ? "#B31B1E" : "#D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                  cursor:
                    caption && place && eventType ? "pointer" : "not-allowed",
                }}
                onClick={() => {
                  if (caption && place && eventType) {
                    handleDetailsNext();
                  }
                }}
              >
                <span
                  style={{
                    width: "46px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color:
                      caption && place && eventType ? "#FDFCFF" : "#999999",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Next
                </span>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                      fill={
                        caption && place && eventType ? "#FFFFFF" : "#999999"
                      }
                    />
                    <path
                      d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                      fill={
                        caption && place && eventType ? "#FFFFFF" : "#999999"
                      }
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        {step === "face" && !userFaceVerified && (
          <FaceVerificationModal
            open={true}
            onVerified={() => {
              setUserFaceVerified(true);
              setFaceVerified(true);
            }}
            onClose={() => {
              setUserFaceVerified(false);
              setStep("file");
            }}
          />
        )}

        {step === "face" && userFaceVerified && (
          <div className="flex flex-col items-center justify-center min-h-[200px]">
            <span className="text-green-600 font-semibold">
              Face already verified! You can proceed.
            </span>
          </div>
        )}

        {step === "uploading" && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold">Uploading...</span>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
              <div
                className="bg-red-600 h-4 rounded-full transition-all"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span className="text-sm text-gray-700">{progress}%</span>
          </div>
        )}

        {uploadError && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold text-red-600">
              {uploadError}
            </span>
            <button
              className="w-full py-2 rounded-md bg-red-600 text-white font-semibold text-base hover:bg-red-700 transition-colors mt-2"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        )}

        {uploadSuccess && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold text-green-600">
              Photo uploaded successfully!
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PhotoUploadModal;
