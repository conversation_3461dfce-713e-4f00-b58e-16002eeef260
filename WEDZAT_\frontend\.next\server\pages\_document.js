"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n            (function() {\n              // Run immediately to clean up before React hydration\n              function cleanupExtensionAttributes() {\n                // Target common extension attributes\n                const attributesToRemove = [\n                  'bis_skin_checked',\n                  '__processed_',\n                  'data-bis-'\n                ];\n                \n                // Get all elements\n                const allElements = document.querySelectorAll('*');\n                \n                // Remove attributes from each element\n                allElements.forEach(el => {\n                  for (let i = 0; i < el.attributes.length; i++) {\n                    const attr = el.attributes[i];\n                    for (const badAttr of attributesToRemove) {\n                      if (attr.name.includes(badAttr)) {\n                        el.removeAttribute(attr.name);\n                        // Adjust index since we removed an attribute\n                        i--;\n                        break;\n                      }\n                    }\n                  }\n                });\n              }\n              \n              // Run immediately\n              cleanupExtensionAttributes();\n              \n              // Also run after a short delay to catch any late additions\n              setTimeout(cleanupExtensionAttributes, 0);\n              \n              // Run again when DOM changes\n              const observer = new MutationObserver(function(mutations) {\n                cleanupExtensionAttributes();\n              });\n              \n              // Start observing the document\n              observer.observe(document.documentElement, {\n                childList: true,\n                subtree: true,\n                attributes: true\n              });\n            })();\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./pages/_document.tsx")));
module.exports = __webpack_exports__;

})();