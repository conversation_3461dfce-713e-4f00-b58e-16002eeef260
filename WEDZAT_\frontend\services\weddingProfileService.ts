import axios from 'axios';
import { multipartUpload } from '../utils/s3MultipartUpload';

// Use environment variable for base URL
const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test';

// Helper function to get auth headers
function getAuthHeaders() {
  const token = typeof window !== 'undefined' && 
    (localStorage.getItem('token') || localStorage.getItem('jwt_token') || localStorage.getItem('wedzat_token'));
  return token ? { Authorization: `Bearer ${token}` } : {};
}

// Interface for wedding profile data
export interface WeddingProfile {
  user_id: string;
  boy_name: string;
  girl_name: string;
  wedding_hashtag: string;
  boy_pic_url: string;
  girl_pic_url: string;
  updated_at?: string;
}

// Interface for upload URL response
export interface UploadUrlResponse {
  multipart: true;
  upload_id: string;
  s3_upload_id?: string;
  media_id: string;
  main_key: string;
  part_urls: { part_number: number; url: string }[];
  thumbnail_url?: string;
  expires_in_seconds: number;
}

// Wedding profile service
export const weddingProfileService = {
  // Get upload URL for photo
  getUploadUrl: async (file: File, userId: string) => {
    try {
      console.log('Getting upload URL for file:', file.name);
      
      const requestData = {
        media_type: 'photo',
        media_subtype: 'post',
        filename: file.name,
        content_type: file.type,
        file_size: file.size,
        user_id: userId
      };

      const response = await axios.post(`${BASE_URL}/get-upload-url`, requestData, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      console.log('Upload URL response:', response.data);
      return response.data as UploadUrlResponse;
    } catch (error: any) {
      console.error('Error getting upload URL:', error);
      throw error.response?.data || { error: 'Failed to get upload URL' };
    }
  },

  // Complete photo upload process
  uploadPhoto: async (file: File, userId: string, type: 'boy' | 'girl') => {
    try {
      console.log(`Starting photo upload for ${type}:`, file.name);
      
      // Step 1: Get upload URL
      const uploadUrlResponse = await weddingProfileService.getUploadUrl(file, userId);
      
      // Step 2: Upload file using multipart upload
      const parts = await multipartUpload(file, uploadUrlResponse.part_urls);

      // Step 3: Complete multipart upload
      await axios.post(`${BASE_URL}/complete-multipart-upload`, {
        media_id: uploadUrlResponse.media_id,
        upload_id: uploadUrlResponse.upload_id,
        parts: parts
      }, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });
      
      // Step 4: Return the access URL, ensuring it's a CloudFront URL
      const cloudfrontDomain = 'https://d32sx0if8c41dk.cloudfront.net';
      const accessUrl = `https://${cloudfrontDomain}/${uploadUrlResponse.main_key}`;
      return accessUrl;
    } catch (error: any) {
      console.error(`Error uploading ${type} photo:`, error);
      throw error;
    }
  },

  // Save wedding profile
  saveWeddingProfile: async (profileData: WeddingProfile) => {
    try {
      console.log('Saving wedding profile:', profileData);
      
      const response = await axios.post(`${BASE_URL}/save-wedding-profile?user_id=${profileData.user_id}`, {
        girl_name: profileData.girl_name,
        boy_name: profileData.boy_name,
        wedding_hashtag: profileData.wedding_hashtag,
        girl_pic_url: profileData.girl_pic_url,
        boy_pic_url: profileData.boy_pic_url
      }, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      console.log('Wedding profile saved successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error saving wedding profile:', error);
      throw error.response?.data || { error: 'Failed to save wedding profile' };
    }
  },

  // Get wedding profile
  getWeddingProfile: async (userId: string) => {
    try {
      console.log('Fetching wedding profile for user:', userId);
      
      const response = await axios.get(`${BASE_URL}/wedding-profile?user_id=${userId}`, {
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        }
      });

      console.log('Wedding profile fetched successfully:', response.data);
      const profile = response.data as WeddingProfile;

      const s3Domain = 'https://upload-wedzat-1.s3.amazonaws.com';
      const cloudfrontDomain = 'https://d32sx0if8c41dk.cloudfront.net';

      if (profile.boy_pic_url && profile.boy_pic_url.startsWith(s3Domain)) {
        profile.boy_pic_url = profile.boy_pic_url.replace(s3Domain, cloudfrontDomain);
      }
      if (profile.girl_pic_url && profile.girl_pic_url.startsWith(s3Domain)) {
        profile.girl_pic_url = profile.girl_pic_url.replace(s3Domain, cloudfrontDomain);
      }
      
      return profile;
    } catch (error: any) {
      console.error('Error fetching wedding profile:', error);
      if (error.response?.status === 404) {
        // Return default profile if not found
        return {
          user_id: userId,
          boy_name: 'Suba Shivam Rajan',
          girl_name: 'Salini Rajan',
          wedding_hashtag: '#TheShivamRajanSaliniStories',
          boy_pic_url: '/pics/wedcouncler.png',
          girl_pic_url: '/pics/wedcouncler.png'
        };
      }
      throw error.response?.data || { error: 'Failed to fetch wedding profile' };
    }
  }
};

export default weddingProfileService; 