"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import useIsMobile from '../../hooks/useIsMobile';
import { TopNavigation } from '../../components/HomeDashboard/Navigation';

// Types
type Conversation = {
  conversation_id: string;
  name: string;
  is_group: boolean;
  updated_at: string | null;
  unread_count: number;
  last_message: string | null;
  last_message_time: string | null;
  other_user?: {
    user_id: string;
    name: string;
    profile_picture: string | null;
  };
  participants?: Array<{
    user_id: string;
    name: string;
    profile_picture: string | null;
  }>;
};

export default function MobileConversationList() {
  const isMobile = useIsMobile();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        
        if (!token) {
          setError('Authentication token not found');
          setLoading(false);
          return;
        }

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/conversations`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch conversations');
        }

        const data = await response.json();
        
        // Handle different response formats
        let conversationsArray: Conversation[] = [];
        if (data.body && typeof data.body === 'string') {
          conversationsArray = JSON.parse(data.body);
        } else if (Array.isArray(data)) {
          conversationsArray = data;
        } else if (data.data && Array.isArray(data.data)) {
          conversationsArray = data.data;
        }

        setConversations(Array.isArray(conversationsArray) ? conversationsArray : []);
      } catch (error) {
        console.error('Error fetching conversations:', error);
        setError('Failed to load conversations');
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();

    // Set up polling to refresh conversations
    const interval = setInterval(fetchConversations, 15000);
    return () => clearInterval(interval);
  }, []);

  if (!isMobile) {
    return (
      <div className="flex items-center justify-center h-screen text-gray-500">
        This page is for mobile only.
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-white w-full">
      {/* Top Navigation Bar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md border-b border-gray-200">
        <TopNavigation />
      </div>

      {/* Main Content - Account for fixed navbar */}
      <div className="flex flex-col flex-1 bg-gray-100" style={{ marginTop: '84px' }}>
        {/* Messages Header */}
        <div 
          className="p-4 border-b border-gray-300 bg-white"
          style={{
            background: "linear-gradient(179.27deg, #FAE6C4 0.63%, #FFFFFF 149.12%)",
          }}
        >
          <h1 className="text-xl font-semibold text-black">Messages</h1>
        </div>

        {/* Conversations List */}
        <div 
          className="flex-1 overflow-y-auto bg-white"
          style={{ height: "calc(100vh - 84px - 72px)" }} // navbar + header
        >
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-4"></div>
                    <p>Loading conversations...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="p-4 text-center">
                  <div className="bg-red-50 text-red-700 p-4 rounded-lg">
                    <p className="font-medium">Error</p>
                    <p>{error}</p>
                    <button
                      onClick={() => window.location.reload()}
                      className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : conversations.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  <div className="mb-4">
                    <svg
                      className="w-16 h-16 mx-auto text-gray-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                      />
                    </svg>
                  </div>
                  <p>No conversations yet</p>
                  <p className="mt-1 text-sm">
                    Start a new conversation by visiting a profile
                  </p>
                </div>
              ) : (
                conversations.map((conversation) => (
                  <Link 
                    key={conversation.conversation_id} 
                    href={`/mobile-messages/${conversation.conversation_id}`}
                  >
                    <div className="p-4 hover:bg-gray-50 transition border-b border-gray-100 flex items-center space-x-3">
                      {conversation.is_group ? (
                        // Group chat avatar
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-500 font-medium">
                            {conversation.name?.charAt(0) || "G"}
                          </span>
                        </div>
                      ) : conversation.other_user?.profile_picture ? (
                        // User with profile picture
                        <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                          <Image
                            src={conversation.other_user.profile_picture}
                            alt={conversation.name || "User"}
                            fill
                            className="object-cover"
                            sizes="48px"
                          />
                        </div>
                      ) : (
                        // User without profile picture
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-gray-500 font-medium">
                            {(
                              conversation.other_user?.name ||
                              conversation.name ||
                              "?"
                            )
                              .charAt(0)
                              .toUpperCase()}
                          </span>
                        </div>
                      )}

                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-baseline">
                          <h2 className="text-sm font-medium truncate text-black">
                            {conversation.is_group
                              ? conversation.name
                              : conversation.other_user?.name || "User"}
                          </h2>
                          {conversation.last_message_time && (
                            <span className="text-xs text-gray-500">
                              {formatMessageTime(conversation.last_message_time)}
                            </span>
                          )}
                        </div>

                        <p className="text-sm text-gray-500 truncate">
                          {conversation.last_message || "No messages yet"}
                        </p>
                      </div>

                      {conversation.unread_count > 0 && (
                        <span className="bg-green-500 text-white text-xs font-bold rounded-full h-5 min-w-5 px-1.5 flex items-center justify-center">
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </Link>
                ))
              )}
            </div>
          </div>
    </div>
  );
}

// Helper function to format message time
function formatMessageTime(timeString: string): string {
  try {
    const date = new Date(timeString);
    const now = new Date();

    // If message is from today, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    }

    // If message is from this year, show month and day
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }

    // Otherwise show date with year
    return date.toLocaleDateString([], {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  } catch (e) {
    console.error("Error formatting date:", e);
    return timeString;
  }
}