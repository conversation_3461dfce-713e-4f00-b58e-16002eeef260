"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx":
/*!*******************************************************************************!*\
  !*** ./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx ***!
  \*******************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IndianTraditionalTemplate = (param)=>{\n    let { data } = param;\n    var _fonts_heading;\n    // Helper function to handle font values (can be string or array)\n    const getFontFamily = (fontValue, fallback)=>{\n        if (!fontValue) return fallback;\n        if (Array.isArray(fontValue)) return fontValue.join(', ');\n        return fontValue;\n    };\n    const { partner1_name = \"Navneet\", partner2_name = \"Suknya\", wedding_date = \"Monday, 22th Aug 2022\", wedding_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", muhurtam_time = \"7:00 Pm\", reception_date = \"Monday, 23th Aug 2022\", reception_venue = \"Unitech Unihomes Plots Sec -Mu Greater Noida\", reception_time = \"10:00 To 11:00 Am\", couple_photo = \"\", custom_message = \"May your love story be as magical and charming as in fairy tales!\", colors = {\n        primary: '#B31B1E',\n        secondary: '#FFD700',\n        background: '#FFFFFF',\n        text: '#000000',\n        accent: '#FF8C00'\n    }, fonts = {\n        heading: [\n            'Playfair Display',\n            'Georgia',\n            'serif'\n        ],\n        body: [\n            'Roboto',\n            'Arial',\n            'sans-serif'\n        ],\n        decorative: [\n            'Dancing Script',\n            'cursive'\n        ]\n    } } = data;\n    const styles = {\n        container: {\n            maxWidth: '600px',\n            width: '100%',\n            background: colors.background,\n            borderRadius: '20px',\n            overflow: 'hidden',\n            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n            position: 'relative',\n            margin: '0 auto',\n            fontFamily: getFontFamily(fonts.body, 'Roboto, Arial, sans-serif')\n        },\n        headerDecoration: {\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            height: '8px',\n            width: '100%'\n        },\n        marigoldBorder: {\n            background: \"url(\\\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'><circle cx='10' cy='10' r='8' fill='\".concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='30' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='50' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/><circle cx='70' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.accent || '#FF8C00'), \"'/><circle cx='90' cy='10' r='8' fill='\").concat(encodeURIComponent(colors.secondary || '#FFD700'), \"'/></svg>\\\") repeat-x\"),\n            height: '20px',\n            width: '100%'\n        },\n        ganeshSection: {\n            textAlign: 'center',\n            padding: '30px 20px 20px',\n            background: \"linear-gradient(180deg, \".concat(colors.background, \" 0%, #FFF8E7 100%)\")\n        },\n        ganeshSymbol: {\n            fontSize: '48px',\n            color: colors.primary,\n            marginBottom: '10px'\n        },\n        ganeshText: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '24px',\n            color: colors.primary,\n            fontWeight: '700',\n            marginBottom: '20px'\n        },\n        coupleNames: {\n            fontFamily: getFontFamily(fonts.heading, 'Playfair Display, serif'),\n            fontSize: '42px',\n            fontWeight: '700',\n            color: colors.primary,\n            textAlign: 'center',\n            margin: '20px 0',\n            textShadow: '2px 2px 4px rgba(0,0,0,0.1)'\n        },\n        andSymbol: {\n            fontFamily: getFontFamily(fonts.decorative, 'Dancing Script, cursive'),\n            fontSize: '32px',\n            color: colors.secondary,\n            margin: '0 15px'\n        },\n        invitationText: {\n            textAlign: 'center',\n            padding: '20px 30px',\n            fontSize: '18px',\n            color: colors.text,\n            lineHeight: '1.6'\n        },\n        eventDetails: {\n            background: \"linear-gradient(135deg, \".concat(colors.primary, \" 0%, #8B0000 100%)\"),\n            color: 'white',\n            padding: '30px',\n            textAlign: 'center'\n        },\n        eventTitle: {\n            fontFamily: ((_fonts_heading = fonts.heading) === null || _fonts_heading === void 0 ? void 0 : _fonts_heading.join(', ')) || 'Playfair Display, serif',\n            fontSize: '28px',\n            fontWeight: '700',\n            marginBottom: '15px',\n            color: colors.secondary\n        },\n        eventInfo: {\n            margin: '15px 0',\n            fontSize: '16px'\n        },\n        eventDate: {\n            fontSize: '24px',\n            fontWeight: '500',\n            margin: '20px 0',\n            color: colors.secondary\n        },\n        decorativeDivider: {\n            width: '100px',\n            height: '3px',\n            background: \"linear-gradient(90deg, \".concat(colors.secondary, \", \").concat(colors.accent, \", \").concat(colors.secondary, \")\"),\n            margin: '20px auto',\n            borderRadius: '2px'\n        },\n        couplePhoto: {\n            textAlign: 'center',\n            padding: '30px',\n            background: '#FFF8E7'\n        },\n        photoPlaceholder: {\n            width: '200px',\n            height: '200px',\n            borderRadius: '50%',\n            background: \"linear-gradient(135deg, \".concat(colors.secondary, \", \").concat(colors.accent, \")\"),\n            margin: '0 auto 20px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '18px',\n            color: colors.primary,\n            fontWeight: '500',\n            border: \"5px solid \".concat(colors.primary),\n            backgroundImage: couple_photo ? \"url(\".concat(couple_photo, \")\") : 'none',\n            backgroundSize: 'cover',\n            backgroundPosition: 'center'\n        },\n        blessingText: {\n            textAlign: 'center',\n            padding: '20px',\n            fontStyle: 'italic',\n            color: '#666',\n            fontSize: '14px'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: styles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.ganeshSection,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshSymbol,\n                        children: \"\\uD83D\\uDD49️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.ganeshText,\n                        children: \"|| Shree Ganesh Namah ||\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.coupleNames,\n                children: [\n                    partner1_name,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: styles.andSymbol,\n                        children: \"&\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 24\n                    }, undefined),\n                    partner2_name\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.invitationText,\n                children: [\n                    \"We Invite You to Share in our Joy And Request\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 54\n                    }, undefined),\n                    \"Your Presence At the Reception\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.eventDetails,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Muhurtam ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            muhurtam_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: wedding_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            wedding_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.decorativeDivider\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventTitle,\n                        children: \"✦ Reception ✦\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: [\n                            \"Time \",\n                            reception_time\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventInfo,\n                        children: reception_venue\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: styles.eventDate,\n                        children: [\n                            \"On \",\n                            reception_date\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.couplePhoto,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: styles.photoPlaceholder,\n                    children: !couple_photo && \"Couple Photo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.blessingText,\n                children: [\n                    '\"',\n                    custom_message,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.marigoldBorder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: styles.headerDecoration\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\templates\\\\IndianTraditionalTemplate.tsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, undefined);\n};\n_c = IndianTraditionalTemplate;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IndianTraditionalTemplate);\nvar _c;\n$RefreshReg$(_c, \"IndianTraditionalTemplate\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/templates/IndianTraditionalTemplate.tsx\n"));

/***/ })

});