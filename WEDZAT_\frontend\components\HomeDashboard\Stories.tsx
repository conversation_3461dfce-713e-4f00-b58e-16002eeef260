"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import axios from "../../services/axiosConfig";
import { useGlobalUpload } from "../../contexts/GlobalUploadManager";
import UserAvatar from "./UserAvatar";

// Define interface for story items
interface Story {
  content_id: string;
  content_name: string;
  content_url: string;
  content_description?: string;
  thumbnail_url?: string;
  duration?: number;
  created_at: string;
  user_name: string;
  user_id: string;
  user_avatar?: string;
  content_type: 'video' | 'photo';
  is_own_content: boolean;
  viewed?: boolean; // Track if the user has viewed this story
  // New API fields
  followers_count?: number;
  following_count?: number;
  is_following?: boolean;
  views_count?: number;
  is_liked?: boolean;
}

interface ApiResponse {
  stories: Story[];
  next_page: boolean;
  total_count: number;
  current_page: number;
}

const Stories: React.FC = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const { openUploadModal } = useGlobalUpload();

  // Fetch stories from the API
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        // Get token from localStorage - try multiple possible keys
        const token = typeof window !== 'undefined' ?
          (localStorage.getItem('token') ||
            localStorage.getItem('jwt_token') ||
            localStorage.getItem('auth_token')) : null;

        if (!token) {
          console.warn('No authentication token found in any storage key');
          setError('Authentication required');
          setStories(getFallbackStories());
          return;
        }

        const response = await axios.get<ApiResponse>('/stories?page=1&limit=15', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.stories) {
          console.log(`Stories API response for page ${response.data.current_page}:`, response.data);
          console.log(`Loaded ${response.data.stories.length} stories`);

          // Log the content URLs for debugging
          response.data.stories.forEach(story => {
            console.log(`Story ${story.content_id} - Type: ${story.content_type}, URL: ${story.content_url}, Thumbnail: ${story.thumbnail_url}`);
          });

          // Add viewed property to each story (randomly for now)
          const processedStories = response.data.stories.map(story => {
            // For photos, ensure we're using content_url directly
            // For videos, use thumbnail_url if available, otherwise generate one
            const thumbnailUrl = story.content_type === 'photo'
              ? story.content_url
              : (story.thumbnail_url || getDefaultThumbnail(story));

            // Log the processed URLs for debugging
            console.log(`Processed story ${story.content_id}: ${story.content_type} - Original URL: ${story.content_url}, Using: ${thumbnailUrl}`);

            return {
              ...story,
              viewed: Math.random() > 0.5, // Random viewed status for demo
              thumbnail_url: thumbnailUrl,
              // Ensure content_url is also set correctly
              content_url: story.content_url || thumbnailUrl
            };
          });

          // console.log('Processed stories with thumbnails:', processedStories);
          setStories(processedStories);
        } else {
          console.warn('Unexpected API response format:', response.data);
          // Fallback to hardcoded data if API doesn't return expected format
          setStories(getFallbackStories());
        }
      } catch (err) {
        console.error('Error fetching stories:', err);
        setError('Failed to load stories');
        // Fallback to hardcoded data on error
        setStories(getFallbackStories());
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, []);

  // Get default thumbnail based on content type
  const getDefaultThumbnail = (story: Story): string => {
    if (story.content_type === 'video') {
      // Try to extract YouTube thumbnail if it's a YouTube video
      if (story.content_url && story.content_url.includes('youtube')) {
        const videoId = getYoutubeId(story.content_url);
        if (videoId) {
          return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
        }
      }
      return `/pics/video-placeholder.jpg`;
    } else {
      // For photos, use the content_url directly if available
      return story.content_url || `/pics/placeholder.svg`;
    }
  };

  // Process image URL to handle cloudfront URLs properly
  const processImageUrl = (url: string): string => {
    if (!url) return '/pics/placeholder.svg';

    // If it's already a local URL, return as is
    if (url.startsWith('/')) return url;

    // For cloudfront URLs, ensure they're returned as is
    if (url.includes('cloudfront.net') || url.includes('amazonaws.com')) {
      // console.log('Using CDN URL for story:', url);
      return url;
    }

    // Return the URL as is for other external URLs
    return url;
  };

  // Extract YouTube video ID from URL if needed
  const getYoutubeId = (url: string): string => {
    if (!url) return '';

    // If it's already just an ID, return it
    if (url.length < 20 && !url.includes('/')) return url;

    // Try to extract ID from YouTube URL
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : '';
  };

  // Fallback data if API fails
  const getFallbackStories = (): Story[] => [
    {
      content_id: "story1",
      content_name: "Wedding Day",
      content_url: "/pics/wedding-photo-1.jpg",
      content_description: "Our special day",
      thumbnail_url: "/pics/wedding-photo-1.jpg",
      created_at: new Date().toISOString(),
      user_name: "Moments",
      user_id: "fallback_user_1",
      user_avatar: undefined,
      content_type: 'photo',
      is_own_content: true,
      viewed: false
    },
    {
      content_id: "story2",
      content_name: "Venue Decoration",
      content_url: "/pics/wedding-photo-2.jpg",
      content_description: "Beautiful venue setup",
      thumbnail_url: "/pics/wedding-photo-2.jpg",
      created_at: new Date().toISOString(),
      user_name: "itsabigthingwv",
      user_id: "fallback_user_2",
      user_avatar: undefined,
      content_type: 'photo',
      is_own_content: false,
      viewed: false
    },
    {
      content_id: "story3",
      content_name: "Bride Preparation",
      content_url: "/pics/wedding-photo-3.jpg",
      content_description: "Getting ready for the ceremony",
      thumbnail_url: "/pics/wedding-photo-3.jpg",
      created_at: new Date().toISOString(),
      user_name: "opendalale",
      user_id: "fallback_user_3",
      content_type: 'photo',
      is_own_content: false,
      viewed: true
    },
    {
      content_id: "story4",
      content_name: "First Dance",
      content_url: "/pics/wedding-photo-4.jpg",
      content_description: "First dance as a married couple",
      thumbnail_url: "/pics/wedding-photo-4.jpg",
      created_at: new Date().toISOString(),
      user_name: "levrishamilton",
      user_id: "fallback_user_4",
      content_type: 'photo',
      is_own_content: false,
      viewed: false
    },
    {
      content_id: "story5",
      content_name: "Wedding Cake",
      content_url: "/pics/wedding-photo-5.jpg",
      content_description: "Beautiful wedding cake",
      thumbnail_url: "/pics/wedding-photo-5.jpg",
      created_at: new Date().toISOString(),
      user_name: "wahab.xyz",
      user_id: "fallback_user_5",
      content_type: 'photo',
      is_own_content: false,
      viewed: false
    }
  ];

  const handleScroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = direction === "left" ? -280 : 280;
      scrollContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  const handleStoryClick = (storyId: string) => {
    // Find the story index
    const storyIndex = stories.findIndex(s => s.content_id === storyId);
    if (storyIndex !== -1) {
      console.log(`Opening story ${storyId} at index ${storyIndex}`);

      // Navigate to the moments viewer without caching
      window.location.href = `/home/<USER>/viewer`;
    }

    // Mark story as viewed
    setStories(prevStories =>
      prevStories.map(story =>
        story.content_id === storyId
          ? { ...story, viewed: true }
          : story
      )
    );
  };

  return (
    <section className="w-full mb-1 relative">
      <div className="flex items-center mb-4">
        <h2 className="font-inter text-[20px] leading-[18px] font-semibold text-black">
          Moments
        </h2>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="py-4 text-center">
          <div className="inline-block w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin mr-2"></div>
          <span className="text-gray-600">Loading moments...</span>
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="py-4 text-center">
          <div className="text-red-500 mb-2">{error}</div>
        </div>
      )}

      {/* Horizontal scroll container */}
      {!loading && !error && (
        <div className="relative">
          {/* Left scroll button */}
          <button
            onClick={() => handleScroll("left")}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-1 shadow-md hover:bg-white"
            style={{ marginLeft: "4px" }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <div
            ref={scrollContainerRef}
            className="flex overflow-x-auto scrollbar-hide space-x-4 px-2 py-2"
            style={{
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              scrollBehavior: 'smooth',
              WebkitOverflowScrolling: 'touch'
            }}
          >
            {/* "Add Story" button */}
            <div
              className="flex flex-col items-center w-[64px] h-[84px] cursor-pointer"
              onClick={() => openUploadModal('moments')}
            >
              <div className="rounded-full bg-gray-100 p-0.5">
                <div className="bg-white p-0.5 rounded-full">
                  <div className="w-[56px] h-[56px] rounded-[28px] bg-gray-100 flex items-center justify-center">
                    <span className="text-2xl text-black font-light">+</span>
                  </div>
                </div>
              </div>
              <span className="text-xs text-center w-[57px] h-[16px] mt-1 text-black">
                Add Story
              </span>
            </div>

            {/* Story circles */}
            {stories.map((story) => (
              <div
                key={story.content_id}
                className="flex flex-col items-center w-[64px] h-[100px] cursor-pointer"
                onClick={() => handleStoryClick(story.content_id)}
              >
                <div
                  className={`rounded-full p-0.5 ${!story.viewed
                    ? "bg-gradient-to-tr from-yellow-500 via-red-500 to-purple-600"
                    : "border-2 border-gray-300"
                    }`}
                >
                  <div className="bg-white p-0.5 rounded-full">
                    <div className="overflow-hidden w-[56px] h-[56px] rounded-[28px] relative flex items-center justify-center">
                      {/* Display user avatar or first letter of username */}
                      {story.user_avatar ? (
                        <div className="w-full h-full relative">
                          <img
                            src={story.user_avatar}
                            alt={story.user_name || "User"}
                            className="w-full h-full object-cover rounded-full"
                            onError={(e) => {
                              console.error(`Failed to load user avatar: ${story.user_avatar}`);
                              // Hide the image and show fallback
                              const imgElement = e.target as HTMLImageElement;
                              if (imgElement && imgElement.parentElement) {
                                imgElement.parentElement.innerHTML = `
                                  <div class="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-gray-600 font-semibold text-lg">
                                      ${story.user_name ? story.user_name.charAt(0).toUpperCase() : 'U'}
                                    </span>
                                  </div>
                                `;
                              }
                            }}
                          />
                        </div>
                      ) : (
                        <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-gray-600 font-semibold text-lg">
                            {story.user_name ? story.user_name.charAt(0).toUpperCase() : 'U'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <span className="text-xs font-medium text-center w-[57px] h-[16px] mt-1 truncate text-black">
                  {story.user_name.length > 9
                    ? `${story.user_name.slice(0, 9)}...`
                    : story.user_name}
                </span>
                {/* Show stats below username */}
                <span className="text-[10px] text-gray-500 text-center w-[57px] block truncate">
                  {story.views_count ?? 0} views
                  {story.is_liked && <span className="ml-1 text-pink-500">♥</span>}
                </span>
              </div>
            ))}
          </div>

          {/* Right scroll button */}
          <button
            onClick={() => handleScroll("right")}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 rounded-full p-1 shadow-md hover:bg-white"
            style={{ marginRight: "4px" }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>
      )}

      {/* CSS to hide scrollbar across browsers */}
      <style jsx>{`
        /* Hide scrollbar for Chrome, Safari and Opera */
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
};

export default Stories;
