"use client";
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useIsMobile from '../../hooks/useIsMobile';

export default function MessagesPage() {
  const isMobile = useIsMobile();
  const router = useRouter();

  useEffect(() => {
    if (isMobile) {
      router.replace('/mobile-messages');
    }
  }, [isMobile, router]);

  if (isMobile) return null;

  return (
    <div className="flex-1 flex flex-col items-center sm:items-center justify-start sm:justify-center bg-gray-50 pt-8 sm:pt-0">
      <div className="w-full max-w-md px-4 sm:px-0 text-center text-gray-500">
        <svg
          className="w-16 h-16 mx-auto text-gray-300 mb-3 sm:w-20 sm:h-20 sm:mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        <h2 className="text-lg sm:text-xl font-semibold mb-1 sm:mb-2">Welcome to Messages</h2>
        <p className="text-sm sm:text-base max-w-xs sm:max-w-md mx-auto">
          Select a conversation from the list to start chatting or visit a profile to start a new conversation.
        </p>
      </div>
    </div>
  );
}
