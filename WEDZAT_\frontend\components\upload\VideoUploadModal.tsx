import React, { useState, useEffect, useRef } from "react";
import { uploadService } from "../../services/api";
import { uploadToS3 } from "../../utils/directS3Upload";
import { multipartUpload } from "../../utils/s3MultipartUpload";
import CitySearchInput from "../ui/CitySearchInput";
import Webcam from "react-webcam";

interface VideoUploadModalProps {
  open: boolean;
  subtype: "flashes" | "glimpses" | "movies";
  onClose: () => void;
}

const SUBTYPE_LIMITS = {
  flashes: { label: "Flashes", maxSeconds: 90 },
  glimpses: { label: "Glimpses", maxSeconds: 420 },
  movies: { label: "Movies", maxSeconds: 3600 },
};

const BUDGET_OPTIONS = [
  { value: "below_5_lakh", label: "Below 5 Lakh" },
  { value: "5_to_10_lakh", label: "5 to 10 Lakh" },
  { value: "10_to_20_lakh", label: "10 to 20 Lakh" },
  { value: "20_to_30_lakh", label: "20 to 30 Lakh" },
  { value: "30_to_40_lakh", label: "30 to 40 Lakh" },
  { value: "above_40_lakh", label: "Above 40 Lakh" },
];

const EVENT_TYPE_OPTIONS = [
  { value: "pre_wedding_shoot", label: "Pre Wedding Shoot" },
  { value: "save_the_date", label: "Save The Date" },
  { value: "engagement", label: "Engagement" },
  { value: "haldi", label: "Haldi" },
  { value: "mehndi", label: "Mehndi" },
  { value: "sangeet", label: "Sangeet" },
  { value: "bridal_makeup", label: "Bridal Makeup" },
  { value: "groom_prep", label: "Groom Prep" },
  { value: "groom_entry", label: "Groom Entry" },
  { value: "bridal_entry", label: "Bridal Entry" },
  { value: "couple_entry", label: "Couple Entry" },
  { value: "varmala", label: "Varmala" },
  { value: "wedding", label: "Wedding" },
  { value: "reception", label: "Reception" },
  { value: "post_wedding_rituals", label: "Post Wedding Rituals" },
  { value: "pre_wedding_rituals", label: "Pre Wedding Rituals" },
  { value: "wedding_film", label: "Wedding Film" },
  { value: "wedding_reel", label: "Wedding Reel" },
  { value: "wedding_teaser", label: "Wedding Teaser" },
  { value: "couple_dance", label: "Couple Dance" },
  { value: "family_dance", label: "Family Dance" },
  { value: "behind_the_scenes", label: "Behind The Scenes" },
  { value: "venue_decor", label: "Venue Decor" },
  { value: "wedding_venue_tour", label: "Wedding Venue Tour" },
  { value: "invitation_unboxing", label: "Invitation Unboxing" },
  { value: "gift_unboxing", label: "Gift Unboxing" },
  { value: "honeymoon", label: "Honeymoon" },
  { value: "couple_story", label: "Couple Story" },
  { value: "travel", label: "Travel" },
  { value: "wedding_shopping", label: "Wedding Shopping" },
  { value: "bachelor_party", label: "Bachelor Party" },
];

const VENDOR_LABELS = [
  "Venue",
  "Photograph",
  "Make up Artist",
  "Decorations",
  "Caterer",
];

// Helper to map vendorDetails array to backend object
function mapVendorDetailsToBackend(
  vendorArr: { name: string; phone: string }[]
) {
  const vendorObj: Record<string, string> = {};
  const keys = [
    "venue",
    "photographer",
    "makeup_artist",
    "decoration",
    "caterer",
  ];
  keys.forEach((k, i) => {
    if (vendorArr[i]) {
      vendorObj[`${k}_name`] = vendorArr[i].name || "";
      vendorObj[`${k}_contact`] = vendorArr[i].phone || "";
    }
  });
  for (let i = 5; i < vendorArr.length; i++) {
    vendorObj[`additional_vendor_${i - 4}_name`] = vendorArr[i].name || "";
    vendorObj[`additional_vendor_${i - 4}_contact`] = vendorArr[i].phone || "";
  }
  return vendorObj;
}

const WebcamWrapper = React.forwardRef<any, any>((props, ref) =>
  React.createElement(Webcam as any, { ref, ...props })
);

const FaceVerificationModal = ({
  open,
  onVerified,
  onClose,
}: {
  open: boolean;
  onVerified: () => void;
  onClose: () => void;
}) => {
  const webcamRef = React.useRef<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const capture = React.useCallback(async () => {
    if (!webcamRef.current) return;
    setLoading(true);
    setError(null);
    const imageSrc = webcamRef.current.getScreenshot();
    if (!imageSrc) {
      setError("Could not capture image. Please try again.");
      setLoading(false);
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(
        "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/verify-face",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ face_image: imageSrc }),
        }
      );
      if (res.status === 200) {
        onVerified();
      } else {
        const data = await res.json();
        setError(data.error || "Face verification failed. Try again.");
      }
    } catch (e: any) {
      setError("Face verification failed. Try again.");
    } finally {
      setLoading(false);
    }
  }, [onVerified]);
  // face verification modal ui
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
      <div
        style={{
          position: "relative",
          width: "606px",
          height: "663.08px",
          background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
          borderRadius: "20px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "40px",
          gap: "60px",
          isolation: "isolate",
        }}
      >
        {/* Close button */}
        <button
          className="absolute"
          style={{
            top: "20px",
            right: "20px",
            width: "24px",
            height: "24px",
            zIndex: 1,
          }}
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
              stroke="#292D32"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {/* Main content container */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "0px",
            gap: "40px",
            width: "526px",
            height: "583.08px",
            flex: "none",
            order: 0,
            alignSelf: "stretch",
            flexGrow: 0,
            zIndex: 0,
          }}
        >
          {/* Header section */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "20px",
              width: "526px",
              height: "87.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Title row with logo and text */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "20px",
                width: "280px",
                height: "37.08px",
                flex: "none",
                order: 0,
                flexGrow: 0,
              }}
            >
              {/* Heart logo */}
              <div
                style={{
                  width: "47px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="47"
                  height="38"
                  viewBox="0 0 47 38"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                    fill="#B31B1E"
                  />
                  <path
                    d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                    fill="#B31B1E"
                  />
                </svg>
              </div>

              {/* Title and icon */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "10px",
                  width: "213px",
                  height: "27px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <h2
                  style={{
                    width: "179px",
                    height: "27px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 600,
                    fontSize: "22px",
                    lineHeight: "27px",
                    textAlign: "center",
                    color: "#000000",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Face Verification
                </h2>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.1205 14.0698C12.1005 14.0698 12.0705 14.0698 12.0505 14.0698C12.0205 14.0698 11.9805 14.0698 11.9505 14.0698C9.68047 13.9998 7.98047 12.2298 7.98047 10.0498C7.98047 7.82978 9.79047 6.01978 12.0105 6.01978C14.2305 6.01978 16.0405 7.82978 16.0405 10.0498C16.0305 12.2398 14.3205 13.9998 12.1505 14.0698C12.1305 14.0698 12.1305 14.0698 12.1205 14.0698ZM12.0005 7.50978C10.6005 7.50978 9.47047 8.64978 9.47047 10.0398C9.47047 11.4098 10.5405 12.5198 11.9005 12.5698C11.9305 12.5598 12.0305 12.5598 12.1305 12.5698C13.4705 12.4998 14.5205 11.3998 14.5305 10.0398C14.5305 8.64978 13.4005 7.50978 12.0005 7.50978Z"
                      fill="#292D32"
                    />
                    <path
                      d="M11.9998 23.2899C9.30984 23.2899 6.73984 22.2899 4.74984 20.4699C4.56984 20.3099 4.48984 20.0699 4.50984 19.8399C4.63984 18.6499 5.37984 17.5399 6.60984 16.7199C9.58984 14.7399 14.4198 14.7399 17.3898 16.7199C18.6198 17.5499 19.3598 18.6499 19.4898 19.8399C19.5198 20.0799 19.4298 20.3099 19.2498 20.4699C17.2598 22.2899 14.6898 23.2899 11.9998 23.2899ZM6.07984 19.6399C7.73984 21.0299 9.82984 21.7899 11.9998 21.7899C14.1698 21.7899 16.2598 21.0299 17.9198 19.6399C17.7398 19.0299 17.2598 18.4399 16.5498 17.9599C14.0898 16.3199 9.91984 16.3199 7.43984 17.9599C6.72984 18.4399 6.25984 19.0299 6.07984 19.6399Z"
                      fill="#292D32"
                    />
                    <path
                      d="M12 23.2898C6.07 23.2898 1.25 18.4698 1.25 12.5398C1.25 6.6098 6.07 1.78979 12 1.78979C17.93 1.78979 22.75 6.6098 22.75 12.5398C22.75 18.4698 17.93 23.2898 12 23.2898ZM12 3.28979C6.9 3.28979 2.75 7.4398 2.75 12.5398C2.75 17.6398 6.9 21.7898 12 21.7898C17.1 21.7898 21.25 17.6398 21.25 12.5398C21.25 7.4398 17.1 3.28979 12 3.28979Z"
                      fill="#292D32"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Subtitle */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "0px",
                gap: "8px",
                width: "526px",
                height: "30px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 12.8296C8.83 12.8296 6.25 10.2496 6.25 7.07959C6.25 3.90959 8.83 1.32959 12 1.32959C15.17 1.32959 17.75 3.90959 17.75 7.07959C17.75 10.2496 15.17 12.8296 12 12.8296ZM12 2.82959C9.66 2.82959 7.75 4.73959 7.75 7.07959C7.75 9.41959 9.66 11.3296 12 11.3296C14.34 11.3296 16.25 9.41959 16.25 7.07959C16.25 4.73959 14.34 2.82959 12 2.82959Z"
                    fill="#292D32"
                  />
                  <path
                    d="M3.41016 22.8296C3.00016 22.8296 2.66016 22.4896 2.66016 22.0796C2.66016 17.8096 6.85015 14.3296 12.0002 14.3296C13.0102 14.3296 14.0001 14.4596 14.9601 14.7296C15.3601 14.8396 15.5902 15.2496 15.4802 15.6496C15.3702 16.0496 14.9601 16.2796 14.5602 16.1696C13.7402 15.9396 12.8802 15.8296 12.0002 15.8296C7.68015 15.8296 4.16016 18.6296 4.16016 22.0796C4.16016 22.4896 3.82016 22.8296 3.41016 22.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M18 22.8296C16.34 22.8296 14.78 21.9496 13.94 20.5196C13.49 19.7996 13.25 18.9496 13.25 18.0796C13.25 16.6196 13.9 15.2696 15.03 14.3696C15.87 13.6996 16.93 13.3296 18 13.3296C20.62 13.3296 22.75 15.4596 22.75 18.0796C22.75 18.9496 22.51 19.7996 22.06 20.5296C21.81 20.9496 21.49 21.3296 21.11 21.6496C20.28 22.4096 19.17 22.8296 18 22.8296ZM18 14.8296C17.26 14.8296 16.56 15.0796 15.97 15.5496C15.2 16.1596 14.75 17.0896 14.75 18.0796C14.75 18.6696 14.91 19.2496 15.22 19.7496C15.8 20.7296 16.87 21.3296 18 21.3296C18.79 21.3296 19.55 21.0396 20.13 20.5196C20.39 20.2996 20.61 20.0396 20.77 19.7596C21.09 19.2496 21.25 18.6696 21.25 18.0796C21.25 16.2896 19.79 14.8296 18 14.8296Z"
                    fill="#292D32"
                  />
                  <path
                    d="M17.4299 19.8196C17.2399 19.8196 17.0499 19.7497 16.8999 19.5997L15.9099 18.6097C15.6199 18.3197 15.6199 17.8396 15.9099 17.5496C16.1999 17.2596 16.6799 17.2596 16.9699 17.5496L17.4499 18.0297L19.0499 16.5496C19.3499 16.2696 19.8299 16.2897 20.1099 16.5897C20.3899 16.8897 20.3699 17.3697 20.0699 17.6497L17.9399 19.6196C17.7899 19.7496 17.6099 19.8196 17.4299 19.8196Z"
                    fill="#292D32"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "157px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Verify your face ID
              </span>
            </div>
          </div>

          {/* Camera section */}
          <div
            style={{
              boxSizing: "border-box",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              padding: "20px 40px",
              gap: "8px",
              width: "526px",
              height: "354px",
              border: "1px solid #D9D9D9",
              borderRadius: "20px",
              flex: "none",
              order: 1,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Camera circle */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                padding: "90px 40px",
                gap: "8px",
                width: "204px",
                height: "204px",
                border: "1px solid #D9D9D9",
                borderRadius: "130px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                position: "relative",
                overflow: "hidden",
              }}
            >
              {webcamRef.current ? (
                <WebcamWrapper
                  audio={false}
                  ref={webcamRef}
                  screenshotFormat="image/jpeg"
                  width={204}
                  height={204}
                  videoConstraints={{ facingMode: "user" }}
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    borderRadius: "130px",
                  }}
                />
              ) : (
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M9 10C10.1046 10 11 9.10457 11 8C11 6.89543 10.1046 6 9 6C7.89543 6 7 6.89543 7 8C7 9.10457 7.89543 10 9 10Z"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Open Camera text */}
            <span
              style={{
                width: "92px",
                height: "17px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "17px",
                color: "#262626",
                flex: "none",
                order: 1,
                flexGrow: 0,
              }}
            >
              Open Camera
            </span>

            {/* Description text */}
            <p
              style={{
                width: "372px",
                height: "56px",
                fontFamily: "Inter",
                fontStyle: "normal",
                fontWeight: 300,
                fontSize: "18px",
                lineHeight: "28px",
                textAlign: "center",
                color: "#000000",
                flex: "none",
                order: 1,
                flexGrow: 0,
                marginTop: "16px",
              }}
            >
              This is a one-time face verification process. It won't appear
              again next time.
            </p>

            {error && (
              <div
                style={{
                  color: "#B31B1E",
                  fontSize: "14px",
                  textAlign: "center",
                  marginTop: "8px",
                }}
              >
                {error}
              </div>
            )}
          </div>

          {/* Bottom buttons */}
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "526px",
              height: "62px",
              flex: "none",
              order: 2,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Back button */}
            <div
              style={{
                boxSizing: "border-box",
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "143px",
                height: "62px",
                border: "1px solid #D9D9D9",
                borderRadius: "10px",
                flex: "none",
                order: 0,
                flexGrow: 0,
                cursor: "pointer",
              }}
              onClick={onClose}
            >
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  transform: "rotate(-180deg)",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ transform: "rotate(-180deg)" }}
                >
                  <path
                    d="M11.47 21.58C11.01 21.58 10.57 21.39 10.25 21.07L3.18 14C2.53 13.35 2.53 12.31 3.18 11.66L10.25 4.59C10.9 3.94 11.94 3.94 12.59 4.59C13.24 5.24 13.24 6.28 12.59 6.93L7.66 11.86L20.5 11.86C21.33 11.86 22 12.53 22 13.36C22 14.19 21.33 14.86 20.5 14.86L7.66 14.86L12.59 19.79C13.24 20.44 13.24 21.48 12.59 22.13C12.27 22.45 11.83 22.64 11.47 21.58Z"
                    fill="#262626"
                  />
                  <path
                    d="M11.46 12.17L20.5 12.17C20.91 12.17 21.25 12.53 21.25 12.94C21.25 13.35 20.91 13.71 20.5 13.71L11.46 13.71C11.05 13.71 10.71 13.35 10.71 12.94C10.71 12.53 11.05 12.17 11.46 12.17Z"
                    fill="#262626"
                  />
                </svg>
              </div>
              <span
                style={{
                  width: "49px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#292D32",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                Back
              </span>
            </div>

            {/* Upload button */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "16px 30px",
                gap: "10px",
                margin: "0 auto",
                width: "164px",
                height: "62px",
                background: loading ? "#999999" : "#B31B1E",
                borderRadius: "10px",
                flex: "none",
                order: 1,
                flexGrow: 0,
                cursor: loading ? "not-allowed" : "pointer",
              }}
              onClick={loading ? undefined : capture}
            >
              <span
                style={{
                  width: "70px",
                  height: "24px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 700,
                  fontSize: "20px",
                  lineHeight: "24px",
                  textTransform: "capitalize",
                  color: "#FDFCFF",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {loading ? "Verifying..." : "Upload"}
              </span>
              <div
                style={{
                  width: "24px",
                  height: "24px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.43 18.9C14.24 18.9 14.05 18.83 13.9 18.68C13.61 18.39 13.61 17.91 13.9 17.62L19.44 12.08L13.9 6.54C13.61 6.25 13.61 5.77 13.9 5.48C14.19 5.19 14.67 5.19 14.96 5.48L21.03 11.55C21.32 11.84 21.32 12.32 21.03 12.61L14.96 18.68C14.81 18.83 14.62 18.9 14.43 18.9Z"
                    fill="#FFFFFF"
                  />
                  <path
                    d="M20.33 12.83H3.5C3.09 12.83 2.75 12.49 2.75 12.08C2.75 11.67 3.09 11.33 3.5 11.33H20.33C20.74 11.33 21.08 11.67 21.08 12.08C21.08 12.49 20.74 12.83 20.33 12.83Z"
                    fill="#FFFFFF"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const VideoUploadModal: React.FC<VideoUploadModalProps> = ({
  open,
  subtype,
  onClose,
}) => {
  const [step, setStep] = useState<
    | "category"
    | "file"
    | "duration-check"
    | "thumbnail"
    | "face"
    | "uploading"
    | "error"
    | "success"
  >("category");
  const [videoCategory, setVideoCategory] = useState<
    "my_wedding" | "wedding_vlog" | null
  >(null);
  const [file, setFile] = useState<File | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [duration, setDuration] = useState<number | null>(null);
  const [durationError, setDurationError] = useState<string | null>(null);
  const [suggestedSubtype, setSuggestedSubtype] = useState<
    "flashes" | "glimpses" | "movies" | null
  >(null);
  const [autoThumbnailUrl, setAutoThumbnailUrl] = useState<string | null>(null);
  const [customThumbnail, setCustomThumbnail] = useState<File | null>(null);
  const [customThumbnailUrl, setCustomThumbnailUrl] = useState<string | null>(
    null
  );
  const [selectedThumbnail, setSelectedThumbnail] = useState<"auto" | "custom">(
    "auto"
  );
  const [faceVerified, setFaceVerified] = useState<boolean | null>(null);
  const [userFaceVerified, setUserFaceVerified] = useState<boolean | null>(
    null
  );
  const [checkingFace, setCheckingFace] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [details, setDetails] = useState({
    caption: "",
    partner: "",
    place: "",
    budget: "",
    event_type: "",
    wedding_style: "",
  });
  const [vendorDetails, setVendorDetails] = useState([
    { name: "", phone: "" }, // Venue
    { name: "", phone: "" }, // Photograph
    { name: "", phone: "" }, // Make up Artist
    { name: "", phone: "" }, // Decorations
    { name: "", phone: "" }, // Caterer
  ]);
  const [additionalVendors, setAdditionalVendors] = useState<
    { name: string; phone: string }[]
  >([]);
  const [showDetails, setShowDetails] = useState(false);
  const [showVendors, setShowVendors] = useState(false);
  const [phoneErrors, setPhoneErrors] = useState<{ [key: string]: boolean }>(
    {}
  );

  useEffect(() => {
    if (!open) {
      setStep("category");
      setVideoCategory(null);
      setFile(null);
      setFileUrl(null);
      setDuration(null);
      setDurationError(null);
      setSuggestedSubtype(null);
      setAutoThumbnailUrl(null);
      setCustomThumbnail(null);
      setCustomThumbnailUrl(null);
      setSelectedThumbnail("auto");
      setFaceVerified(null);
      setUserFaceVerified(null);
      setCheckingFace(false);
      setUploading(false);
      setProgress(0);
      setUploadError(null);
      setUploadSuccess(false);
      setDetails({
        caption: "",
        partner: "",
        place: "",
        budget: "",
        event_type: "",
        wedding_style: "",
      });
      setVendorDetails([
        { name: "", phone: "" },
        { name: "", phone: "" },
        { name: "", phone: "" },
        { name: "", phone: "" },
        { name: "", phone: "" },
      ]);
      setAdditionalVendors([]);
      setShowDetails(false);
      setShowVendors(false);
    }
  }, [open]);

  // Step 1: Video category selection
  const handleCategorySelect = (cat: "my_wedding" | "wedding_vlog") => {
    setVideoCategory(cat);
    setStep("file");
  };

  // Step 2: File selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setFile(f);
    setFileUrl(URL.createObjectURL(f));
    setStep("duration-check");
  };

  // Step 3: Duration check
  useEffect(() => {
    if (step === "duration-check" && fileUrl) {
      const video = document.createElement("video");
      video.src = fileUrl;
      video.preload = "metadata";
      video.onloadedmetadata = () => {
        const dur = video.duration;
        setDuration(dur);
        const limit = SUBTYPE_LIMITS[subtype].maxSeconds;
        if (dur <= limit) {
          setStep("thumbnail");
        } else {
          let suggested: "flashes" | "glimpses" | "movies" = "movies";
          if (dur <= SUBTYPE_LIMITS.flashes.maxSeconds) suggested = "flashes";
          else if (dur <= SUBTYPE_LIMITS.glimpses.maxSeconds)
            suggested = "glimpses";
          setSuggestedSubtype(suggested);
          setDurationError(
            `Selected video is too long for ${SUBTYPE_LIMITS[subtype].label} (max ${limit} sec).`
          );
        }
      };

      return () => {
        video.src = "";
      };
    }
  }, [step, fileUrl, subtype]);

  // Step 4: Thumbnail extraction
  useEffect(() => {
    if (step === "thumbnail" && fileUrl) {
      const video = document.createElement("video");
      video.src = fileUrl;
      video.crossOrigin = "anonymous";
      // Try to use 5s, fallback to 3s, fallback to 0.1s if video is too short
      video.muted = true;
      video.playsInline = true;
      video.addEventListener(
        "loadedmetadata",
        () => {
          let seekTime = 5;
          if (video.duration < 5) {
            seekTime = video.duration > 3 ? 3 : 0.1;
          }
          video.currentTime = seekTime;
        },
        { once: true }
      );
      video.addEventListener(
        "seeked",
        () => {
          const canvas = document.createElement("canvas");
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          const ctx = canvas.getContext("2d");
          if (ctx) {
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            const dataUrl = canvas.toDataURL("image/jpeg");
            setAutoThumbnailUrl(dataUrl);
          }
        },
        { once: true }
      );
    }
  }, [step, fileUrl]);

  // Step 5: Face verification check
  useEffect(() => {
    if (step === "face" && userFaceVerified === null) {
      fetch(
        "https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub/user",
        {
          headers: { Authorization: `Bearer ${localStorage.getItem("token")}` },
        }
      )
        .then((res) => res.json())
        .then((data) => setUserFaceVerified(!!data.face_verified))
        .catch(() => setUserFaceVerified(false));
    }
  }, [step, userFaceVerified]);

  const handleFaceVerification = () => {
    setFaceVerified(true);
  };

  // Step 6: Upload logic
  const startUpload = async () => {
    if (!file || !videoCategory) return;
    setUploading(true);
    setProgress(5);
    setUploadError(null);
    setUploadSuccess(false);
    try {
      // 1. Get presigned URL
      const presigned = await uploadService.getPresignedUrl({
        media_type: "video",
        media_subtype:
          subtype === "flashes"
            ? "flash"
            : subtype === "glimpses"
            ? "glimpse"
            : "movie",
        video_category: (videoCategory || "") as string,
        filename: file.name,
        content_type: file.type,
        file_size: file.size,
      });
      setProgress(20);
      // 2. Upload video using multipart upload
      const parts = await multipartUpload(file, presigned.part_urls, (progress) => {
        setProgress(20 + Math.round(progress.percentage * 0.5));
      });

      // Complete multipart upload
      await uploadService.completeMultipartUpload({
        media_id: presigned.media_id,
        upload_id: presigned.upload_id,
        parts: parts
      });

      // 3. Upload thumbnail
      let thumbFile: File | null = null;
      if (selectedThumbnail === "auto" && autoThumbnailUrl) {
        const arr = autoThumbnailUrl.split(","),
          mimeMatch = arr[0].match(/:(.*?);/),
          mime = mimeMatch ? mimeMatch[1] : null,
          bstr = atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        for (let i = 0; i < n; i++) u8arr[i] = bstr.charCodeAt(i);
        if (mime) {
          thumbFile = new File([u8arr], "thumbnail.jpg", { type: mime });
        }
      } else if (selectedThumbnail === "custom" && customThumbnail) {
        thumbFile = customThumbnail;
      }
      if (thumbFile && presigned.thumbnail_url) {
        await uploadToS3(presigned.thumbnail_url, thumbFile, (p) =>
          setProgress(70 + Math.round(p * 0.2))
        );
      }
      setProgress(90);
      // 4. Complete upload
      const completeReq: any = {
        media_id: presigned.media_id,
        media_type: "video" as "video",
        media_subtype:
          subtype === "flashes"
            ? "flash"
            : subtype === "glimpses"
            ? "glimpse"
            : "movie",
        video_category: (videoCategory || "") as string,
        caption: String(details.caption || ""),
        partner: String(details.partner || ""),
        place: String(details.place || ""),
        event_type: String(details.event_type || ""),
        wedding_style: String(details.wedding_style || ""),
        budget: String(details.budget || ""),
        vendor_details: mapVendorDetailsToBackend(
          vendorDetails.concat(additionalVendors)
        ),
        // Do NOT send title or details
      };
      await uploadService.completeUpload(completeReq);
      setProgress(100);
      setUploadSuccess(true);
      setTimeout(() => {
        setUploading(false);
        onClose();
      }, 1200);
    } catch (err: any) {
      setUploading(false);
      setUploadError(err?.error || err?.message || "Upload failed.");
      setStep("error");
    }
  };

  useEffect(() => {
    if (step === "face" && userFaceVerified) {
      setStep("uploading");
      startUpload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [step, userFaceVerified]);

  const handleVendorNext = async () => {
    setShowVendors(false);
    setShowDetails(false);
    setStep("face");
    setUserFaceVerified(null);
  };

  // Add a helper to reset all upload state (like onClose, but also resets step)
  const resetUploadState = () => {
    setStep("category");
    setVideoCategory(null);
    setFile(null);
    setFileUrl(null);
    setDuration(null);
    setDurationError(null);
    setSuggestedSubtype(null);
    setAutoThumbnailUrl(null);
    setCustomThumbnail(null);
    setCustomThumbnailUrl(null);
    setSelectedThumbnail("auto");
    setUserFaceVerified(null);
    setCheckingFace(false);
    setUploading(false);
    setProgress(0);
    setUploadError(null);
    setUploadSuccess(false);
    setDetails({
      caption: "",
      partner: "",
      place: "",
      budget: "",
      event_type: "",
      wedding_style: "",
    });
    setVendorDetails([
      { name: "", phone: "" },
      { name: "", phone: "" },
      { name: "", phone: "" },
      { name: "", phone: "" },
      { name: "", phone: "" },
    ]);
    setAdditionalVendors([]);
    setShowDetails(false);
    setShowVendors(false);
  };

  if (!open) return null;

  // UI rendering for each step
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
      <div
        className="relative"
        style={{
          width: "700px",
          height: "559.08px",
          background: "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
          borderRadius: "20px",
          padding: "40px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "60px",
          isolation: "isolate",
        }}
      >
        {/* Close button */}
        <button
          className="absolute text-gray-600 hover:text-gray-800 text-2xl font-light"
          style={{
            top: "20px",
            right: "20px",
            width: "24px",
            height: "24px",
          }}
          onClick={onClose}
          aria-label="Close"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
              stroke="#292D32"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        {step === "category" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "620px",
              height: "479.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Header */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "0px",
                gap: "20px",
                width: "620px",
                height: "87.08px",
                flex: "none",
                order: 0,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "222px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Heart logo */}
                <div
                  style={{
                    width: "47px",
                    height: "37.08px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title + icon */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "155px",
                    height: "27px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <h2
                    style={{
                      width: "125px",
                      height: "27px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 600,
                      fontSize: "22px",
                      lineHeight: "27px",
                      textAlign: "center",
                      color: "#000000",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    Create New
                  </h2>
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="20"
                      height="21"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C16.32 18.248 20.25 14.6508 20.25 10.2272C20.25 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#262626"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#262626"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#262626"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Subtitle */}
              <p
                style={{
                  width: "620px",
                  height: "30px",
                  fontFamily: "Inter",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "18px",
                  lineHeight: "30px",
                  color: "#000000",
                  flex: "none",
                  order: 1,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                Choose Video Category
              </p>
            </div>

            {/* Content area */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "250px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Left side - Category options */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                  padding: "0px",
                  gap: "40px",
                  width: "300px",
                  height: "154px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                    padding: "0px",
                    gap: "30px",
                    width: "300px",
                    height: "154px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  {/* My Wedding Videos option */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "flex-start",
                      padding: "0px",
                      width: "300px",
                      height: "62px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    <div
                      style={{
                        boxSizing: "border-box",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "22px 20px",
                        gap: "10px",
                        width: "300px",
                        height: "62px",
                        border:
                          videoCategory === "my_wedding"
                            ? "1px solid #4285F4"
                            : "1px solid #D9D9D9",
                        borderRadius: "10px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                        cursor: "pointer",
                      }}
                      onClick={() => setVideoCategory("my_wedding")}
                    >
                      {/* Radio button */}
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                          position: "relative",
                        }}
                      >
                        <div
                          style={{
                            boxSizing: "border-box",
                            position: "absolute",
                            left: "0%",
                            right: "0%",
                            top: "0%",
                            bottom: "0%",
                            border:
                              videoCategory === "my_wedding"
                                ? "1px solid #4285F4"
                                : "1px solid #030405",
                            borderRadius: "50%",
                            background:
                              videoCategory === "my_wedding"
                                ? "#4285F4"
                                : "transparent",
                          }}
                        />
                        {videoCategory === "my_wedding" && (
                          <div
                            style={{
                              position: "absolute",
                              left: "50%",
                              top: "50%",
                              transform: "translate(-50%, -50%)",
                              width: "6px",
                              height: "6px",
                              background: "#FFFFFF",
                              borderRadius: "50%",
                            }}
                          />
                        )}
                      </div>
                      <span
                        style={{
                          width: "193px",
                          height: "18px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "20px",
                          lineHeight: "18px",
                          display: "flex",
                          alignItems: "center",
                          color: "#000000",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        My Wedding Videos
                      </span>
                    </div>
                  </div>

                  {/* Wedding Vlogs option */}
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "flex-start",
                      padding: "0px",
                      width: "300px",
                      height: "62px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <div
                      style={{
                        boxSizing: "border-box",
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                        padding: "22px 20px",
                        gap: "10px",
                        width: "300px",
                        height: "62px",
                        border:
                          videoCategory === "wedding_vlog"
                            ? "1px solid #4285F4"
                            : "1px solid #D9D9D9",
                        borderRadius: "10px",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                        cursor: "pointer",
                      }}
                      onClick={() => setVideoCategory("wedding_vlog")}
                    >
                      {/* Radio button */}
                      <div
                        style={{
                          width: "16px",
                          height: "16px",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                          position: "relative",
                        }}
                      >
                        <div
                          style={{
                            boxSizing: "border-box",
                            position: "absolute",
                            left: "0%",
                            right: "0%",
                            top: "0%",
                            bottom: "0%",
                            border:
                              videoCategory === "wedding_vlog"
                                ? "1px solid #4285F4"
                                : "1px solid #030405",
                            borderRadius: "50%",
                            background:
                              videoCategory === "wedding_vlog"
                                ? "#4285F4"
                                : "transparent",
                          }}
                        />
                        {videoCategory === "wedding_vlog" && (
                          <div
                            style={{
                              position: "absolute",
                              left: "50%",
                              top: "50%",
                              transform: "translate(-50%, -50%)",
                              width: "6px",
                              height: "6px",
                              background: "#FFFFFF",
                              borderRadius: "50%",
                            }}
                          />
                        )}
                      </div>
                      <span
                        style={{
                          width: "147px",
                          height: "18px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 600,
                          fontSize: "20px",
                          lineHeight: "18px",
                          display: "flex",
                          alignItems: "center",
                          color: "#000000",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        Wedding Vlogs
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Upload sections */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "flex-start",
                  padding: "0px",
                  gap: "30px",
                  width: "280px",
                  height: "250px",
                  flex: "none",
                  order: 1,
                  flexGrow: 1,
                }}
              >
                {/* Upload Videos section */}
                <div
                  style={{
                    boxSizing: "border-box",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "20px 40px",
                    gap: "8px",
                    width: "280px",
                    height: "110px",
                    background: "#000000",
                    border: "1px solid #FFFFFF",
                    borderRadius: "20px",
                    flex: "none",
                    order: 0,
                    alignSelf: "stretch",
                    flexGrow: 0,
                    cursor: videoCategory ? "pointer" : "not-allowed",
                    opacity: videoCategory ? 1 : 0.5,
                  }}
                  onClick={() => {
                    if (videoCategory) {
                      const input = document.createElement("input");
                      input.type = "file";
                      input.accept = "video/*";
                      input.onchange = handleFileChange;
                      input.click();
                    }
                  }}
                >
                  <div
                    style={{
                      width: "24px",
                      height: "24px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </div>
                  <span
                    style={{
                      width: "98px",
                      height: "17px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 500,
                      fontSize: "14px",
                      lineHeight: "17px",
                      color: "#FFFFFF",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    Upload Videos
                  </span>
                </div>
              </div>
            </div>

            {/* Bottom buttons */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "flex-start",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "62px",
                flex: "none",
                order: 2,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Back button */}
              <div
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "143px",
                  height: "62px",
                  border: "1px solid #D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                  cursor: "pointer",
                }}
                onClick={onClose}
              >
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                      fill="#262626"
                    />
                    <path
                      d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                      fill="#262626"
                    />
                  </svg>
                </div>
                <span
                  style={{
                    width: "49px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: "#292D32",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Back
                </span>
              </div>

              {/* Next button */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "140px",
                  height: "62px",
                  background: videoCategory ? "#B31B1E" : "#D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                  cursor: videoCategory ? "pointer" : "not-allowed",
                }}
                onClick={() => {
                  if (videoCategory) {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = "video/*";
                    input.onchange = handleFileChange;
                    input.click();
                  }
                }}
              >
                <span
                  style={{
                    width: "46px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: videoCategory ? "#FDFCFF" : "#999999",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Next
                </span>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                      fill="white"
                    />
                    <path
                      d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                      fill="white"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Keep all other existing steps unchanged */}
        {step === "file" && <></>}
        {step === "duration-check" && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "620px",
              height: "479.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Header */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "0px",
                gap: "20px",
                width: "620px",
                height: "87.08px",
                flex: "none",
                order: 0,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "222px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Heart logo */}
                <div
                  style={{
                    width: "47px",
                    height: "37.08px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "155px",
                    height: "27px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <h2
                    style={{
                      width: "155px",
                      height: "27px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 600,
                      fontSize: "22px",
                      lineHeight: "27px",
                      textAlign: "center",
                      color: "#000000",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    Duration Check
                  </h2>
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Error section */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                width: "100%",
                height: "200px",
                gap: "20px",
              }}
            >
              <div
                style={{
                  fontFamily: "Inter",
                  fontWeight: 600,
                  fontSize: "18px",
                  color: "#B31B1E",
                  textAlign: "center",
                }}
              >
                {durationError}
              </div>
              <div
                style={{
                  fontFamily: "Inter",
                  fontWeight: 400,
                  fontSize: "22px",
                  color: "#000000",
                  textAlign: "center",
                }}
              >
                Suggested:{" "}
                <span style={{ fontWeight: 600 }}>{suggestedSubtype}</span>
              </div>
            </div>

            {/* Buttons */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
                marginTop: "auto",
              }}
            >
              <div
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  border: "1px solid #D9D9D9",
                  borderRadius: "10px",
                  cursor: "pointer",
                }}
                onClick={onClose}
              >
                <span
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 700,
                    fontSize: "20px",
                    color: "#292D32",
                  }}
                >
                  Cancel
                </span>
              </div>

              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  background: "#B31B1E",
                  borderRadius: "10px",
                  cursor: "pointer",
                }}
                onClick={() => {
                  setStep("thumbnail");
                }}
              >
                <span
                  style={{
                    fontFamily: "Inter",
                    fontWeight: 700,
                    fontSize: "20px",
                    color: "#FFFFFF",
                  }}
                >
                  Continue as {suggestedSubtype}
                </span>
              </div>
            </div>
          </div>
        )}

        {step === "thumbnail" && fileUrl && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "0px",
              gap: "40px",
              width: "620px",
              height: "479.08px",
              flex: "none",
              order: 0,
              alignSelf: "stretch",
              flexGrow: 0,
            }}
          >
            {/* Header */}
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "0px",
                gap: "20px",
                width: "620px",
                height: "87.08px",
                flex: "none",
                order: 0,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "222px",
                  height: "37.08px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Heart logo */}
                <div
                  style={{
                    width: "47px",
                    height: "37.08px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title + icon */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "155px",
                    height: "27px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <h2
                    style={{
                      width: "125px",
                      height: "27px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight: 600,
                      fontSize: "22px",
                      lineHeight: "27px",
                      textAlign: "center",
                      color: "#000000",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                    }}
                  >
                    Thumbnail
                  </h2>
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    <svg
                      width="20"
                      height="21"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.5 18.2047H12.5C16.25 18.2047 17.5 16.9547 17.5 13.2047V8.20469C17.5 4.45469 16.25 3.20469 12.5 3.20469H7.5C3.75 3.20469 2.5 4.45469 2.5 8.20469V13.2047C2.5 16.9547 3.75 18.2047 7.5 18.2047Z"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M7.5 8.95469C8.32843 8.95469 9 8.28312 9 7.45469C9 6.62626 8.32843 5.95469 7.5 5.95469C6.67157 5.95469 6 6.62626 6 7.45469C6 8.28312 6.67157 8.95469 7.5 8.95469Z"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M2.67004 15.4547L6.10004 12.8047C6.59004 12.4047 7.35004 12.4547 7.78004 12.9047L8.02004 13.1547C8.50004 13.6547 9.35004 13.6547 9.83004 13.1547L12.55 10.4047C13.03 9.90469 13.88 9.90469 14.36 10.4047L17.5 13.5547"
                        stroke="#262626"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Subtitle */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  padding: "0px",
                  gap: "8px",
                  width: "620px",
                  height: "30px",
                  flex: "none",
                  order: 1,
                  alignSelf: "stretch",
                  flexGrow: 0,
                }}
              >
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 22.8296H15C20 22.8296 22 20.8296 22 15.8296V9.82959C22 4.82959 20 2.82959 15 2.82959H9C4 2.82959 2 4.82959 2 9.82959V15.8296C2 20.8296 4 22.8296 9 22.8296Z"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M9 10.8296C10.1046 10.8296 11 9.93416 11 8.82959C11 7.72502 10.1046 6.82959 9 6.82959C7.89543 6.82959 7 7.72502 7 8.82959C7 9.93416 7.89543 10.8296 9 10.8296Z"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M2.67004 18.9501L7.60004 15.6401C8.39004 15.1101 9.53004 15.1701 10.24 15.7801L10.57 16.0701C11.35 16.7401 12.61 16.7401 13.39 16.0701L17.55 12.5001C18.33 11.8301 19.59 11.8301 20.37 12.5001L22 13.9001"
                      stroke="#292D32"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <span
                  style={{
                    width: "200px",
                    height: "30px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 400,
                    fontSize: "18px",
                    lineHeight: "30px",
                    color: "#000000",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Select a thumbnail
                </span>
              </div>
            </div>

            {/* Content area */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "250px",
                flex: "none",
                order: 1,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Left side - Thumbnail options */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  padding: "0px",
                  gap: "20px",
                  width: "300px",
                  height: "250px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                }}
              >
                {/* Auto-generated thumbnail */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "140px",
                    height: "110px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                    cursor: autoThumbnailUrl ? "pointer" : "default",
                  }}
                  onClick={() =>
                    autoThumbnailUrl && setSelectedThumbnail("auto")
                  }
                >
                  <div
                    style={{
                      boxSizing: "border-box",
                      width: "140px",
                      height: "80px",
                      border:
                        selectedThumbnail === "auto" && autoThumbnailUrl
                          ? "2px solid #B31B1E"
                          : "1px solid #D9D9D9",
                      borderRadius: "10px",
                      flex: "none",
                      order: 0,
                      flexGrow: 0,
                      overflow: "hidden",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      background: "#F5F5F5",
                    }}
                  >
                    {autoThumbnailUrl ? (
                      <img
                        src={autoThumbnailUrl}
                        alt="Auto Thumbnail"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                    ) : (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: "100%",
                          height: "100%",
                          fontSize: "12px",
                          color: "#999999",
                        }}
                      >
                        Generating...
                      </div>
                    )}
                  </div>
                  <span
                    style={{
                      width: "140px",
                      height: "20px",
                      fontFamily: "Inter",
                      fontStyle: "normal",
                      fontWeight:
                        selectedThumbnail === "auto" && autoThumbnailUrl
                          ? 600
                          : 400,
                      fontSize: "12px",
                      lineHeight: "20px",
                      textAlign: "center",
                      color:
                        selectedThumbnail === "auto" && autoThumbnailUrl
                          ? "#B31B1E"
                          : "#000000",
                      flex: "none",
                      order: 1,
                      flexGrow: 0,
                    }}
                  >
                    Auto Generated
                  </span>
                </div>

                {/* Custom thumbnail upload */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    padding: "0px",
                    gap: "10px",
                    width: "140px",
                    height: "110px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  {customThumbnailUrl ? (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        gap: "10px",
                        cursor: "pointer",
                      }}
                      onClick={() => setSelectedThumbnail("custom")}
                    >
                      <div
                        style={{
                          boxSizing: "border-box",
                          width: "140px",
                          height: "80px",
                          border:
                            selectedThumbnail === "custom"
                              ? "2px solid #B31B1E"
                              : "1px solid #D9D9D9",
                          borderRadius: "10px",
                          overflow: "hidden",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <img
                          src={customThumbnailUrl}
                          alt="Custom Thumbnail"
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </div>
                      <span
                        style={{
                          width: "140px",
                          height: "20px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight:
                            selectedThumbnail === "custom" ? 600 : 400,
                          fontSize: "12px",
                          lineHeight: "20px",
                          textAlign: "center",
                          color:
                            selectedThumbnail === "custom"
                              ? "#B31B1E"
                              : "#000000",
                        }}
                      >
                        Uploaded Thumbnail
                      </span>
                    </div>
                  ) : (
                    <label
                      style={{
                        boxSizing: "border-box",
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center",
                        padding: "20px",
                        gap: "8px",
                        width: "140px",
                        height: "80px",
                        border: "2px dashed #D9D9D9",
                        borderRadius: "10px",
                        cursor: "pointer",
                        background: "#FAFAFA",
                        flex: "none",
                        order: 0,
                        flexGrow: 0,
                      }}
                    >
                      <div
                        style={{
                          width: "24px",
                          height: "24px",
                          flex: "none",
                          order: 0,
                          flexGrow: 0,
                        }}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 16V8M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                            stroke="#999999"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <span
                        style={{
                          width: "100px",
                          height: "12px",
                          fontFamily: "Inter",
                          fontStyle: "normal",
                          fontWeight: 400,
                          fontSize: "10px",
                          lineHeight: "12px",
                          textAlign: "center",
                          color: "#999999",
                          flex: "none",
                          order: 1,
                          flexGrow: 0,
                        }}
                      >
                        Upload Thumbnail
                      </span>
                      <input
                        type="file"
                        accept="image/*"
                        style={{ display: "none" }}
                        onChange={(e) => {
                          const f = e.target.files?.[0];
                          if (!f) return;
                          setCustomThumbnail(f);
                          setCustomThumbnailUrl(URL.createObjectURL(f));
                          setSelectedThumbnail("custom");
                        }}
                      />
                    </label>
                  )}
                </div>
              </div>

              {/* Right side - Video preview */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "0px",
                  gap: "10px",
                  width: "280px",
                  height: "250px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                }}
              >
                <video
                  src={fileUrl}
                  controls
                  style={{
                    width: "280px",
                    height: "200px",
                    borderRadius: "10px",
                    objectFit: "cover",
                    background:
                      "linear-gradient(0deg, rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.24))",
                  }}
                />
                <span
                  style={{
                    width: "280px",
                    height: "20px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 500,
                    fontSize: "14px",
                    lineHeight: "20px",
                    textAlign: "center",
                    color: "#262626",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Video Preview
                </span>
              </div>
            </div>

            {/* Bottom buttons */}
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "flex-start",
                padding: "0px",
                gap: "40px",
                width: "620px",
                height: "62px",
                flex: "none",
                order: 2,
                alignSelf: "stretch",
                flexGrow: 0,
              }}
            >
              {/* Back button */}
              <div
                style={{
                  boxSizing: "border-box",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "143px",
                  height: "62px",
                  border: "1px solid #D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 0,
                  flexGrow: 0,
                  cursor: "pointer",
                }}
                onClick={() => setStep("category")}
              >
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                      fill="#262626"
                    />
                    <path
                      d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                      fill="#262626"
                    />
                  </svg>
                </div>
                <span
                  style={{
                    width: "49px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: "#292D32",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  Back
                </span>
              </div>

              {/* Next button */}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "16px 30px",
                  gap: "10px",
                  margin: "0 auto",
                  width: "140px",
                  height: "62px",
                  background: (
                    selectedThumbnail === "auto"
                      ? autoThumbnailUrl
                      : customThumbnailUrl
                  )
                    ? "#B31B1E"
                    : "#D9D9D9",
                  borderRadius: "10px",
                  flex: "none",
                  order: 1,
                  flexGrow: 0,
                  cursor: (
                    selectedThumbnail === "auto"
                      ? autoThumbnailUrl
                      : customThumbnailUrl
                  )
                    ? "pointer"
                    : "not-allowed",
                }}
                onClick={() => {
                  if (
                    selectedThumbnail === "auto"
                      ? autoThumbnailUrl
                      : customThumbnailUrl
                  ) {
                    setShowDetails(true);
                  }
                }}
              >
                <span
                  style={{
                    width: "46px",
                    height: "24px",
                    fontFamily: "Inter",
                    fontStyle: "normal",
                    fontWeight: 700,
                    fontSize: "20px",
                    lineHeight: "24px",
                    textTransform: "capitalize",
                    color: (
                      selectedThumbnail === "auto"
                        ? autoThumbnailUrl
                        : customThumbnailUrl
                    )
                      ? "#FDFCFF"
                      : "#999999",
                    flex: "none",
                    order: 0,
                    flexGrow: 0,
                  }}
                >
                  Next
                </span>
                <div
                  style={{
                    width: "24px",
                    height: "24px",
                    flex: "none",
                    order: 1,
                    flexGrow: 0,
                  }}
                >
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                      fill={
                        (
                          selectedThumbnail === "auto"
                            ? autoThumbnailUrl
                            : customThumbnailUrl
                        )
                          ? "#FFFFFF"
                          : "#999999"
                      }
                    />
                    <path
                      d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                      fill={
                        (
                          selectedThumbnail === "auto"
                            ? autoThumbnailUrl
                            : customThumbnailUrl
                        )
                          ? "#FFFFFF"
                          : "#999999"
                      }
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}
        {showDetails && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
            <div
              className="relative bg-white rounded-2xl shadow-lg mx-2 p-6 max-w-4xl w-full max-h-[90vh] overflow-hidden"
              style={{
                background:
                  "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
              }}
            >
              {/* Close button */}
              <button
                className="absolute top-4 right-4 text-gray-600 hover:text-gray-800 text-2xl font-light z-10"
                onClick={() => setShowDetails(false)}
                aria-label="Close"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.25 6.25L17.75 17.75M6.25 17.75L17.75 6.25"
                    stroke="#292D32"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              {/* Header Section */}
              <div className="flex items-center gap-4 mb-6">
                {/* Heart Logo */}
                <div className="w-12 h-9">
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title and Icon */}
                <div className="flex items-center gap-2">
                  <h2 className="text-2xl font-semibold text-black">Details</h2>
                  <div className="w-5 h-5">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#262626"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#262626"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#262626"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Subtitle */}
              <div className="flex items-center gap-2 mb-6">
                <div className="w-6 h-6">
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11.4599 14.5594C9.4999 14.5594 7.8999 12.9595 7.8999 10.9995C7.8999 9.03945 9.4999 7.43945 11.4599 7.43945C13.4199 7.43945 15.0199 9.03945 15.0199 10.9995C15.0199 12.9595 13.4199 14.5594 11.4599 14.5594ZM11.4599 8.94946C10.3299 8.94946 9.3999 9.86946 9.3999 11.0095C9.3999 12.1495 10.3199 13.0695 11.4599 13.0695C12.5999 13.0695 13.5199 12.1495 13.5199 11.0095C13.5199 9.86946 12.5999 8.94946 11.4599 8.94946Z"
                      fill="#292D32"
                    />
                    <path
                      d="M16.65 21.0296C16.24 21.0296 15.9 20.6896 15.9 20.2796C15.9 18.3596 13.91 16.7996 11.46 16.7996C9.01002 16.7996 7.02002 18.3596 7.02002 20.2796C7.02002 20.6896 6.68002 21.0296 6.27002 21.0296C5.86002 21.0296 5.52002 20.6896 5.52002 20.2796C5.52002 17.5396 8.18002 15.2996 11.46 15.2996C14.74 15.2996 17.4 17.5296 17.4 20.2796C17.4 20.6896 17.06 21.0296 16.65 21.0296Z"
                      fill="#292D32"
                    />
                    <path
                      d="M11.5 22.8296C5.85 22.8296 1.25 18.2296 1.25 12.5796C1.25 6.92959 5.85 2.32959 11.5 2.32959C12.89 2.32959 14.23 2.59958 15.49 3.12958C15.85 3.27958 16.03 3.67956 15.91 4.04956C15.8 4.37956 15.75 4.72959 15.75 5.07959C15.75 5.66959 15.91 6.24957 16.22 6.74957C16.38 7.02957 16.59 7.27956 16.83 7.48956C17.7 8.27956 18.99 8.52962 20 8.16962C20.37 8.02962 20.79 8.2196 20.94 8.5896C21.48 9.8596 21.75 11.2096 21.75 12.5896C21.75 18.2296 17.15 22.8296 11.5 22.8296ZM11.5 3.82959C6.68 3.82959 2.75 7.74959 2.75 12.5796C2.75 17.4096 6.68 21.3296 11.5 21.3296C16.32 21.3296 20.25 17.4096 20.25 12.5796C20.25 11.6196 20.09 10.6696 19.79 9.75958C18.41 9.99958 16.9 9.56961 15.84 8.59961C15.49 8.29961 15.18 7.92959 14.94 7.51959C14.5 6.79959 14.26 5.94959 14.26 5.07959C14.26 4.80959 14.28 4.54961 14.33 4.28961C13.42 3.97961 12.47 3.82959 11.5 3.82959Z"
                      fill="#292D32"
                    />
                    <path
                      d="M19 9.82959C17.82 9.82959 16.7 9.38961 15.83 8.59961C15.48 8.29961 15.17 7.92959 14.93 7.51959C14.49 6.79959 14.25 5.94959 14.25 5.07959C14.25 4.56959 14.33 4.0696 14.49 3.5896C14.71 2.9096 15.09 2.27959 15.6 1.76959C16.5 0.849592 17.71 0.32959 19.01 0.32959C20.37 0.32959 21.66 0.909607 22.54 1.90961C23.32 2.77961 23.76 3.89959 23.76 5.07959C23.76 5.45959 23.71 5.83958 23.61 6.19958C23.51 6.64958 23.32 7.1196 23.06 7.5296C22.48 8.5096 21.56 9.23959 20.48 9.57959C20.03 9.74959 19.53 9.82959 19 9.82959ZM19 1.82959C18.11 1.82959 17.28 2.17957 16.67 2.80957C16.32 3.16957 16.07 3.57956 15.92 4.04956C15.81 4.37956 15.76 4.72959 15.76 5.07959C15.76 5.66959 15.92 6.24957 16.23 6.74957C16.39 7.02957 16.6 7.27956 16.84 7.48956C17.71 8.27956 19 8.52962 20.01 8.16962C20.77 7.92962 21.39 7.42958 21.79 6.75958C21.97 6.46958 22.09 6.15961 22.16 5.84961C22.23 5.58961 22.26 5.33959 22.26 5.07959C22.26 4.27959 21.96 3.50961 21.42 2.90961C20.81 2.21961 19.93 1.82959 19 1.82959Z"
                      fill="#292D32"
                    />
                    <path
                      d="M20.49 5.80957H17.5C17.09 5.80957 16.75 5.46957 16.75 5.05957C16.75 4.64957 17.09 4.30957 17.5 4.30957H20.49C20.9 4.30957 21.24 4.64957 21.24 5.05957C21.24 5.46957 20.91 5.80957 20.49 5.80957Z"
                      fill="#292D32"
                    />
                    <path
                      d="M19 7.3396C18.59 7.3396 18.25 6.9996 18.25 6.5896V3.59961C18.25 3.18961 18.59 2.84961 19 2.84961C19.41 2.84961 19.75 3.18961 19.75 3.59961V6.5896C19.75 7.0096 19.41 7.3396 19 7.3396Z"
                      fill="#292D32"
                    />
                  </svg>
                </div>
                <span className="text-lg text-black">Add Personal Details</span>
              </div>

              {/* Main Content Area */}
              <div className="flex flex-col lg:flex-row gap-8">
                {/* Form Section - Left Side */}
                <div className="flex-1 space-y-4 max-h-96 overflow-y-auto pr-2">
                  {/* Add Caption Input */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <input
                      type="text"
                      placeholder="Add Caption"
                      value={details.caption}
                      onChange={(e) =>
                        setDetails((d) => ({ ...d, caption: e.target.value }))
                      }
                      className="w-full text-base text-black bg-transparent border-none outline-none placeholder:text-gray-400"
                    />
                  </div>

                  {/* Tag Input */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <input
                      type="text"
                      placeholder="Partner"
                      value={details.partner}
                      onChange={(e) =>
                        setDetails((d) => ({ ...d, partner: e.target.value }))
                      }
                      className="w-full text-base text-black bg-transparent border-none outline-none placeholder:text-gray-400"
                    />
                  </div>

                  {/* Place Dropdown */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <CitySearchInput
                      value={details.place}
                      onChange={(val) =>
                        setDetails((d) => ({ ...d, place: val }))
                      }
                      placeholder="Select or search city..."
                    />
                  </div>

                  {/* Budget Dropdown */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <select
                      value={details.budget}
                      onChange={(e) =>
                        setDetails((d) => ({ ...d, budget: e.target.value }))
                      }
                      className="w-full text-base text-black bg-transparent border-none outline-none"
                    >
                      <option value="">Select Budget</option>
                      {BUDGET_OPTIONS.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {opt.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Event Type Dropdown */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <select
                      value={details.event_type}
                      onChange={(e) =>
                        setDetails((d) => ({
                          ...d,
                          event_type: e.target.value,
                        }))
                      }
                      className="w-full text-base text-black bg-transparent border-none outline-none"
                    >
                      <option value="">Select Event Type</option>
                      {EVENT_TYPE_OPTIONS.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {opt.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  {/* Wedding Style Input */}
                  <div className="border border-gray-300 rounded-lg p-4">
                    <input
                      type="text"
                      placeholder="Add style of wedding"
                      value={details.wedding_style}
                      onChange={(e) =>
                        setDetails((d) => ({
                          ...d,
                          wedding_style: e.target.value,
                        }))
                      }
                      className="w-full text-base text-black bg-transparent border-none outline-none placeholder:text-gray-400"
                    />
                  </div>
                </div>

                {/* Video Preview - Right Side */}
                <div className="w-full lg:w-80 h-80">
                  {fileUrl ? (
                    <video
                      src={fileUrl}
                      controls
                      className="w-full h-full object-cover rounded-lg"
                      style={{
                        background:
                          "linear-gradient(0deg, rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.24))",
                      }}
                    />
                  ) : (
                    <div
                      className="w-full h-full rounded-lg flex items-center justify-center text-white text-base font-medium"
                      style={{
                        background:
                          "linear-gradient(0deg, rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.24))",
                      }}
                    >
                      Video Preview
                    </div>
                  )}
                </div>
              </div>

              {/* Bottom Buttons */}
              <div className="flex justify-between items-center gap-8 mt-8">
                {/* Back Button */}
                <button
                  className="flex items-center justify-center gap-2 px-8 py-4 border border-gray-300 rounded-lg bg-transparent hover:bg-gray-50 transition-colors"
                  onClick={() => setShowDetails(false)}
                >
                  <div className="w-6 h-6">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                        fill="#262626"
                      />
                      <path
                        d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                  <span className="text-xl font-bold text-gray-800 capitalize">
                    Back
                  </span>
                </button>

                {/* Next Button */}
                <button
                  className="flex items-center justify-center gap-2 px-8 py-4 bg-red-600 rounded-lg text-white hover:bg-red-700 transition-colors"
                  onClick={() => {
                    setShowVendors(true);
                    setShowDetails(false);
                  }}
                  disabled={
                    details.caption.trim().length < 1 ||
                    details.partner.trim().length < 1 ||
                    details.place.trim().length < 1 ||
                    details.budget.trim().length < 1 ||
                    details.event_type.trim().length < 1 ||
                    details.wedding_style.trim().length < 1
                  }
                >
                  <span className="text-xl font-bold capitalize">Next</span>
                  <div className="w-6 h-6">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </div>
                </button>
              </div>
            </div>
          </div>
        )}

        {showVendors && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
            <div
              className="relative bg-white rounded-2xl shadow-lg mx-2 p-6 max-w-4xl w-full max-h-[90vh] overflow-hidden"
              style={{
                background:
                  "linear-gradient(180deg, #FAE6C4 0%, #FFFFFF 99.79%)",
                marginTop: "40px", // keeps it near the top
                marginBottom: "90px", // adds space at the bottom (pulls it up)
                maxHeight: "calc(100vh - 100px)", // optional, to ensure it never overflows
                overflowY: "auto",
              }}
            >
              <div className="flex items-center gap-4 mb-6">
                {/* Heart Logo */}
                <div className="w-12 h-9">
                  <svg
                    width="47"
                    height="38"
                    viewBox="0 0 47 38"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M26.5435 33.9151C25.6401 33.9467 24.966 34.2089 24.5212 34.7032C24.075 35.1974 23.7564 35.9882 23.5684 37.0797C23.3789 35.9882 23.0618 35.1974 22.6156 34.7032C22.1694 34.2089 21.4953 33.9467 20.5933 33.9151C21.4953 33.8836 22.1694 33.6227 22.6156 33.1285C23.0618 32.6342 23.3775 31.8434 23.5684 30.752C23.7564 31.8434 24.075 32.6342 24.5212 33.1285C24.966 33.6227 25.6401 33.8836 26.5435 33.9151Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M17.5399 2.28733C15.6205 0.782623 13.3923 0.0645903 11.0021 6.34036e-05C6.58403 -0.0150386 2.55042 2.669 0.869973 6.66692C-0.861268 10.7857 0.0228854 15.3931 3.24099 18.6401C8.10384 23.5441 13.0038 28.4111 17.8721 33.3083C18.3293 33.7668 18.6052 33.7119 19.0212 33.2836C20.3351 31.9271 21.6833 30.605 23.0164 29.2705C23.5203 28.7667 24.1175 28.1681 24.7943 27.4912C25.6908 26.592 26.7246 25.5582 27.86 24.42C30.7802 21.4985 30.1912 22.0847 30.6471 21.6275L33.9599 18.3147L17.5399 2.29008V2.28733ZM24.7971 21.3461C23.685 22.4595 22.6526 23.492 21.7286 24.4173C20.6852 25.4648 19.7791 26.3723 19.0542 27.0986C18.6821 27.472 18.4226 27.7164 17.9174 27.2071C14.0225 23.2682 10.074 19.3815 6.19001 15.4302C3.68032 12.8807 3.74897 8.7867 6.25591 6.29349C8.48689 4.07212 11.8794 3.76321 14.4247 5.40247L27.5772 18.5563C27.2697 18.8652 27.5786 18.5591 24.7957 21.3461H24.7971Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M14.5689 16.7757C13.7246 17.6228 13.726 18.9943 14.5717 19.8414C17.7129 22.984 14.9767 20.2533 19.1393 24.4187C20.2747 25.5554 21.3099 26.5906 22.2051 27.4885C22.8819 28.1653 23.4791 28.7639 23.983 29.2678C25.3174 30.6036 26.6643 31.9258 27.9781 33.2822C28.3955 33.7119 28.6715 33.7668 29.1273 33.3069C33.9956 28.4084 38.8969 23.5441 43.7584 18.6387C46.9765 15.3918 47.862 10.7857 46.1294 6.66556C44.449 2.66764 40.414 -0.0163995 35.9973 7.54205e-05C34.6738 0.0357711 33.4011 0.270536 32.2012 0.730461C30.7391 1.28924 30.3615 3.18797 31.4681 4.29591C32.0832 4.91098 33.0003 5.08534 33.8144 4.77918C36.1456 3.90464 38.8599 4.41673 40.7435 6.29213C43.2504 8.78671 43.3191 12.8807 40.8094 15.4302C36.9254 19.3801 32.9783 23.2682 29.082 27.2057C28.5767 27.715 28.3173 27.4707 27.9466 27.0986C27.2217 26.3723 26.3156 25.4648 25.2708 24.4173C24.3468 23.492 23.3157 22.4582 22.2037 21.3447C17.9765 17.112 20.6537 19.7865 17.6456 16.7743C16.7958 15.9245 15.4174 15.9245 14.5689 16.7743V16.7757Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M11.9479 16.5821C13.3158 16.5821 14.4247 15.4733 14.4247 14.1054C14.4247 12.7375 13.3158 11.6287 11.9479 11.6287C10.5801 11.6287 9.47119 12.7375 9.47119 14.1054C9.47119 15.4733 10.5801 16.5821 11.9479 16.5821Z"
                      fill="#B31B1E"
                    />
                    <path
                      d="M27.9928 8.97471C29.3607 8.97471 30.4696 7.86584 30.4696 6.49797C30.4696 5.13011 29.3607 4.02124 27.9928 4.02124C26.625 4.02124 25.5161 5.13011 25.5161 6.49797C25.5161 7.86584 26.625 8.97471 27.9928 8.97471Z"
                      fill="#B31B1E"
                    />
                  </svg>
                </div>

                {/* Title and Icon */}
                <div className="flex items-center gap-2">
                  <h2 className="text-2xl font-semibold text-black">
                    Vendor Details
                  </h2>
                  <div className="w-5 h-5">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.0002 19.7064C4.76961 19.7064 0.520996 15.4578 0.520996 10.2272C0.520996 4.99666 4.76961 0.748047 10.0002 0.748047C10.3988 0.748047 10.7293 1.0786 10.7293 1.47721C10.7293 1.87582 10.3988 2.20638 10.0002 2.20638C5.57655 2.20638 1.97933 5.8036 1.97933 10.2272C1.97933 14.6508 5.57655 18.248 10.0002 18.248C14.4238 18.248 18.021 14.6508 18.021 10.2272C18.021 9.8286 18.3516 9.49805 18.7502 9.49805C19.1488 9.49805 19.4793 9.8286 19.4793 10.2272C19.4793 15.4578 15.2307 19.7064 10.0002 19.7064Z"
                        fill="#262626"
                      />
                      <path
                        d="M15.3473 8.80784C14.4529 8.80784 12.3723 7.71895 11.7306 5.71617C11.2931 4.34534 11.7987 2.54673 13.3834 2.03145C14.064 1.80784 14.7737 1.91478 15.3376 2.27451C15.8918 1.91478 16.6209 1.81756 17.3015 2.03145C18.8862 2.54673 19.4015 4.34534 18.9543 5.71617C18.3223 7.75784 16.1348 8.80784 15.3473 8.80784ZM13.1209 5.27867C13.5681 6.68839 15.0848 7.33006 15.357 7.35923C15.6681 7.33006 17.1556 6.61062 17.564 5.28839C17.7876 4.57867 17.564 3.66478 16.8543 3.43145C16.5529 3.33423 16.1445 3.39256 15.9501 3.67451C15.814 3.87867 15.6001 3.99534 15.357 4.00506C15.1334 4.01478 14.8904 3.89812 14.7543 3.70367C14.5306 3.38284 14.1223 3.33423 13.8306 3.42173C13.1306 3.65506 12.8973 4.56895 13.1209 5.27867Z"
                        fill="#262626"
                      />
                      <path
                        d="M10.2085 13.4565C9.86683 13.4565 9.5835 13.1731 9.5835 12.8315V8.66479C9.5835 8.32313 9.86683 8.03979 10.2085 8.03979C10.5502 8.03979 10.8335 8.32313 10.8335 8.66479V12.8315C10.8335 13.1731 10.5502 13.4565 10.2085 13.4565Z"
                        fill="#262626"
                      />
                      <path
                        d="M12.2917 11.373H8.125C7.78333 11.373 7.5 11.0897 7.5 10.748C7.5 10.4064 7.78333 10.123 8.125 10.123H12.2917C12.6333 10.123 12.9167 10.4064 12.9167 10.748C12.9167 11.0897 12.6333 11.373 12.2917 11.373Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 mb-6">
                <div className="w-6 h-6">
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14.72 22.8296H9.33007C8.69007 22.8296 8.13005 22.7896 7.62005 22.7196C7.21005 22.6596 6.92003 22.2796 6.98003 21.8696C7.04003 21.4596 7.41007 21.1596 7.83007 21.2296C8.27007 21.2896 8.76006 21.3196 9.32006 21.3196H14.7101C18.8001 21.3196 20.2501 19.8695 20.2501 15.7795V11.2896C20.2501 10.8796 20.5901 10.5396 21.0001 10.5396C21.4101 10.5396 21.7501 10.8796 21.7501 11.2896V15.7795C21.7601 20.7195 19.65 22.8296 14.72 22.8296Z"
                      fill="#292D32"
                    />
                    <path
                      d="M3.04004 16.3496C2.63004 16.3496 2.29004 16.0096 2.29004 15.5996V11.2996C2.29004 10.8896 2.63004 10.5496 3.04004 10.5496C3.45004 10.5496 3.79004 10.8896 3.79004 11.2996V15.5996C3.79004 16.0096 3.45004 16.3496 3.04004 16.3496Z"
                      fill="#292D32"
                    />
                    <path
                      d="M12.0298 12.8296C10.9298 12.8296 9.92978 12.3996 9.21978 11.6096C8.50978 10.8196 8.17979 9.78958 8.28979 8.68958L8.95983 2.00964C8.99983 1.62964 9.31983 1.32959 9.70983 1.32959H14.3798C14.7698 1.32959 15.0898 1.61964 15.1298 2.00964L15.7998 8.68958C15.9098 9.78958 15.5798 10.8196 14.8698 11.6096C14.1298 12.3996 13.1298 12.8296 12.0298 12.8296ZM10.3698 2.82959L9.76983 8.8396C9.69983 9.5096 9.89982 10.1396 10.3198 10.5996C11.1698 11.5396 12.8698 11.5396 13.7198 10.5996C14.1398 10.1296 14.3398 9.4996 14.2698 8.8396L13.6698 2.82959H10.3698Z"
                      fill="#292D32"
                    />
                    <path
                      d="M18.33 12.8296C16.3 12.8296 14.49 11.1896 14.28 9.16956L13.58 2.15955C13.56 1.94955 13.63 1.73959 13.77 1.57959C13.91 1.41959 14.11 1.32959 14.33 1.32959H17.38C20.32 1.32959 21.69 2.55959 22.1 5.57959L22.38 8.35962C22.5 9.53962 22.14 10.6596 21.37 11.5096C20.6 12.3596 19.52 12.8296 18.33 12.8296ZM15.16 2.82959L15.78 9.01953C15.91 10.2695 17.08 11.3296 18.33 11.3296C19.09 11.3296 19.77 11.0396 20.26 10.5096C20.74 9.97964 20.96 9.26964 20.89 8.50964L20.61 5.75964C20.3 3.49964 19.57 2.82959 17.38 2.82959H15.16Z"
                      fill="#292D32"
                    />
                    <path
                      d="M5.66979 12.8296C4.47979 12.8296 3.39982 12.3596 2.62982 11.5096C1.85982 10.6596 1.49981 9.53962 1.61981 8.35962L1.89984 5.60962C2.31984 2.55962 3.68982 1.32959 6.62982 1.32959H9.6798C9.8898 1.32959 10.0898 1.41959 10.2398 1.57959C10.3798 1.73959 10.4498 1.94955 10.4298 2.15955L9.72979 9.16956C9.50979 11.1896 7.68979 12.8296 5.66979 12.8296ZM6.61981 2.82959C4.42981 2.82959 3.69982 3.48954 3.37982 5.77954L3.1098 8.49963C3.0298 9.25963 3.2598 9.96963 3.7398 10.4996C4.2198 11.0296 4.90979 11.3196 5.66979 11.3196C6.92979 11.3196 8.09978 10.2596 8.21978 9.00964L8.83984 2.81958H6.61981V2.82959Z"
                      fill="#292D32"
                    />
                    <path
                      d="M5 23.8296C3.6 23.8296 2.29001 23.2195 1.39001 22.1495C1.38001 22.1395 1.34 22.0996 1.31 22.0496C1.16 21.8696 1.02999 21.6896 0.929993 21.4996C0.489993 20.7896 0.25 19.9496 0.25 19.0696C0.25 17.6096 0.900029 16.2596 2.03003 15.3596C2.23003 15.1996 2.45 15.0496 2.69 14.9196C3.38 14.5296 4.19 14.3096 5 14.3096C6.16 14.3096 7.23003 14.7096 8.09003 15.4596C8.20003 15.5396 8.34998 15.6896 8.47998 15.8296C9.28998 16.7196 9.73999 17.8596 9.73999 19.0496C9.73999 19.9196 9.49999 20.7696 9.04999 21.4996C8.82999 21.8796 8.56001 22.2096 8.26001 22.4696C8.20001 22.5296 8.14001 22.5896 8.07001 22.6396C7.28001 23.4096 6.17 23.8296 5 23.8296ZM2.47998 21.1196C2.50998 21.1496 2.55998 21.1995 2.59998 21.2695C3.14998 21.9195 4.05 22.3396 5 22.3396C5.79 22.3396 6.55 22.0495 7.13 21.5295C7.16001 21.4895 7.2 21.4496 7.25 21.4196C7.45 21.2496 7.62003 21.0295 7.78003 20.7695C8.09003 20.2595 8.26001 19.6896 8.26001 19.0896C8.26001 18.2796 7.95001 17.4896 7.39001 16.8796C7.32001 16.7996 7.24997 16.7195 7.15997 16.6595C6.07997 15.7295 4.59998 15.5896 3.41998 16.2596C3.25998 16.3496 3.11999 16.4396 2.98999 16.5496C2.20999 17.1696 1.76001 18.0896 1.76001 19.0896C1.76001 19.6796 1.91998 20.2596 2.22998 20.7596C2.30998 20.8996 2.38998 21.0096 2.47998 21.1096C2.46998 21.1096 2.47998 21.1096 2.47998 21.1196Z"
                      fill="#292D32"
                    />
                    <path
                      d="M6.49976 19.8096H3.50977C3.09977 19.8096 2.75977 19.4696 2.75977 19.0596C2.75977 18.6496 3.09977 18.3096 3.50977 18.3096H6.49976C6.90976 18.3096 7.24976 18.6496 7.24976 19.0596C7.24976 19.4696 6.90976 19.8096 6.49976 19.8096Z"
                      fill="#292D32"
                    />
                    <path
                      d="M5 21.3396C4.59 21.3396 4.25 20.9996 4.25 20.5896V17.5996C4.25 17.1896 4.59 16.8496 5 16.8496C5.41 16.8496 5.75 17.1896 5.75 17.5996V20.5896C5.75 21.0096 5.41 21.3396 5 21.3396Z"
                      fill="#292D32"
                    />
                  </svg>
                </div>
                <span className="text-lg text-black">
                  Add details to help others find it easily
                </span>
              </div>

              <div className="flex justify-end  -mt-13">
                <button
                  className="flex items-center justify-center gap-[10px] w-[120px] h-[32px] px-[10px] py-[6px] bg-[#E0CBCB] border border-[#D9D9D9] rounded-[20px] font-['Inter'] font-medium text-[14px] text-black"
                  onClick={() =>
                    setAdditionalVendors((vs) => [
                      ...vs,
                      { name: "", phone: "" },
                    ])
                  }
                >
                  Add More <span className="text-lg text-black"></span>
                  <svg
                    width="20"
                    height="21"
                    viewBox="0 0 20 21"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.0002 10.7047C7.3585 10.7047 5.2085 8.55467 5.2085 5.913C5.2085 3.27134 7.3585 1.12134 10.0002 1.12134C12.6418 1.12134 14.7918 3.27134 14.7918 5.913C14.7918 8.55467 12.6418 10.7047 10.0002 10.7047ZM10.0002 2.37134C8.05016 2.37134 6.4585 3.963 6.4585 5.913C6.4585 7.863 8.05016 9.45467 10.0002 9.45467C11.9502 9.45467 13.5418 7.863 13.5418 5.913C13.5418 3.963 11.9502 2.37134 10.0002 2.37134Z"
                      fill="#292D32"
                    />
                    <path
                      d="M2.8418 19.0379C2.50013 19.0379 2.2168 18.7546 2.2168 18.4129C2.2168 14.8546 5.70846 11.9546 10.0001 11.9546C10.8418 11.9546 11.6668 12.0629 12.4668 12.2879C12.8001 12.3796 12.9918 12.7213 12.9001 13.0546C12.8085 13.3879 12.4668 13.5796 12.1335 13.4879C11.4501 13.2963 10.7335 13.2046 10.0001 13.2046C6.40013 13.2046 3.4668 15.5379 3.4668 18.4129C3.4668 18.7546 3.18346 19.0379 2.8418 19.0379Z"
                      fill="#292D32"
                    />
                    <path
                      d="M14.9998 19.038C14.0165 19.038 13.0832 18.6714 12.3582 18.013C12.0665 17.763 11.8082 17.4547 11.6082 17.113C11.2415 16.513 11.0415 15.8047 11.0415 15.0797C11.0415 14.038 11.4415 13.063 12.1582 12.3213C12.9082 11.5463 13.9165 11.1213 14.9998 11.1213C16.1332 11.1213 17.2082 11.6047 17.9415 12.438C18.5915 13.163 18.9582 14.0963 18.9582 15.0797C18.9582 15.3963 18.9165 15.713 18.8332 16.013C18.7498 16.388 18.5915 16.7797 18.3748 17.1213C17.6832 18.3047 16.3832 19.038 14.9998 19.038ZM14.9998 12.3713C14.2582 12.3713 13.5748 12.663 13.0582 13.188C12.5665 13.6963 12.2915 14.363 12.2915 15.0797C12.2915 15.5713 12.4248 16.0547 12.6832 16.4713C12.8165 16.7047 12.9915 16.913 13.1915 17.088C13.6915 17.5463 14.3332 17.7963 14.9998 17.7963C15.9415 17.7963 16.8332 17.2963 17.3165 16.488C17.4582 16.2547 17.5665 15.988 17.6248 15.7297C17.6832 15.513 17.7082 15.3047 17.7082 15.088C17.7082 14.4213 17.4582 13.7797 17.0082 13.2797C16.5082 12.6964 15.7748 12.3713 14.9998 12.3713Z"
                      fill="#292D32"
                    />
                    <path
                      d="M16.25 15.688H13.7583C13.4166 15.688 13.1333 15.4047 13.1333 15.063C13.1333 14.7213 13.4166 14.438 13.7583 14.438H16.25C16.5916 14.438 16.875 14.7213 16.875 15.063C16.875 15.4047 16.5916 15.688 16.25 15.688Z"
                      fill="#292D32"
                    />
                    <path
                      d="M15 16.9628C14.6583 16.9628 14.375 16.6795 14.375 16.3378V13.8462C14.375 13.5045 14.6583 13.2212 15 13.2212C15.3417 13.2212 15.625 13.5045 15.625 13.8462V16.3378C15.625 16.6878 15.3417 16.9628 15 16.9628Z"
                      fill="#292D32"
                    />
                  </svg>
                </button>
              </div>
              <div
                className="grid grid-cols-3 gap-3 items-center mt-4"
                style={{
                  maxHeight: additionalVendors.length > 0 ? "400px" : "auto",
                  overflowY: additionalVendors.length > 0 ? "auto" : "visible",
                  paddingRight: additionalVendors.length > 0 ? "8px" : "0",
                }}
              >
                {VENDOR_LABELS.map((label, i) => (
                  <React.Fragment key={label}>
                    <div className="text-right pr-2 col-span-1 text-sm font-medium text-gray-700">
                      {label}
                    </div>
                    <input
                      type="text"
                      className="w-full px-4 py-3 rounded-lg border border-[#E4E4E7] bg-[#FFF6F0] text-[#5A5A5A] font-['Inter'] font-normal text-[16px] leading-[14.5px] placeholder-[#5A5A5A]"
                      placeholder="Name"
                      value={vendorDetails[i].name}
                      onChange={(e) =>
                        setVendorDetails((vs) =>
                          vs.map((v, idx) =>
                            idx === i ? { ...v, name: e.target.value } : v
                          )
                        )
                      }
                    />
                    <input
                      type="text"
                      className="w-full px-4 py-3 rounded-lg border border-[#E4E4E7] bg-[#FFF6F0] text-[#5A5A5A] font-['Inter'] font-normal text-[16px] leading-[14.5px] placeholder-[#5A5A5A]"
                      placeholder="Mobile Number "
                      value={vendorDetails[i].phone}
                      onChange={(e) =>
                        setVendorDetails((vs) =>
                          vs.map((v, idx) =>
                            idx === i ? { ...v, phone: e.target.value } : v
                          )
                        )
                      }
                    />
                  </React.Fragment>
                ))}
                {additionalVendors.map((v, i) => (
                  <React.Fragment key={i}>
                    <div className="text-right pr-2 col-span-1 text-sm font-medium text-gray-700">
                      Additional {i + 1}
                    </div>
                    <input
                      className="w-full px-4 py-3 rounded-lg border border-[#E4E4E7] bg-[#FFF6F0] text-[#5A5A5A] font-['Inter'] font-normal text-[16px] leading-[14.5px] placeholder-[#5A5A5A]"
                      type="text"
                      placeholder="Name"
                      value={v.name}
                      onChange={(e) =>
                        setAdditionalVendors((vs) =>
                          vs.map((vv, idx) =>
                            idx === i ? { ...vv, name: e.target.value } : vv
                          )
                        )
                      }
                    />
                    <input
                      className="w-full px-4 py-3 rounded-lg border border-[#E4E4E7] bg-[#FFF6F0] text-[#5A5A5A] font-['Inter'] font-normal text-[16px] leading-[14.5px] placeholder-[#5A5A5A]"
                      type="text"
                      placeholder="Mobile Number"
                      value={v.phone}
                      onChange={(e) =>
                        setAdditionalVendors((vs) =>
                          vs.map((vv, idx) =>
                            idx === i ? { ...vv, phone: e.target.value } : vv
                          )
                        )
                      }
                    />
                  </React.Fragment>
                ))}
              </div>
              <div className="flex justify-between items-center gap-8 mt-8">
                <button
                  className="flex items-center justify-center gap-2 px-8 py-4 border border-gray-300 rounded-lg bg-transparent hover:bg-gray-50 transition-colors"
                  onClick={() => setShowVendors(false)}
                >
                  <div className="w-6 h-6">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9.56988 5.25947C9.75988 5.25947 9.94988 5.32947 10.0999 5.47947C10.3899 5.76947 10.3899 6.24947 10.0999 6.53947L4.55988 12.0795L10.0999 17.6195C10.3899 17.9095 10.3899 18.3895 10.0999 18.6795C9.80988 18.9695 9.32988 18.9695 9.03988 18.6795L2.96988 12.6095C2.67988 12.3195 2.67988 11.8395 2.96988 11.5495L9.03988 5.47947C9.18988 5.32947 9.37988 5.25947 9.56988 5.25947Z"
                        fill="#262626"
                      />
                      <path
                        d="M3.67 11.3296L20.5 11.3296C20.91 11.3296 21.25 11.6696 21.25 12.0796C21.25 12.4896 20.91 12.8296 20.5 12.8296L3.67 12.8296C3.26 12.8296 2.92 12.4896 2.92 12.0796C2.92 11.6696 3.26 11.3296 3.67 11.3296Z"
                        fill="#262626"
                      />
                    </svg>
                  </div>
                  <span className="text-xl font-bold text-gray-800 capitalize">
                    Back
                  </span>
                </button>
                <button
                  className={`flex items-center justify-center gap-2 px-8 py-4 rounded-lg text-white transition-colors ${
                    videoCategory === "my_wedding"
                      ? vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 4
                        ? "bg-red-300 cursor-not-allowed"
                        : "bg-red-600 hover:bg-red-700"
                      : vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 1
                      ? "bg-red-300 cursor-not-allowed"
                      : "bg-red-600 hover:bg-red-700"
                  }`}
                  onClick={() => {
                    if (
                      (videoCategory === "my_wedding" &&
                        vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 4) ||
                      (videoCategory !== "my_wedding" &&
                        vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 1)
                    ) {
                      return;
                    }
                    handleVendorNext();
                    setShowVendors(false);
                  }}
                  disabled={
                    videoCategory === "my_wedding"
                      ? vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 4
                      : vendorDetails
                          .concat(additionalVendors)
                          .filter((v) => v.name && v.phone).length < 1
                  }
                >
                  <span className="text-xl font-bold capitalize">Next</span>
                  <div className="w-6 h-6">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M14.4301 18.8997C14.2401 18.8997 14.0501 18.8297 13.9001 18.6797C13.6101 18.3897 13.6101 17.9097 13.9001 17.6197L19.4401 12.0797L13.9001 6.53971C13.6101 6.24971 13.6101 5.76971 13.9001 5.47971C14.1901 5.18971 14.6701 5.18971 14.9601 5.47971L21.0301 11.5497C21.3201 11.8397 21.3201 12.3197 21.0301 12.6097L14.9601 18.6797C14.8101 18.8297 14.6201 18.8997 14.4301 18.8997Z"
                        fill="#FFFFFF"
                      />
                      <path
                        d="M20.33 12.8296H3.5C3.09 12.8296 2.75 12.4896 2.75 12.0796C2.75 11.6696 3.09 11.3296 3.5 11.3296H20.33C20.74 11.3296 21.08 11.6696 21.08 12.0796C21.08 12.4896 20.74 12.8296 20.33 12.8296Z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </div>
                </button>
              </div>
              {((videoCategory === "my_wedding" &&
                vendorDetails
                  .concat(additionalVendors)
                  .filter((v) => v.name && v.phone).length < 4) ||
                (videoCategory === "wedding_vlog" &&
                  vendorDetails
                    .concat(additionalVendors)
                    .filter((v) => v.name && v.phone).length < 1)) && (
                <div className="text-red-600 text-sm text-right w-full">
                  Please complete at least{" "}
                  {videoCategory === "my_wedding" ? 4 : 1} vendor details (
                  {
                    vendorDetails
                      .concat(additionalVendors)
                      .filter((v) => v.name && v.phone).length
                  }
                  /{videoCategory === "my_wedding" ? 4 : 1})
                </div>
              )}
            </div>
          </div>
        )}
        {step === "face" && !userFaceVerified && (
          <FaceVerificationModal
            open={true}
            onVerified={() => {
              setUserFaceVerified(true);
              setFaceVerified(true);
            }}
            onClose={() => {
              setUserFaceVerified(false);
              setStep("file");
            }}
          />
        )}
        {step === "face" && userFaceVerified && (
          <div className="flex flex-col items-center justify-center min-h-[200px]">
            <span className="text-green-600 font-semibold mb-2">
              Face already verified! You can proceed.
            </span>
            <button
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 mt-4"
              onClick={() => {
                setStep("uploading");
                startUpload();
              }}
            >
              Continue to Upload
            </button>
          </div>
        )}
        {step === "uploading" && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold">Uploading...</span>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
              <div
                className="bg-red-600 h-4 rounded-full transition-all"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <span className="text-sm text-gray-700">{progress}%</span>
          </div>
        )}
        {uploadError && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold text-red-600">
              {uploadError}
            </span>
            <button
              className="w-full py-2 rounded-md bg-red-600 text-white font-semibold text-base hover:bg-red-700 transition-colors mt-2"
              onClick={resetUploadState}
            >
              Try Again
            </button>
          </div>
        )}
        {uploadSuccess && (
          <div className="flex flex-col items-center justify-center min-h-[200px] w-full">
            <span className="mb-2 text-base font-semibold text-green-600">
              Video uploaded successfully!
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoUploadModal;
