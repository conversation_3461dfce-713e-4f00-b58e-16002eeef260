"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>/page",{

/***/ "(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx":
/*!****************************************************!*\
  !*** ./app/home/<USER>/components/EInvites.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Plus,Share2,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _EInviteEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EInviteEditor */ \"(app-pages-browser)/./app/home/<USER>/components/EInviteEditor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EInvites = (param)=>{\n    let { setError, setSuccessMessage, setLoading, loading, error, successMessage } = param;\n    _s();\n    const [einvites, setEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingEInvite, setEditingEInvite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingEInvites, setLoadingEInvites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingTemplates, setLoadingTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredCard, setHoveredCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to clear all cached data\n    const clearAllCache = ()=>{\n        console.log('Clearing all cached data...');\n        setTemplates([]);\n        setEInvites([]);\n        setError(null);\n        setEditingEInvite(null);\n        // Clear any browser cache\n        if (true) {\n            const keys = Object.keys(localStorage);\n            keys.forEach((key)=>{\n                const value = localStorage.getItem(key);\n                if (value && value.includes('default-')) {\n                    localStorage.removeItem(key);\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EInvites.useEffect\": ()=>{\n            clearAllCache();\n            fetchEInvites();\n            fetchTemplates();\n        }\n    }[\"EInvites.useEffect\"], []);\n    const getAuthToken = ()=>{\n        return localStorage.getItem('token');\n    };\n    const fetchEInvites = async ()=>{\n        try {\n            const token = getAuthToken();\n            if (!token) {\n                // If no token, just set empty e-invites and show templates\n                setEInvites([]);\n                setLoadingEInvites(false);\n                return;\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.data && response.data.websites) {\n                // Filter for e-invites (websites marked with type='einvite' in design_settings)\n                const einvites = response.data.websites.filter((website)=>{\n                    var _website_design_settings;\n                    return ((_website_design_settings = website.design_settings) === null || _website_design_settings === void 0 ? void 0 : _website_design_settings.type) === 'einvite';\n                });\n                setEInvites(einvites);\n            }\n            setLoadingEInvites(false);\n        } catch (error) {\n            console.error('Error fetching e-invites:', error);\n            // Don't show error for e-invites, just show empty and let templates load\n            setEInvites([]);\n            setLoadingEInvites(false);\n        }\n    };\n    const fetchTemplates = async ()=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (!token) {\n                console.log('No token available for fetching templates');\n                setLoadingTemplates(false);\n                return;\n            }\n            console.log('Fetching templates with token:', token.substring(0, 20) + '...');\n            // Use the same API call as websites - with authentication\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            console.log('Templates API response:', response.data);\n            // Default frontend templates including the new Indian template\n            const defaultTemplates = [\n                {\n                    template_id: 'indian-traditional-001',\n                    name: 'Indian Traditional',\n                    thumbnail_url: 'data:image/svg+xml;base64,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',\n                    description: 'Beautiful traditional Indian wedding invitation with Ganesh blessings, marigold decorations, and vibrant red-gold colors.',\n                    default_colors: {\n                        primary: '#B31B1E',\n                        secondary: '#FFD700',\n                        background: '#FFFFFF',\n                        text: '#000000',\n                        accent: '#FF8C00'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Playfair Display',\n                            'Georgia',\n                            'serif'\n                        ],\n                        body: [\n                            'Roboto',\n                            'Arial',\n                            'sans-serif'\n                        ],\n                        decorative: [\n                            'Dancing Script',\n                            'cursive'\n                        ]\n                    }\n                },\n                {\n                    template_id: 'classic-elegance-001',\n                    name: 'Classic Elegance',\n                    thumbnail_url: 'https://via.placeholder.com/300x400/B31B1E/FFFFFF?text=Classic+Elegance',\n                    description: 'A timeless and elegant design for your special day.',\n                    default_colors: {\n                        primary: '#B31B1E',\n                        secondary: '#333333',\n                        background: '#FFFFFF',\n                        text: '#000000'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Playfair Display',\n                            'Georgia',\n                            'serif'\n                        ],\n                        body: [\n                            'Roboto',\n                            'Arial',\n                            'sans-serif'\n                        ]\n                    }\n                },\n                {\n                    template_id: 'modern-minimalist-001',\n                    name: 'Modern Minimalist',\n                    thumbnail_url: 'https://via.placeholder.com/300x400/000000/FFFFFF?text=Modern+Minimalist',\n                    description: 'A clean, modern design with minimalist aesthetics.',\n                    default_colors: {\n                        primary: '#000000',\n                        secondary: '#CCCCCC',\n                        background: '#FFFFFF',\n                        text: '#333333'\n                    },\n                    default_fonts: {\n                        heading: [\n                            'Montserrat',\n                            'Helvetica',\n                            'sans-serif'\n                        ],\n                        body: [\n                            'Open Sans',\n                            'Arial',\n                            'sans-serif'\n                        ]\n                    }\n                }\n            ];\n            if (response.data && response.data.templates && response.data.templates.length > 0) {\n                const apiTemplates = response.data.templates.map((template)=>({\n                        ...template,\n                        thumbnail_url: template.thumbnail_url || 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template'\n                    }));\n                // Combine API templates with default templates, avoiding duplicates\n                const combinedTemplates = [\n                    ...defaultTemplates,\n                    ...apiTemplates.filter((apiTemplate)=>!defaultTemplates.some((defaultTemplate)=>defaultTemplate.name === apiTemplate.name))\n                ];\n                console.log('Loaded templates (API + defaults):', combinedTemplates);\n                setTemplates(combinedTemplates);\n            } else {\n                console.log('No templates from API, using default templates');\n                setTemplates(defaultTemplates);\n            }\n            setLoadingTemplates(false);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response;\n                console.error('Templates API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            }\n            setError(\"Failed to load templates. Please refresh the page.\");\n            setLoadingTemplates(false);\n        }\n    };\n    const handleCreateEInvite = (template)=>{\n        if (template) {\n            // Create a new e-invite based on the selected template\n            const newEInvite = {\n                website_id: '',\n                title: 'Our Wedding E-Invite',\n                couple_names: 'Eiyana & Warlin',\n                template_id: template.template_id,\n                wedding_date: '',\n                wedding_location: 'THE LITTLE HOTEL, 888, Glenport Road, Australia',\n                about_couple: '',\n                deployed_url: '',\n                is_published: false,\n                created_at: '',\n                updated_at: '',\n                template_name: template.name,\n                template_thumbnail: template.thumbnail_url,\n                design_settings: {\n                    colors: {\n                        primary: \"#B31B1E\",\n                        secondary: \"#333333\",\n                        background: \"#FFFFFF\",\n                        text: \"#000000\"\n                    },\n                    fonts: {\n                        heading: \"Playfair Display\",\n                        body: \"Open Sans\",\n                        coupleNames: \"Dancing Script\",\n                        date: \"Open Sans\",\n                        location: \"Open Sans\",\n                        aboutCouple: \"Open Sans\"\n                    },\n                    customImage: template.thumbnail_url,\n                    couple_names: 'Eiyana & Warlin',\n                    type: 'einvite'\n                }\n            };\n            setEditingEInvite(newEInvite);\n        } else {\n            setEditingEInvite(null);\n        }\n        setShowEditor(true);\n    };\n    const handleEditEInvite = (einvite)=>{\n        setEditingEInvite(einvite);\n        setShowEditor(true);\n    };\n    const handleSaveEInvite = async (einviteData)=>{\n        try {\n            setLoading(true);\n            const token = localStorage.getItem('token');\n            if (!token) {\n                setError(\"Authentication required. Please log in.\");\n                setLoading(false);\n                return;\n            }\n            // Check if templates are loaded\n            if (templates.length === 0) {\n                setError(\"Templates not loaded. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Ensure we have a valid template_id and REJECT any \"default-\" IDs\n            let templateId = einviteData.template_id;\n            // REJECT any \"default-\" template IDs completely\n            if (templateId && templateId.includes('default-')) {\n                console.error('REJECTING invalid template ID:', templateId);\n                templateId = null;\n            }\n            if (!templateId && templates.length > 0) {\n                templateId = templates[0].template_id;\n                console.log('No valid template_id provided, using first template:', templateId);\n            }\n            if (!templateId) {\n                setError(\"No valid template available. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Double check the template ID is valid UUID format\n            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;\n            if (!uuidRegex.test(templateId)) {\n                console.error('Template ID is not valid UUID format:', templateId);\n                setError(\"Invalid template format. Please refresh the page and try again.\");\n                setLoading(false);\n                return;\n            }\n            // Find the selected template to include its thumbnail\n            const selectedTemplate = templates.find((t)=>t.template_id === templateId);\n            // Prepare the data with template information - EXACTLY like website save\n            const completeEInviteData = {\n                ...einviteData,\n                template_id: templateId,\n                template_name: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) || '',\n                template_thumbnail: '',\n                // Add the type marker in design_settings\n                design_settings: {\n                    ...einviteData.design_settings,\n                    type: 'einvite' // This marks it as an e-invite\n                }\n            };\n            console.log('Saving e-invite with data:', completeEInviteData);\n            console.log('Template ID being used:', completeEInviteData.template_id);\n            console.log('Available templates:', templates.map((t)=>({\n                    id: t.template_id,\n                    name: t.name\n                })));\n            if (editingEInvite && editingEInvite.website_id) {\n                // Update existing e-invite - EXACTLY like website update\n                const updateData = {\n                    ...completeEInviteData,\n                    website_id: editingEInvite.website_id\n                };\n                console.log('Updating with data:', updateData);\n                await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website', updateData, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                setSuccessMessage(\"E-Invite updated successfully\");\n            } else {\n                // Create new e-invite - EXACTLY like website create\n                console.log('Creating new e-invite with data:', completeEInviteData);\n                // Check if we have a valid template_id\n                if (!completeEInviteData.template_id) {\n                    throw new Error('No template_id available. Please select a template.');\n                }\n                // Create a minimal test payload first\n                const minimalPayload = {\n                    title: completeEInviteData.title || 'Test E-Invite',\n                    template_id: completeEInviteData.template_id,\n                    wedding_date: completeEInviteData.wedding_date,\n                    wedding_location: completeEInviteData.wedding_location || '',\n                    about_couple: completeEInviteData.about_couple || '',\n                    couple_names: completeEInviteData.couple_names || '',\n                    design_settings: completeEInviteData.design_settings || {},\n                    is_published: true,\n                    template_name: completeEInviteData.template_name || '',\n                    template_thumbnail: ''\n                };\n                console.log('Minimal payload:', minimalPayload);\n                const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post('https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website', minimalPayload, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token),\n                        'Content-Type': 'application/json'\n                    }\n                });\n                console.log('Create response:', response.data);\n                setSuccessMessage(\"E-Invite created successfully! You can create another one or edit this template.\");\n            }\n            // Don't refresh e-invites list since we're not showing them\n            // Don't close the editor - stay on template selection\n            setLoading(false);\n        } catch (error) {\n            console.error('Error saving e-invite:', error);\n            if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n                var _error_response, _error_response_data, _error_response1;\n                console.error('API Error details:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                setError(\"Failed to save e-invite: \".concat(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message));\n            } else {\n                setError(\"Failed to save e-invite. Please try again.\");\n            }\n            setLoading(false);\n        }\n    };\n    const handleDeleteEInvite = async (einviteId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this e-invite?\")) {\n            return;\n        }\n        try {\n            setDeletingId(einviteId);\n            const token = getAuthToken();\n            if (!token) {\n                setError(\"Authentication required\");\n                setDeletingId(null);\n                return;\n            }\n            await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                },\n                data: {\n                    website_id: einviteId\n                }\n            });\n            setSuccessMessage(\"E-Invite deleted successfully!\");\n            fetchEInvites();\n            setDeletingId(null);\n        } catch (error) {\n            console.error('Error deleting e-invite:', error);\n            setError(\"Failed to delete e-invite. Please try again.\");\n            setDeletingId(null);\n        }\n    };\n    const handleShareEInvite = async (einvite)=>{\n        try {\n            // Generate sharing link (viewable by anyone, but encourages login for RSVP)\n            const shareUrl = \"\".concat(window.location.origin, \"/e-invite/\").concat(einvite.website_id);\n            // Copy to clipboard\n            await navigator.clipboard.writeText(shareUrl);\n            setSuccessMessage(\"E-Invite link copied to clipboard! Anyone can view it, and they'll be encouraged to join Wedzat to RSVP.\");\n        } catch (error) {\n            console.error('Error sharing e-invite:', error);\n            setError(\"Failed to copy link. Please try again.\");\n        }\n    };\n    const handlePreviewEInvite = (einvite)=>{\n        // Open preview in new tab\n        const previewUrl = \"/e-invite/\".concat(einvite.website_id);\n        window.open(previewUrl, '_blank');\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'N/A';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    };\n    if (showEditor) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EInviteEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            templates: templates,\n            einvite: editingEInvite,\n            onSave: handleSaveEInvite,\n            onCancel: ()=>{\n                setShowEditor(false);\n                setEditingEInvite(null);\n            },\n            loading: loading\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n            lineNumber: 502,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gray-200 rounded flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-4 h-4 bg-gray-400 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-black\",\n                            children: [\n                                \"Our Most Trending \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#B31B1E]\",\n                                    children: \"Invites\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 31\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined),\n            loadingEInvites || loadingTemplates ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 32,\n                    className: \"animate-spin text-[#B31B1E]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 530,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8\",\n                        children: templates.length > 0 ? templates.slice(0, 8).map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group cursor-pointer\",\n                                onClick: ()=>handleCreateEInvite(template),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: template.thumbnail_url || 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template',\n                                            alt: template.name,\n                                            className: \"w-full h-full object-cover\",\n                                            onError: (e)=>{\n                                                const target = e.target;\n                                                target.onerror = null;\n                                                target.src = 'https://via.placeholder.com/300x400/f3f4f6/9ca3af?text=Template';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-0 h-0 border-l-[8px] border-l-gray-600 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Plus_Share2_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    \"Customise\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, template.template_id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 17\n                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-full text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Loading templates...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleCreateEInvite(),\n                                    className: \"px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors\",\n                                    children: \"Create E-Invite\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, undefined),\n                     false && /*#__PURE__*/ 0\n                ]\n            }, void 0, true),\n            error && !error.includes(\"Authentication\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-red-100 text-red-700 rounded-md\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 698,\n                columnNumber: 9\n            }, undefined),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-2 bg-green-100 text-green-700 rounded-md\",\n                children: successMessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n                lineNumber: 705,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\app\\\\home\\\\e-invites\\\\components\\\\EInvites.tsx\",\n        lineNumber: 516,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EInvites, \"Gi3ldhoSg2oZqGGlxEvm0L3Q6IA=\");\n_c = EInvites;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EInvites);\nvar _c;\n$RefreshReg$(_c, \"EInvites\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home/<USER>/components/EInvites.tsx\n"));

/***/ })

});