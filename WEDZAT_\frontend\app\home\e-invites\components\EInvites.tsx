"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { Loader2, Plus, Edit, Trash2, Share2, Eye } from "lucide-react";
import EInviteEditor from "./EInviteEditor";

interface EInvite {
  website_id: string;
  title: string;
  couple_names?: string;
  template_id: string;
  wedding_date: string;
  wedding_location: string;
  about_couple: string;
  deployed_url: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
  template_name: string;
  template_thumbnail: string;
  design_settings?: {
    colors?: {
      primary: string;
      secondary: string;
      background: string;
      text: string;
    };
    fonts?: {
      heading: string;
      body: string;
      coupleNames?: string;
      date?: string;
      location?: string;
      aboutCouple?: string;
    };
    customImage?: string;
    couple_names?: string;
    type?: string; // 'einvite' to distinguish from regular websites
  };
}

interface Template {
  template_id: string;
  name: string;
  thumbnail_url: string;
  description: string;
  default_colors: any;
  default_fonts: any;
}

interface EInvitesProps {
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  setLoading: (loading: boolean) => void;
  loading: boolean;
  error: string | null;
  successMessage: string | null;
}

const EInvites: React.FC<EInvitesProps> = ({
  setError,
  setSuccessMessage,
  setLoading,
  loading,
  error,
  successMessage
}) => {
  const [einvites, setEInvites] = useState<EInvite[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showEditor, setShowEditor] = useState(false);
  const [editingEInvite, setEditingEInvite] = useState<EInvite | null>(null);
  const [loadingEInvites, setLoadingEInvites] = useState(true);
  const [loadingTemplates, setLoadingTemplates] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  useEffect(() => {
    fetchEInvites();
    fetchTemplates();
  }, []);

  const getAuthToken = () => {
    return localStorage.getItem('authToken');
  };

  const fetchEInvites = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        setError("Authentication required");
        setLoadingEInvites(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/user-websites',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.websites) {
        // Filter for e-invites (websites marked with type='einvite' in design_settings)
        const einvites = response.data.websites.filter((website: any) =>
          website.design_settings?.type === 'einvite'
        );
        setEInvites(einvites);
      }
      setLoadingEInvites(false);
    } catch (error) {
      console.error('Error fetching e-invites:', error);
      setError("Failed to load e-invites. Please try again.");
      setLoadingEInvites(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        setError("Authentication required");
        setLoadingTemplates(false);
        return;
      }

      const response = await axios.get(
        'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/website-templates',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.templates) {
        const processedTemplates = response.data.templates.map((template: any) => ({
          ...template,
          thumbnail_url: template.thumbnail_url || '/placeholder-template.jpg'
        }));
        setTemplates(processedTemplates);
      }
      setLoadingTemplates(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      setError("Failed to load templates. Please try again.");
      setLoadingTemplates(false);
    }
  };

  const handleCreateEInvite = () => {
    setEditingEInvite(null);
    setShowEditor(true);
  };

  const handleEditEInvite = (einvite: EInvite) => {
    setEditingEInvite(einvite);
    setShowEditor(true);
  };

  const handleSaveEInvite = async (einviteData: any) => {
    try {
      setLoading(true);
      const token = getAuthToken();

      if (!token) {
        setError("Authentication required");
        setLoading(false);
        return;
      }

      // Mark this as an e-invite
      const dataWithType = {
        ...einviteData,
        design_settings: {
          ...einviteData.design_settings,
          type: 'einvite'
        }
      };

      let response;
      if (editingEInvite) {
        // Update existing e-invite using website API
        response = await axios.put(
          `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/update-website`,
          {
            website_id: editingEInvite.website_id,
            ...dataWithType
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      } else {
        // Create new e-invite using website API
        response = await axios.post(
          'https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/create-website',
          dataWithType,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }

      if (response.data) {
        setSuccessMessage(editingEInvite ? "E-Invite updated successfully!" : "E-Invite created successfully!");
      }

      // Refresh the e-invites list
      fetchEInvites();

      // Close the editor
      setShowEditor(false);
      setEditingEInvite(null);
      setLoading(false);
    } catch (error) {
      console.error('Error saving e-invite:', error);
      setError("Failed to save e-invite. Please try again.");
      setLoading(false);
    }
  };

  const handleDeleteEInvite = async (einviteId: string) => {
    if (!window.confirm("Are you sure you want to delete this e-invite?")) {
      return;
    }

    try {
      setDeletingId(einviteId);
      const token = getAuthToken();

      if (!token) {
        setError("Authentication required");
        setDeletingId(null);
        return;
      }

      await axios.delete(
        `https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/tool/delete-website`,
        {
          headers: { Authorization: `Bearer ${token}` },
          data: { website_id: einviteId }
        }
      );

      setSuccessMessage("E-Invite deleted successfully!");
      fetchEInvites();
      setDeletingId(null);
    } catch (error) {
      console.error('Error deleting e-invite:', error);
      setError("Failed to delete e-invite. Please try again.");
      setDeletingId(null);
    }
  };

  const handleShareEInvite = async (einvite: EInvite) => {
    try {
      // Generate auth-protected sharing link
      const shareUrl = `${window.location.origin}/e-invite/${einvite.website_id}`;

      // Copy to clipboard
      await navigator.clipboard.writeText(shareUrl);
      setSuccessMessage("E-Invite link copied to clipboard! Recipients will need to login to view.");
    } catch (error) {
      console.error('Error sharing e-invite:', error);
      setError("Failed to copy link. Please try again.");
    }
  };

  const handlePreviewEInvite = (einvite: EInvite) => {
    // Open preview in new tab
    const previewUrl = `/e-invite/${einvite.website_id}`;
    window.open(previewUrl, '_blank');
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (showEditor) {
    return (
      <EInviteEditor
        templates={templates}
        einvite={editingEInvite as any}
        onSave={handleSaveEInvite}
        onCancel={() => {
          setShowEditor(false);
          setEditingEInvite(null);
        }}
        loading={loading}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-black">Your E-Invites</h2>
        <button
          onClick={handleCreateEInvite}
          className="flex items-center gap-2 px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
          disabled={loading}
        >
          <Plus size={20} />
          Create E-Invite
        </button>
      </div>

      {loadingEInvites || loadingTemplates ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 size={32} className="animate-spin text-[#B31B1E]" />
        </div>
      ) : einvites.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600 mb-4">You haven't created any e-invites yet.</p>
          <button
            onClick={handleCreateEInvite}
            className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Create Your First E-Invite
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {einvites.map((einvite) => (
            <div 
              key={einvite.website_id} 
              className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 relative"
              onMouseEnter={() => setHoveredCard(einvite.website_id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div className="h-40 bg-gray-200 relative">
                <img
                  src={einvite.design_settings?.customImage || einvite.template_thumbnail || '/placeholder-template.jpg'}
                  alt={einvite.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src = '/placeholder-template.jpg';
                  }}
                />
                
                {/* Hover overlay with "Customize" button */}
                {hoveredCard === einvite.website_id && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <button
                      onClick={() => handleEditEInvite(einvite)}
                      className="px-4 py-2 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                      Customize
                    </button>
                  </div>
                )}
                
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
                  <h3 className="font-semibold truncate">{einvite.title}</h3>
                  <p className="text-sm opacity-80">{einvite.template_name}</p>
                </div>
              </div>
              
              <div className="p-4">
                <div className="mb-3">
                  <p className="text-sm text-gray-600 truncate">
                    <span className="font-semibold">Couple:</span> {einvite.couple_names || einvite.design_settings?.couple_names || 'Jane & John'}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-semibold">Wedding Date:</span> {formatDate(einvite.wedding_date)}
                  </p>
                  <p className="text-sm text-gray-600 truncate">
                    <span className="font-semibold">Location:</span> {einvite.wedding_location || 'TBD'}
                  </p>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleEditEInvite(einvite)}
                      className="p-2 text-gray-600 hover:text-[#B31B1E] transition-colors"
                      title="Edit E-Invite"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleDeleteEInvite(einvite.website_id)}
                      className="p-2 text-gray-600 hover:text-red-600 transition-colors"
                      title="Delete E-Invite"
                      disabled={deletingId === einvite.website_id}
                    >
                      {deletingId === einvite.website_id ? (
                        <Loader2 size={18} className="animate-spin" />
                      ) : (
                        <Trash2 size={18} />
                      )}
                    </button>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => handlePreviewEInvite(einvite)}
                      className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                      title="Preview E-Invite"
                    >
                      <Eye size={16} />
                      <span>Preview</span>
                    </button>
                    <button
                      onClick={() => handleShareEInvite(einvite)}
                      className="flex items-center gap-1 px-3 py-1 bg-[#B31B1E] text-white rounded-md hover:bg-red-700 transition-colors"
                      title="Share E-Invite"
                    >
                      <Share2 size={16} />
                      <span>Share</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Success message */}
      {successMessage && (
        <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default EInvites;
