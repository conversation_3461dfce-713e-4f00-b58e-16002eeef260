"use client";
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useIsMobile from '../../hooks/useIsMobile';
import MobileConversationList from './mobile-list';

export default function MobileMessagesPage() {
  const isMobile = useIsMobile();
  const router = useRouter();

  useEffect(() => {
    if (isMobile === false) {
      router.replace('/messages');
    }
  }, [isMobile, router]);

  if (isMobile === null) return null; // Wait for hydration
  if (isMobile === false) return null; // Redirecting to desktop

  return <MobileConversationList />;
} 