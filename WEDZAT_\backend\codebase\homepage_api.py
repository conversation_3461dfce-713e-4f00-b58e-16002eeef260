import os
import json
import jwt
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database and JWT configuration
DB_HOST = os.getenv('DB_HOST')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
JWT_SECRET = os.getenv('JWT_SECRET')

def get_db_connection():
    return psycopg2.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        port=DB_PORT,
        dbname=DB_NAME,
        cursor_factory=RealDictCursor  # Return results as dictionaries
    )

def validate_token(headers):
    """Validate the JWT token from the authorization header"""
    token = headers.get('Authorization')

    if not token:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token is required"})
        }

    try:
        token = token.split()[1]
        data = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return data['user_id'], None
    except jwt.ExpiredSignatureError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Token expired"})
        }
    except jwt.InvalidTokenError:
        return None, {
            'statusCode': 401,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": "Invalid token"})
        }

def check_face_verification(event):
    """
    Check if a user has completed face verification
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get face verification status
            cursor.execute("SELECT face_verified FROM users WHERE user_id = %s", (user_id,))
            result = cursor.fetchone()

            if not result:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "User not found"})
                }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "face_verified": result['face_verified']
                })
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_photos(event):
    """
    Get photos (photos only, excluding stories) with pagination
    Each page contains 10 items
    Enriched with user stats, views, likes, and follow status using existing APIs
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media m
                WHERE m.media_type = 'photo' AND m.is_story = false
            """)
            total_count = cursor.fetchone()['total_count']

            # Get all photos with pagination
            photos_query = """
                SELECT
                    m.media_id,
                    m.media_url,
                    m.created_at,
                    m.user_id,
                    u.name AS user_name,
                    u.user_avatar,
                    mpd.caption,
                    mpd.place,
                    mpd.event_type,
                    CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                FROM media m
                JOIN media_photo_details mpd ON m.media_id = mpd.media_id
                JOIN users u ON m.user_id = u.user_id
                WHERE m.media_type = 'photo' AND m.is_story = false
                ORDER BY m.created_at DESC
                LIMIT 10 OFFSET %s
            """

            cursor.execute(photos_query, (user_id, offset))
            photos = cursor.fetchall()

            # Enrich each photo with user stats, views, like status, follow status
            enriched_photos = []
            for photo in photos:
                photo_user_id = photo['user_id']
                content_id = photo['media_id']

                # Follower/following counts - handle potential null results
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (photo_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (photo_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                if user_id != photo_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, photo_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Photo views count - handle if no record exists (return 0)
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status - handle if no record exists (return False)
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count for the photo - handle if no likes exist (return 0)
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count_result = cursor.fetchone()
                likes_count = likes_count_result['count'] if likes_count_result else 0

                enriched_photos.append({
                    "media_id": content_id,
                    "media_url": photo['media_url'],
                    "caption": photo['caption'],
                    "place": photo['place'],
                    "event_type": photo['event_type'],
                    "created_at": photo['created_at'],
                    "user_id": photo_user_id,
                    "user_name": photo['user_name'],
                    "user_avatar": photo['user_avatar'],
                    "is_own_content": photo['is_own_content'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            # Prepare final response
            has_next_page = (offset + len(photos)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "photos": enriched_photos,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_photo(event):
    """
    Get individual photo details by media_id
    Enriched with user stats, views, likes, and follow status using existing APIs
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get photo ID from path parameters - handle both Lambda and direct calls
        photo_id = None
        if 'pathParameters' in event and event['pathParameters']:
            photo_id = event['pathParameters'].get('photo_id')
        elif 'photo_id' in event:
            photo_id = event['photo_id']

        # Also try to extract from resource path if available
        if not photo_id and 'requestContext' in event:
            resource_path = event['requestContext'].get('resourcePath', '')
            if '/hub/photo/' in resource_path:
                photo_id = resource_path.split('/hub/photo/')[-1]

        if not photo_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "Photo ID is required"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get photo details
            cursor.execute("""
                SELECT
                    m.media_id,
                    m.media_url,
                    m.created_at,
                    m.user_id,
                    u.name AS username,
                    u.user_avatar,
                    mpd.caption,
                    mpd.place,
                    mpd.event_type,
                    CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                FROM media m
                JOIN media_photo_details mpd ON m.media_id = mpd.media_id
                JOIN users u ON m.user_id = u.user_id
                WHERE m.media_id = %s AND m.media_type = 'photo'
            """, (user_id, photo_id))

            photo = cursor.fetchone()

            if not photo:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Photo not found"})
                }

            photo_user_id = photo['user_id']
            content_id = photo['media_id']

            # Follower/following counts - handle potential null results
            cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (photo_user_id,))
            followers_result = cursor.fetchone()
            followers_count = followers_result['count'] if followers_result else 0

            cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (photo_user_id,))
            following_result = cursor.fetchone()
            following_count = following_result['count'] if following_result else 0

            # Is current user following this user?
            if user_id != photo_user_id:
                cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, photo_user_id))
                is_following = cursor.fetchone() is not None
            else:
                is_following = False

            # Photo views count - handle if no record exists (return 0)
            cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
            views_result = cursor.fetchone()
            views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

            # User like status - handle if no record exists (return False)
            cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
            like_result = cursor.fetchone()
            is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

            # Total likes count for the photo - handle if no likes exist (return 0)
            cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
            likes_count_result = cursor.fetchone()
            likes_count = likes_count_result['count'] if likes_count_result else 0

            # Format result
            result = {
                "media_id": content_id,
                "media_url": photo['media_url'],
                "caption": photo['caption'],
                "place": photo['place'],
                "event_type": photo['event_type'],
                "created_at": photo['created_at'].isoformat() if photo['created_at'] else None,
                "user_id": photo_user_id,
                "username": photo['username'],
                "user_avatar": photo['user_avatar'],
                "is_own_content": photo['is_own_content'],
                "followers_count": followers_count,
                "following_count": following_count,
                "is_following": is_following,
                "views_count": views_count,
                "likes_count": likes_count,
                "is_liked": is_liked
            }

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(result, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_my_stories(event):
    """
    Get all stories (videos and photos) for the authenticated user without pagination
    Returns all stories that belong to the user
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get user's own stories (both videos and photos) without pagination
            user_stories_query = """
                (
                    SELECT
                        v.video_id AS content_id,
                        v.video_name AS content_name,
                        v.video_url AS content_url,
                        v.video_description AS content_description,
                        v.video_thumbnail AS thumbnail_url,
                        v.video_duration AS duration,
                        v.created_at,
                        u.name AS user_name,
                        u.user_avatar AS user_avatar,
                        v.user_id,
                        'video' AS content_type,
                        TRUE AS is_own_content
                    FROM videos v
                    JOIN users u ON v.user_id = u.user_id
                    WHERE v.video_subtype = 'story' AND v.user_id = %s
                )
                UNION ALL
                (
                    SELECT
                        p.photo_id AS content_id,
                        p.photo_name AS content_name,
                        p.photo_url AS content_url,
                        p.photo_description AS content_description,
                        NULL AS thumbnail_url,
                        NULL AS duration,
                        p.created_at,
                        u.name AS user_name,
                        u.user_avatar AS user_avatar,
                        p.user_id,
                        'photo' AS content_type,
                        TRUE AS is_own_content
                    FROM photos p
                    JOIN users u ON p.user_id = u.user_id
                    WHERE p.photo_subtype = 'story' AND p.user_id = %s
                )
                ORDER BY created_at DESC
            """

            # Execute query to get all user's stories
            cursor.execute(user_stories_query, (user_id, user_id))
            user_stories = cursor.fetchall()

            # Get total count of user's stories
            total_count = len(user_stories)

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": user_stories,
                    "total_count": total_count
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
        
def get_stories(event):
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 items
    Uses new media tables and enriches each story with user follow stats, story views, and like status.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of stories (last 24h)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media
                WHERE is_story = TRUE AND created_at >= NOW() - INTERVAL '24 HOURS'
            """)
            total_count = cursor.fetchone()['total_count']

            # Get user's own stories (no limit/offset for these)
            user_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    TRUE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id = %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
            """
            cursor.execute(user_stories_query, (user_id,))
            user_stories = cursor.fetchall()

            # Get other users' stories (with pagination)
            other_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id != %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
                LIMIT %s OFFSET %s
            """
            remaining_slots = 10 - len(user_stories)
            other_stories = []
            if remaining_slots > 0:
                cursor.execute(other_stories_query, (user_id, remaining_slots, offset))
                other_stories = cursor.fetchall()

            all_stories = list(user_stories) + list(other_stories)

            # Enrich each story with user stats, views, like status, follow status
            enriched_stories = []
            for story in all_stories:
                story_user_id = story['user_id']
                content_id = story['content_id']

                # Follower/following counts - handle potential null results
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                if user_id != story_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Story views count - handle if no record exists (return 0)
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status - handle if no record exists (return False)
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count for the story - handle if no likes exist (return 0)
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count_result = cursor.fetchone()
                likes_count = likes_count_result['count'] if likes_count_result else 0

                enriched_stories.append({
                    "content_id": content_id,
                    "content_type": story['media_type'],
                    "content_url": story['media_url'],
                    "thumbnail_url": story['thumbnail_url'],
                    "created_at": story['created_at'],
                    "user_name": story['user_name'],
                    "user_avatar": story['user_avatar'],
                    "user_id": story_user_id,
                    "is_own_content": story['is_own_content'],
                    "content_name": story['content_name'],
                    "content_description": story['content_description'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            has_next_page = (offset + len(all_stories)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": enriched_stories,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            print(f"Database error details: {str(e)}")  # Added for debugging
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"General error details: {str(e)}")  # Added for debugging
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 users with all their moments from last 24 hours.
    Users are ordered by their latest upload time and won't repeat across pages.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of unique users with stories (last 24h only)
            cursor.execute("""
                SELECT COUNT(DISTINCT m.user_id) as total_users
                FROM media m
                WHERE m.is_story = TRUE 
                AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                AND m.created_at <= NOW()
            """)
            total_users = cursor.fetchone()['total_users']

            # Check if current user has stories in last 24h
            cursor.execute("""
                SELECT COUNT(*) as user_story_count
                FROM media
                WHERE is_story = TRUE 
                AND user_id = %s 
                AND created_at >= NOW() - INTERVAL '24 HOURS'
                AND created_at <= NOW()
            """, (user_id,))
            user_has_stories = cursor.fetchone()['user_story_count'] > 0

            # Initialize the grouped stories structure
            grouped_stories = []

            # Get user's own stories first (if they exist and we're on page 1)
            if user_has_stories and page == 1:
                user_stories_query = """
                    SELECT
                        m.media_id AS content_id,
                        m.media_type,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        u.name AS user_name,
                        u.user_avatar,
                        m.user_id,
                        TRUE AS is_own_content,
                        m.media_type AS content_name,
                        NULL AS content_description
                    FROM media m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.is_story = TRUE 
                    AND m.user_id = %s 
                    AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    AND m.created_at <= NOW()
                    ORDER BY m.created_at DESC
                """
                cursor.execute(user_stories_query, (user_id,))
                user_stories = cursor.fetchall()
                
                if user_stories:
                    # Get user stats for current user
                    cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (user_id,))
                    followers_result = cursor.fetchone()
                    followers_count = followers_result['count'] if followers_result else 0

                    cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (user_id,))
                    following_result = cursor.fetchone()
                    following_count = following_result['count'] if following_result else 0

                    # Process each story for the current user
                    user_story_list = []
                    for story in user_stories:
                        content_id = story['content_id']
                        
                        # Story views count
                        cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                        views_result = cursor.fetchone()
                        views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                        # User like status
                        cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                        like_result = cursor.fetchone()
                        is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                        # Total likes count for the story
                        cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                        likes_count_result = cursor.fetchone()
                        likes_count = likes_count_result['count'] if likes_count_result else 0

                        user_story_list.append({
                            "content_id": content_id,
                            "content_type": story['media_type'],
                            "content_url": story['media_url'],
                            "thumbnail_url": story['thumbnail_url'],
                            "created_at": story['created_at'],
                            "content_name": story['content_name'],
                            "content_description": story['content_description'],
                            "views_count": views_count,
                            "likes_count": likes_count,
                            "is_liked": is_liked
                        })

                    # Add current user's group to the response
                    grouped_stories.append({
                        "user_id": user_id,
                        "user_name": user_stories[0]['user_name'],
                        "user_avatar": user_stories[0]['user_avatar'],
                        "is_own_content": True,
                        "followers_count": followers_count,
                        "following_count": following_count,
                        "is_following": False,
                        "stories": user_story_list
                    })

            # Calculate how many other users we need
            users_needed = 10
            other_users_offset = offset
            
            if user_has_stories and page == 1:
                users_needed = 9  # We already have current user
                other_users_offset = 0  # Start from beginning for other users
            elif user_has_stories and page > 1:
                # For subsequent pages, we need to account for the current user being on page 1
                other_users_offset = offset - 1

            # Get other users' stories (users ordered by latest upload, no repetition across pages)
            other_users_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE 
                    AND m.user_id != %s 
                    AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    AND m.created_at <= NOW()
                    AND m.user_id IN (
                        SELECT user_id
                        FROM (
                            SELECT user_id, MAX(created_at) as latest_story
                            FROM media
                            WHERE is_story = TRUE 
                                AND user_id != %s 
                                AND created_at >= NOW() - INTERVAL '24 HOURS'
                                AND created_at <= NOW()
                            GROUP BY user_id
                            ORDER BY latest_story DESC
                            LIMIT %s OFFSET %s
                        ) latest_users
                    )
                ORDER BY (
                    SELECT MAX(created_at) 
                    FROM media m2 
                    WHERE m2.user_id = m.user_id 
                        AND m2.is_story = TRUE 
                        AND m2.created_at >= NOW() - INTERVAL '24 HOURS'
                        AND m2.created_at <= NOW()
                ) DESC, m.user_id, m.created_at DESC
            """
            cursor.execute(other_users_stories_query, (user_id, user_id, users_needed, other_users_offset))
            other_stories = cursor.fetchall()

            # Group other users' stories by user_id
            users_stories_dict = {}
            for story in other_stories:
                story_user_id = story['user_id']
                if story_user_id not in users_stories_dict:
                    users_stories_dict[story_user_id] = []
                users_stories_dict[story_user_id].append(story)

            # Process each user's stories
            for story_user_id, stories in users_stories_dict.items():
                # Get user stats
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                is_following = cursor.fetchone() is not None

                # Process each story for this user
                user_story_list = []
                for story in stories:
                    content_id = story['content_id']
                    
                    # Story views count
                    cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                    views_result = cursor.fetchone()
                    views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                    # User like status
                    cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                    like_result = cursor.fetchone()
                    is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                    # Total likes count for the story
                    cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                    likes_count_result = cursor.fetchone()
                    likes_count = likes_count_result['count'] if likes_count_result else 0

                    user_story_list.append({
                        "content_id": content_id,
                        "content_type": story['media_type'],
                        "content_url": story['media_url'],
                        "thumbnail_url": story['thumbnail_url'],
                        "created_at": story['created_at'],
                        "content_name": story['content_name'],
                        "content_description": story['content_description'],
                        "views_count": views_count,
                        "likes_count": likes_count,
                        "is_liked": is_liked
                    })

                # Add this user's group to the response
                grouped_stories.append({
                    "user_id": story_user_id,
                    "user_name": stories[0]['user_name'],
                    "user_avatar": stories[0]['user_avatar'],
                    "is_own_content": False,
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "stories": user_story_list
                })

            # Calculate pagination info
            total_pages = (total_users + 9) // 10  # Ceiling division
            has_next_page = page < total_pages

            # Calculate total stories count
            total_stories_count = sum(len(user_group['stories']) for user_group in grouped_stories)

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "users_stories": grouped_stories,
                    "next_page": has_next_page,
                    "total_stories_count": total_stories_count,
                    "total_users": total_users,
                    "total_pages": total_pages,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            print(f"Database error details: {str(e)}")  # Added for debugging
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"General error details: {str(e)}")  # Added for debugging
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 users with all their moments from last 24 hours.
    Users are ordered by their latest upload time and won't repeat across pages.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of unique users with stories (last 24h only)
            cursor.execute("""
                SELECT COUNT(DISTINCT m.user_id) as total_users
                FROM media m
                WHERE m.is_story = TRUE 
                AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                AND m.created_at <= NOW()
            """)
            total_users = cursor.fetchone()['total_users']

            # Check if current user has stories in last 24h
            cursor.execute("""
                SELECT COUNT(*) as user_story_count
                FROM media
                WHERE is_story = TRUE 
                AND user_id = %s 
                AND created_at >= NOW() - INTERVAL '24 HOURS'
                AND created_at <= NOW()
            """, (user_id,))
            user_has_stories = cursor.fetchone()['user_story_count'] > 0

            # Get user's own stories first (if they exist and we're on page 1)
            user_stories = []
            if user_has_stories and page == 1:
                user_stories_query = """
                    SELECT
                        m.media_id AS content_id,
                        m.media_type,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        u.name AS user_name,
                        u.user_avatar,
                        m.user_id,
                        TRUE AS is_own_content,
                        m.media_type AS content_name,
                        NULL AS content_description
                    FROM media m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.is_story = TRUE 
                    AND m.user_id = %s 
                    AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    AND m.created_at <= NOW()
                    ORDER BY m.created_at DESC
                """
                cursor.execute(user_stories_query, (user_id,))
                user_stories = cursor.fetchall()

            # Calculate how many other users we need
            users_needed = 10
            other_users_offset = offset
            
            if user_has_stories and page == 1:
                users_needed = 9  # We already have current user
                other_users_offset = 0  # Start from beginning for other users
            elif user_has_stories and page > 1:
                # For subsequent pages, we need to account for the current user being on page 1
                other_users_offset = offset - 1

            # Get other users' stories (users ordered by latest upload, no repetition across pages)
            # Only include stories from last 24 hours and group by user_id
            other_users_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE 
                    AND m.user_id != %s 
                    AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    AND m.created_at <= NOW()
                    AND m.user_id IN (
                        SELECT user_id
                        FROM (
                            SELECT user_id, MAX(created_at) as latest_story
                            FROM media
                            WHERE is_story = TRUE 
                                AND user_id != %s 
                                AND created_at >= NOW() - INTERVAL '24 HOURS'
                                AND created_at <= NOW()
                            GROUP BY user_id
                            ORDER BY latest_story DESC
                            LIMIT %s OFFSET %s
                        ) latest_users
                    )
                ORDER BY (
                    SELECT MAX(created_at) 
                    FROM media m2 
                    WHERE m2.user_id = m.user_id 
                        AND m2.is_story = TRUE 
                        AND m2.created_at >= NOW() - INTERVAL '24 HOURS'
                        AND m2.created_at <= NOW()
                ) DESC, m.user_id, m.created_at DESC
            """
            cursor.execute(other_users_stories_query, (user_id, user_id, users_needed, other_users_offset))
            other_stories = cursor.fetchall()

            # Combine user stories and other stories
            all_stories = list(user_stories) + list(other_stories)

            # Enrich each story with user stats, views, like status, follow status
            enriched_stories = []
            for story in all_stories:
                story_user_id = story['user_id']
                content_id = story['content_id']

                # Follower/following counts - handle potential null results
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                if user_id != story_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Story views count - handle if no record exists (return 0)
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status - handle if no record exists (return False)
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count for the story - handle if no likes exist (return 0)
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count_result = cursor.fetchone()
                likes_count = likes_count_result['count'] if likes_count_result else 0

                enriched_stories.append({
                    "content_id": content_id,
                    "content_type": story['media_type'],
                    "content_url": story['media_url'],
                    "thumbnail_url": story['thumbnail_url'],
                    "created_at": story['created_at'],
                    "user_name": story['user_name'],
                    "user_avatar": story['user_avatar'],
                    "user_id": story_user_id,
                    "is_own_content": story['is_own_content'],
                    "content_name": story['content_name'],
                    "content_description": story['content_description'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            # Calculate pagination info
            total_pages = (total_users + 9) // 10  # Ceiling division
            has_next_page = page < total_pages

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": enriched_stories,
                    "next_page": has_next_page,
                    "total_count": len(enriched_stories),
                    "total_users": total_users,
                    "total_pages": total_pages,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            print(f"Database error details: {str(e)}")  # Added for debugging
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"General error details: {str(e)}")  # Added for debugging
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 users with all their moments from last 24 hours.
    Users are ordered by their latest upload time and won't repeat across pages.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of unique users with stories (last 24h)
            cursor.execute("""
                SELECT COUNT(DISTINCT m.user_id) as total_users
                FROM media m
                WHERE m.is_story = TRUE AND m.created_at >= NOW() - INTERVAL '24 HOURS'
            """)
            total_users = cursor.fetchone()['total_users']

            # Check if current user has stories
            cursor.execute("""
                SELECT COUNT(*) as user_story_count
                FROM media
                WHERE is_story = TRUE AND user_id = %s AND created_at >= NOW() - INTERVAL '24 HOURS'
            """, (user_id,))
            user_has_stories = cursor.fetchone()['user_story_count'] > 0

            # Get user's own stories first (if they exist and we're on page 1)
            user_stories = []
            if user_has_stories and page == 1:
                user_stories_query = """
                    SELECT
                        m.media_id AS content_id,
                        m.media_type,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        u.name AS user_name,
                        u.user_avatar,
                        m.user_id,
                        TRUE AS is_own_content,
                        m.media_type AS content_name,
                        NULL AS content_description
                    FROM media m
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.is_story = TRUE AND m.user_id = %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    ORDER BY m.created_at DESC
                """
                cursor.execute(user_stories_query, (user_id,))
                user_stories = cursor.fetchall()

            # Calculate how many other users we need
            users_needed = 10
            other_users_offset = offset
            
            if user_has_stories and page == 1:
                users_needed = 9  # We already have current user
                other_users_offset = 0  # Start from beginning for other users
            elif user_has_stories and page > 1:
                # For subsequent pages, we need to account for the current user being on page 1
                other_users_offset = offset - 1

            # Get other users' stories (users ordered by latest upload, no repetition across pages)
            other_users_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE 
                    AND m.user_id != %s 
                    AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                    AND m.user_id IN (
                        SELECT user_id
                        FROM (
                            SELECT user_id, MAX(created_at) as latest_story
                            FROM media
                            WHERE is_story = TRUE 
                                AND user_id != %s 
                                AND created_at >= NOW() - INTERVAL '24 HOURS'
                            GROUP BY user_id
                            ORDER BY latest_story DESC
                            LIMIT %s OFFSET %s
                        ) latest_users
                    )
                ORDER BY (
                    SELECT MAX(created_at) 
                    FROM media m2 
                    WHERE m2.user_id = m.user_id 
                        AND m2.is_story = TRUE 
                        AND m2.created_at >= NOW() - INTERVAL '24 HOURS'
                ) DESC, m.created_at DESC
            """
            cursor.execute(other_users_stories_query, (user_id, user_id, users_needed, other_users_offset))
            other_stories = cursor.fetchall()

            # Combine user stories and other stories
            all_stories = list(user_stories) + list(other_stories)

            # Enrich each story with user stats, views, like status, follow status
            enriched_stories = []
            for story in all_stories:
                story_user_id = story['user_id']
                content_id = story['content_id']

                # Follower/following counts - handle potential null results
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                if user_id != story_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Story views count - handle if no record exists (return 0)
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status - handle if no record exists (return False)
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count for the story - handle if no likes exist (return 0)
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count_result = cursor.fetchone()
                likes_count = likes_count_result['count'] if likes_count_result else 0

                enriched_stories.append({
                    "content_id": content_id,
                    "content_type": story['media_type'],
                    "content_url": story['media_url'],
                    "thumbnail_url": story['thumbnail_url'],
                    "created_at": story['created_at'],
                    "user_name": story['user_name'],
                    "user_avatar": story['user_avatar'],
                    "user_id": story_user_id,
                    "is_own_content": story['is_own_content'],
                    "content_name": story['content_name'],
                    "content_description": story['content_description'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            # Calculate pagination info
            total_pages = (total_users + 9) // 10  # Ceiling division
            has_next_page = page < total_pages

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": enriched_stories,
                    "next_page": has_next_page,
                    "total_count": len(enriched_stories),
                    "total_users": total_users,
                    "total_pages": total_pages,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            print(f"Database error details: {str(e)}")  # Added for debugging
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"General error details: {str(e)}")  # Added for debugging
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 items
    Uses new media tables and enriches each story with user follow stats, story views, and like status.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of stories (last 24h)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media
                WHERE is_story = TRUE AND created_at >= NOW() - INTERVAL '24 HOURS'
            """)
            total_count = cursor.fetchone()['total_count']

            # Get user's own stories (no limit/offset for these)
            user_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    TRUE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id = %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
            """
            cursor.execute(user_stories_query, (user_id,))
            user_stories = cursor.fetchall()

            # Get other users' stories (with pagination)
            other_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id != %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
                LIMIT %s OFFSET %s
            """
            remaining_slots = 10 - len(user_stories)
            other_stories = []
            if remaining_slots > 0:
                cursor.execute(other_stories_query, (user_id, remaining_slots, offset))
                other_stories = cursor.fetchall()

            all_stories = list(user_stories) + list(other_stories)

            # Enrich each story with user stats, views, like status, follow status
            enriched_stories = []
            for story in all_stories:
                story_user_id = story['user_id']
                content_id = story['content_id']

                # Follower/following counts - handle potential null results
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_result = cursor.fetchone()
                followers_count = followers_result['count'] if followers_result else 0

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_result = cursor.fetchone()
                following_count = following_result['count'] if following_result else 0

                # Is current user following this user?
                if user_id != story_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Story views count - handle if no record exists (return 0)
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status - handle if no record exists (return False)
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count for the story - handle if no likes exist (return 0)
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count_result = cursor.fetchone()
                likes_count = likes_count_result['count'] if likes_count_result else 0

                enriched_stories.append({
                    "content_id": content_id,
                    "content_type": story['media_type'],
                    "content_url": story['media_url'],
                    "thumbnail_url": story['thumbnail_url'],
                    "created_at": story['created_at'],
                    "user_name": story['user_name'],
                    "user_avatar": story['user_avatar'],
                    "user_id": story_user_id,
                    "is_own_content": story['is_own_content'],
                    "content_name": story['content_name'],
                    "content_description": story['content_description'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            has_next_page = (offset + len(all_stories)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": enriched_stories,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            print(f"Database error details: {str(e)}")  # Added for debugging
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"General error details: {str(e)}")  # Added for debugging
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }
    """
    Get stories (videos or photos) with pagination, showing user's stories first
    Each page contains 10 items
    Uses new media tables and enriches each story with user follow stats, story views, and like status.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        if page < 1:
            page = 1
        offset = (page - 1) * 10

        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count of stories (last 24h)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media
                WHERE is_story = TRUE AND created_at >= NOW() - INTERVAL '24 HOURS'
            """)
            total_count = cursor.fetchone()['total_count']

            # Get user's own stories (no limit/offset for these)
            user_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    TRUE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id = %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
            """
            cursor.execute(user_stories_query, (user_id,))
            user_stories = cursor.fetchall()

            # Get other users' stories (with pagination)
            other_stories_query = """
                SELECT
                    m.media_id AS content_id,
                    m.media_type,
                    m.media_url,
                    m.thumbnail_url,
                    m.created_at,
                    u.name AS user_name,
                    u.user_avatar,
                    m.user_id,
                    FALSE AS is_own_content,
                    m.media_type AS content_name,
                    NULL AS content_description
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                WHERE m.is_story = TRUE AND m.user_id != %s AND m.created_at >= NOW() - INTERVAL '24 HOURS'
                ORDER BY m.created_at DESC
                LIMIT %s OFFSET %s
            """
            remaining_slots = 10 - len(user_stories)
            other_stories = []
            if remaining_slots > 0:
                cursor.execute(other_stories_query, (user_id, remaining_slots, offset))
                other_stories = cursor.fetchall()

            all_stories = list(user_stories) + list(other_stories)

            # Enrich each story with user stats, views, like status, follow status
            enriched_stories = []
            for story in all_stories:
                story_user_id = story['user_id']
                content_id = story['content_id']

                # Follower/following counts
                cursor.execute("SELECT COUNT(*) FROM follows WHERE followee_id = %s AND status = TRUE", (story_user_id,))
                followers_count = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM follows WHERE follower_id = %s AND status = TRUE", (story_user_id,))
                following_count = cursor.fetchone()[0]

                # Is current user following this user?
                if user_id != story_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, story_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Story views count
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                row = cursor.fetchone()
                views_count = row['view_count'] if row else 0

                # User like status
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_row = cursor.fetchone()
                is_liked = like_row['status'] if like_row else False

                enriched_stories.append({
                    "content_id": content_id,
                    "content_type": story['media_type'],
                    "content_url": story['media_url'],
                    "thumbnail_url": story['thumbnail_url'],
                    "created_at": story['created_at'],
                    "user_name": story['user_name'],
                    "user_avatar": story['user_avatar'],
                    "user_id": story_user_id,
                    "is_own_content": story['is_own_content'],
                    "content_name": story['content_name'],
                    "content_description": story['content_description'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "is_liked": is_liked
                })

            has_next_page = (offset + len(all_stories)) < total_count

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({
                    "stories": enriched_stories,
                    "next_page": has_next_page,
                    "total_count": total_count,
                    "current_page": page
                }, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }



# def get_flashes(event):
#     """
#     Get flashes (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'flash'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all flashes with pagination
#             flashes_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'flash'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(flashes_query, (user_id, offset))
#             flashes = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(flashes)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "flashes": flashes,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }

# def get_glimpses(event):
#     """
#     Get glimpses (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'glimpse'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all glimpses with pagination
#             glimpses_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'glimpse'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(glimpses_query, (user_id, offset))
#             glimpses = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(glimpses)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "glimpses": glimpses,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }

# def get_movies(event):
#     """
#     Get movies (videos only) with pagination
#     Each page contains 10 items
#     """
#     try:
#         # Validate token
#         user_id, error = validate_token(event.get('headers', {}))
#         if error:
#             return error

#         # Get page number from query parameters
#         query_params = event.get('queryStringParameters', {}) or {}
#         page = int(query_params.get('page', 1))
#         if page < 1:
#             page = 1

#         # Calculate offset
#         offset = (page - 1) * 10

#         # Connect to database
#         conn = get_db_connection()
#         cursor = conn.cursor()

#         try:
#             # Get total count for pagination
#             cursor.execute("""
#                 SELECT COUNT(*) as total_count
#                 FROM videos
#                 WHERE video_subtype = 'movie'
#             """)
#             total_count = cursor.fetchone()['total_count']

#             # Get all movies with pagination
#             movies_query = """
#                 SELECT
#                     v.video_id,
#                     v.video_name,
#                     v.video_url,
#                     v.video_description,
#                     v.video_tags,
#                     v.video_thumbnail,
#                     v.video_duration,
#                     v.video_category,
#                     v.created_at,
#                     u.name AS user_name,
#                     v.user_id,
#                     CASE WHEN v.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
#                     vs.video_views,
#                     vs.video_likes,
#                     vs.video_comments
#                 FROM videos v
#                 JOIN users u ON v.user_id = u.user_id
#                 LEFT JOIN video_stats vs ON v.video_id = vs.video_id
#                 WHERE v.video_subtype = 'movie'
#                 ORDER BY v.created_at DESC
#                 LIMIT 10 OFFSET %s
#             """

#             cursor.execute(movies_query, (user_id, offset))
#             movies = cursor.fetchall()

#             # Prepare final response
#             has_next_page = (offset + len(movies)) < total_count

#             return {
#                 'statusCode': 200,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({
#                     "movies": movies,
#                     "next_page": has_next_page,
#                     "total_count": total_count,
#                     "current_page": page
#                 }, cls=CustomJSONEncoder)
#             }

#         except Exception as e:
#             return {
#                 'statusCode': 500,
#                 'headers': {
#                     "Access-Control-Allow-Origin": "*",
#                     "Access-Control-Allow-Methods": "OPTIONS, GET",
#                     "Access-Control-Allow-Headers": "Content-Type, Authorization"
#                 },
#                 'body': json.dumps({"error": f"Database error: {str(e)}"})
#             }
#         finally:
#             cursor.close()
#             conn.close()

#     except Exception as e:
#         return {
#             'statusCode': 500,
#             'headers': {
#                 "Access-Control-Allow-Origin": "*",
#                 "Access-Control-Allow-Methods": "OPTIONS, GET",
#                 "Access-Control-Allow-Headers": "Content-Type, Authorization"
#             },
#             'body': json.dumps({"error": f"Internal server error: {str(e)}"})
#         }
def get_flashes(event):
    """
    Get 'flash' subtype videos (not stories) with pagination.
    Each page contains 10 items.
    Location-matching videos appear first, then other videos to fill the page.
    Enriched with user stats, views, likes, and follow status using existing APIs.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number and location from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination (only flash subtype videos)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media m
                WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'flash'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                flashes_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        CASE 
                            WHEN mvd.place IS NOT NULL AND LOWER(mvd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'flash'
                    ORDER BY 
                        location_priority ASC,
                        m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(flashes_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                flashes_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'flash'
                    ORDER BY m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(flashes_query, (user_id, offset))

            flashes = cursor.fetchall()

            # Enrich each flash with user stats, views, like status, follow status
            enriched_flashes = []
            for flash in flashes:
                flash_user_id = flash['user_id']
                content_id = flash['media_id']

                # Follower/following counts
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (flash_user_id,))
                followers_count = cursor.fetchone()['count']

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (flash_user_id,))
                following_count = cursor.fetchone()['count']

                # Is current user following this user?
                if user_id != flash_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, flash_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Flash views count
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count = cursor.fetchone()['count']

                enriched_flashes.append({
                    "media_id": content_id,
                    "media_url": flash['media_url'],
                    "thumbnail_url": flash['thumbnail_url'],
                    "caption": flash['caption'],
                    "location": flash['location'],
                    "event_type": flash['event_type'],
                    "video_type": flash['video_type'],
                    "partner": flash['partner'],
                    "wedding_style": flash['wedding_style'],
                    "budget": flash['budget'],
                    "created_at": flash['created_at'],
                    "user_id": flash_user_id,
                    "user_name": flash['user_name'],
                    "user_avatar": flash['user_avatar'],
                    "is_own_content": flash['is_own_content'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            has_next_page = (offset + len(flashes)) < total_count

            response_data = {
                "flashes": enriched_flashes,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }

            if location:
                response_data["filtered_by_location"] = location
                location_matches = sum(1 for flash in enriched_flashes 
                                       if flash.get('location') and 
                                       location.lower() in flash['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

def get_glimpses(event):
    """
    Get glimpses (videos only) with pagination
    Each page contains 10 items
    Location-matching videos appear first, then other videos to fill the page
    Can also filter by specific media_id
    Enriched with user stats, views, likes, and follow status
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get parameters from query string
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        media_id = query_params.get('media_id', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # If specific media_id is requested, return only that record
            if media_id:
                single_glimpse_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' 
                        AND m.subtype = 'glimpse' 
                        AND m.media_id = %s
                """
                cursor.execute(single_glimpse_query, (user_id, media_id))
                glimpse = cursor.fetchone()
                
                if not glimpse:
                    return {
                        'statusCode': 404,
                        'headers': {
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Methods": "OPTIONS, GET",
                            "Access-Control-Allow-Headers": "Content-Type, Authorization"
                        },
                        'body': json.dumps({"error": "Glimpse not found"})
                    }
                
                # Enrich the single glimpse with stats
                enriched_glimpse = enrich_single_glimpse(cursor, glimpse, user_id)
                
                return {
                    'statusCode': 200,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"glimpse": enriched_glimpse}, cls=CustomJSONEncoder)
                }

            # Get total count for pagination (all glimpse videos)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media
                WHERE media_type = 'video' AND subtype = 'glimpse'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                glimpses_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        CASE 
                            WHEN mvd.place IS NOT NULL AND LOWER(mvd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.subtype = 'glimpse'
                    ORDER BY 
                        location_priority ASC,
                        m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(glimpses_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                glimpses_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.subtype = 'glimpse'
                    ORDER BY m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(glimpses_query, (user_id, offset))

            glimpses = cursor.fetchall()

            # Enrich each glimpse with user stats, views, like status, follow status
            enriched_glimpses = []
            for glimpse in glimpses:
                enriched_glimpse = enrich_single_glimpse(cursor, glimpse, user_id)
                enriched_glimpses.append(enriched_glimpse)

            # Prepare final response
            has_next_page = (offset + len(glimpses)) < total_count

            response_data = {
                "glimpses": enriched_glimpses,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }
            
            # Add location info if filtered
            if location:
                response_data["filtered_by_location"] = location
                # Count how many location-matching videos are in this page
                location_matches = sum(1 for glimpse in enriched_glimpses 
                                     if glimpse.get('location') and 
                                     location.lower() in glimpse['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }


def enrich_single_glimpse(cursor, glimpse, user_id):
    """
    Helper function to enrich a single glimpse with user stats, views, likes, and follow status
    """
    glimpse_user_id = glimpse['user_id']
    content_id = glimpse['media_id']

    # Follower/following counts - handle potential null results
    cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (glimpse_user_id,))
    followers_result = cursor.fetchone()
    followers_count = followers_result['count'] if followers_result else 0

    cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (glimpse_user_id,))
    following_result = cursor.fetchone()
    following_count = following_result['count'] if following_result else 0

    # Is current user following this user?
    if user_id != glimpse_user_id:
        cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, glimpse_user_id))
        is_following = cursor.fetchone() is not None
    else:
        is_following = False

    # Glimpse views count - handle if no record exists (return 0)
    cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
    views_result = cursor.fetchone()
    views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

    # User like status - handle if no record exists (return False)
    cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
    like_result = cursor.fetchone()
    is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

    # Total likes count for the glimpse - handle if no likes exist (return 0)
    cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
    likes_count_result = cursor.fetchone()
    likes_count = likes_count_result['count'] if likes_count_result else 0

    return {
        "media_id": content_id,
        "media_url": glimpse['media_url'],
        "thumbnail_url": glimpse['thumbnail_url'],
        "caption": glimpse['caption'],
        "location": glimpse['location'],
        "event_type": glimpse['event_type'],
        "video_type": glimpse['video_type'],
        "partner": glimpse['partner'],
        "wedding_style": glimpse['wedding_style'],
        "budget": glimpse['budget'],
        "created_at": glimpse['created_at'],
        "user_id": glimpse_user_id,
        "user_name": glimpse['user_name'],
        "user_avatar": glimpse['user_avatar'],
        "is_own_content": glimpse['is_own_content'],
        "followers_count": followers_count,
        "following_count": following_count,
        "is_following": is_following,
        "views_count": views_count,
        "likes_count": likes_count,
        "is_liked": is_liked
    }

def get_movies(event):
    """
    Get 'movie' subtype videos (not stories) with pagination.
    Each page contains 10 items.
    Location-matching videos appear first, then other videos to fill the page.
    Enriched with user stats, views, likes, and follow status using existing APIs.
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get page number and location from query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        page = int(query_params.get('page', 1))
        location = query_params.get('location', '').strip()
        
        if page < 1:
            page = 1

        # Calculate offset
        offset = (page - 1) * 10

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get total count for pagination (only movie subtype videos)
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM media m
                WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'movie'
            """)
            total_count = cursor.fetchone()['total_count']

            # Build the main query with location prioritization
            if location:
                movies_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content,
                        CASE 
                            WHEN mvd.place IS NOT NULL AND LOWER(mvd.place) LIKE LOWER(%s) THEN 0 
                            ELSE 1 
                        END AS location_priority
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'movie'
                    ORDER BY 
                        location_priority ASC,
                        m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(movies_query, (user_id, f"%{location}%", offset))
            else:
                # No location filter - standard query
                movies_query = """
                    SELECT
                        m.media_id,
                        m.media_url,
                        m.thumbnail_url,
                        m.created_at,
                        m.user_id,
                        u.name AS user_name,
                        u.user_avatar,
                        mvd.caption,
                        mvd.place AS location,
                        mvd.event_type,
                        mvd.video_type,
                        mvd.partner,
                        mvd.wedding_style,
                        mvd.budget,
                        CASE WHEN m.user_id = %s THEN TRUE ELSE FALSE END AS is_own_content
                    FROM media m
                    JOIN media_video_details mvd ON m.media_id = mvd.media_id
                    JOIN users u ON m.user_id = u.user_id
                    WHERE m.media_type = 'video' AND m.is_story = false AND m.subtype = 'movie'
                    ORDER BY m.created_at DESC
                    LIMIT 10 OFFSET %s
                """
                cursor.execute(movies_query, (user_id, offset))

            movies = cursor.fetchall()

            # Enrich each movie with user stats, views, like status, follow status
            enriched_movies = []
            for movie in movies:
                movie_user_id = movie['user_id']
                content_id = movie['media_id']

                # Follower/following counts
                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE followee_id = %s AND status = TRUE", (movie_user_id,))
                followers_count = cursor.fetchone()['count']

                cursor.execute("SELECT COUNT(*) as count FROM follows WHERE follower_id = %s AND status = TRUE", (movie_user_id,))
                following_count = cursor.fetchone()['count']

                # Is current user following this user?
                if user_id != movie_user_id:
                    cursor.execute("SELECT 1 FROM follows WHERE follower_id = %s AND followee_id = %s AND status = TRUE", (user_id, movie_user_id))
                    is_following = cursor.fetchone() is not None
                else:
                    is_following = False

                # Movie views count
                cursor.execute("SELECT view_count FROM content_view_stats WHERE content_id = %s", (content_id,))
                views_result = cursor.fetchone()
                views_count = views_result['view_count'] if views_result and views_result['view_count'] is not None else 0

                # User like status
                cursor.execute("SELECT status FROM likes WHERE user_id = %s AND content_id = %s", (user_id, content_id))
                like_result = cursor.fetchone()
                is_liked = like_result['status'] if like_result and like_result['status'] is not None else False

                # Total likes count
                cursor.execute("SELECT COUNT(*) as count FROM likes WHERE content_id = %s AND status = TRUE", (content_id,))
                likes_count = cursor.fetchone()['count']

                enriched_movies.append({
                    "media_id": content_id,
                    "media_url": movie['media_url'],
                    "thumbnail_url": movie['thumbnail_url'],
                    "caption": movie['caption'],
                    "location": movie['location'],
                    "event_type": movie['event_type'],
                    "video_type": movie['video_type'],
                    "partner": movie['partner'],
                    "wedding_style": movie['wedding_style'],
                    "budget": movie['budget'],
                    "created_at": movie['created_at'],
                    "user_id": movie_user_id,
                    "user_name": movie['user_name'],
                    "user_avatar": movie['user_avatar'],
                    "is_own_content": movie['is_own_content'],
                    "followers_count": followers_count,
                    "following_count": following_count,
                    "is_following": is_following,
                    "views_count": views_count,
                    "likes_count": likes_count,
                    "is_liked": is_liked
                })

            has_next_page = (offset + len(movies)) < total_count

            response_data = {
                "movies": enriched_movies,
                "next_page": has_next_page,
                "total_count": total_count,
                "current_page": page
            }

            if location:
                response_data["filtered_by_location"] = location
                location_matches = sum(1 for movie in enriched_movies 
                                       if movie.get('location') and 
                                       location.lower() in movie['location'].lower())
                response_data["location_matches_in_page"] = location_matches

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }


def get_vendor_details(event):
    """
    Get vendor details for a specific media (video/photo)
    Returns vendor information that was entered during upload
    """
    try:
        # Validate token
        user_id, error = validate_token(event.get('headers', {}))
        if error:
            return error

        # Get media_id from path parameters
        path_params = event.get('pathParameters', {}) or {}
        media_id = path_params.get('media_id') or path_params.get('video_id')  # Support both for backward compatibility

        if not media_id:
            return {
                'statusCode': 400,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": "media_id is required"})
            }

        # Connect to database
        conn = get_db_connection()
        cursor = conn.cursor()

        try:
            # Get media metadata including vendor details
            media_query = """
                SELECT
                    m.media_id,
                    m.media_type,
                    m.media_url,
                    mvd.venue_name,
                    mvd.venue_contact,
                    mvd.photographer_name,
                    mvd.photographer_contact,
                    mvd.makeup_artist_name,
                    mvd.makeup_artist_contact,
                    mvd.decoration_name,
                    mvd.decoration_contact,
                    mvd.caterer_name,
                    mvd.caterer_contact,
                    mvd.additional_vendors,
                    m.created_at,
                    u.name AS user_name,
                    m.user_id,
                    CASE 
                        WHEN m.media_type = 'video' THEN mvd_video.caption
                        WHEN m.media_type = 'photo' THEN mpd.caption
                    END AS media_caption
                FROM media m
                JOIN users u ON m.user_id = u.user_id
                LEFT JOIN media_vendor_details mvd ON m.media_id = mvd.media_id
                LEFT JOIN media_video_details mvd_video ON m.media_id = mvd_video.media_id AND m.media_type = 'video'
                LEFT JOIN media_photo_details mpd ON m.media_id = mpd.media_id AND m.media_type = 'photo'
                WHERE m.media_id = %s
            """

            cursor.execute(media_query, (media_id,))
            media_data = cursor.fetchone()

            if not media_data:
                return {
                    'statusCode': 404,
                    'headers': {
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Methods": "OPTIONS, GET",
                        "Access-Control-Allow-Headers": "Content-Type, Authorization"
                    },
                    'body': json.dumps({"error": "Media not found"})
                }

            # Format vendor details
            vendor_details = {
                "venue": {
                    "name": media_data.get("venue_name"),
                    "contact": media_data.get("venue_contact")
                },
                "photographer": {
                    "name": media_data.get("photographer_name"),
                    "contact": media_data.get("photographer_contact")
                },
                "makeup_artist": {
                    "name": media_data.get("makeup_artist_name"),
                    "contact": media_data.get("makeup_artist_contact")
                },
                "decoration": {
                    "name": media_data.get("decoration_name"),
                    "contact": media_data.get("decoration_contact")
                },
                "caterer": {
                    "name": media_data.get("caterer_name"),
                    "contact": media_data.get("caterer_contact")
                },
                "additional_vendors": media_data.get("additional_vendors")
            }

            # Build response based on media type
            response_data = {
                "vendor_details": vendor_details,
                "media_id": media_id,
                "media_type": media_data.get("media_type"),
                "media_url": media_data.get("media_url"),
                "media_caption": media_data.get("media_caption"),
                "user_name": media_data.get("user_name"),
                "user_id": media_data.get("user_id"),
                "created_at": media_data.get("created_at")
            }

            # Add backward compatibility fields
            if media_data.get("media_type") == "video":
                response_data["video_id"] = media_id  # For backward compatibility
                response_data["video_name"] = media_data.get("media_caption")

            return {
                'statusCode': 200,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps(response_data, cls=CustomJSONEncoder)
            }

        except Exception as e:
            return {
                'statusCode': 500,
                'headers': {
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "OPTIONS, GET",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                'body': json.dumps({"error": f"Database error: {str(e)}"})
            }
        finally:
            cursor.close()
            conn.close()

    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "OPTIONS, GET",
                "Access-Control-Allow-Headers": "Content-Type, Authorization"
            },
            'body': json.dumps({"error": f"Internal server error: {str(e)}"})
        }

# Custom JSON encoder to handle date objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        from datetime import date, datetime
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)