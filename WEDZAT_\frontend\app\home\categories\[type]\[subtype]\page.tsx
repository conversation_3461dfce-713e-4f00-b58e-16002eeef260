"use client";
import { useParams } from 'next/navigation';

export default function CategorySubtypePage() {
  const params = useParams();
  const type = params && typeof params === 'object' && 'type' in params && params.type ? String(params.type) : '';
  const subtype = params && typeof params === 'object' && 'subtype' in params && params.subtype ? String(params.subtype) : '';

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">{type} - {subtype}</h1>
      <div>
        {/* Here you can fetch and display the list or details for this subtype */}
        <p>Display content for {subtype} under {type} here.</p>
      </div>
    </div>
  );
} 