"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/WedzatLoginpage.tsx":
/*!****************************************!*\
  !*** ./components/WedzatLoginpage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Mail,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Mail,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Mail,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Mail,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Mail,Phone,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _hooks_useIsMobile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/useIsMobile */ \"(app-pages-browser)/./hooks/useIsMobile.ts\");\n/* harmony import */ var _login_login__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./login/login */ \"(app-pages-browser)/./components/login/login.tsx\");\n/* harmony import */ var _login_email_login__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./login/email-login */ \"(app-pages-browser)/./components/login/email-login.tsx\");\n/* harmony import */ var _verify_otp_verify_otp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./verify-otp/verify-otp */ \"(app-pages-browser)/./components/verify-otp/verify-otp.tsx\");\n/* harmony import */ var _verify_otp_email_verification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./verify-otp/email-verification */ \"(app-pages-browser)/./components/verify-otp/email-verification.tsx\");\n/* harmony import */ var _registration_registration__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./registration/registration */ \"(app-pages-browser)/./components/registration/registration.tsx\");\n/* harmony import */ var _login_vendor_login__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./login/vendor-login */ \"(app-pages-browser)/./components/login/vendor-login.tsx\");\n/* harmony import */ var _registration_vendor_signup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./registration/vendor-signup */ \"(app-pages-browser)/./components/registration/vendor-signup.tsx\");\n/* harmony import */ var _login_user_login__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./login/user-login */ \"(app-pages-browser)/./components/login/user-login.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WedzatLoginPage = ()=>{\n    var _user_emailAddresses_;\n    _s();\n    const [loginPopUp, setLoginPopUp] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"login\");\n    const [authMethod, setAuthMethod] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"mobile\");\n    const [mobileNumber, setMobileNumber] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const isMobile = (0,_hooks_useIsMobile__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    // We're using isAuthLoaded directly from useAuth() hook\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { isLoaded: isSignUpLoaded, signUp } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useSignUp)();\n    const { isLoaded: isSignInLoaded, signIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useSignIn)();\n    const [logoStyle, setLogoStyle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        filter: \"brightness(0) saturate(100%) invert(13%) sepia(77%) saturate(4720%) hue-rotate(352deg) brightness(89%) contrast(91%)\",\n        opacity: 1\n    });\n    const [currentFrame, setCurrentFrame] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sequenceComplete, setSequenceComplete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Define the SVG frames array\n    const logoFrames = react__WEBPACK_IMPORTED_MODULE_2___default().useMemo({\n        \"WedzatLoginPage.useMemo[logoFrames]\": ()=>[\n                \"/pics/Property 1=Variant4.png\",\n                \"/pics/Property 1=Variant3.png\",\n                \"/pics/Property 1=Variant2.png\",\n                \"/pics/image.png\"\n            ]\n    }[\"WedzatLoginPage.useMemo[logoFrames]\"], []);\n    // const { isLoaded: isSignInLoaded, signIn } = useSignIn();\n    const { isSignedIn, user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useUser)();\n    const { isLoaded: isAuthLoaded } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_15__.usePromisifiedAuth)();\n    // Logo animation effect alternating between violet and gold\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WedzatLoginPage.useEffect\": ()=>{\n            if (sequenceComplete) return; // Don't run if sequence is already complete\n            const frameTransitionTime = 400; // 1.8 seconds per transition between frames\n            const initialDelay = 0; // 1 second delay before starting animation\n            const finalFrameHoldTime = 500; // Hold final frame for 2 seconds before starting color animation\n            const timeoutIds = [];\n            // Schedule each frame transition with increasing delays\n            logoFrames.forEach({\n                \"WedzatLoginPage.useEffect\": (_, index)=>{\n                    const delay = initialDelay + index * frameTransitionTime;\n                    const timeoutId = setTimeout({\n                        \"WedzatLoginPage.useEffect.timeoutId\": ()=>{\n                            setCurrentFrame(index);\n                            // If this is the last frame, mark sequence as complete after a hold time\n                            if (index === logoFrames.length - 1) {\n                                const completeTimeoutId = setTimeout({\n                                    \"WedzatLoginPage.useEffect.timeoutId.completeTimeoutId\": ()=>{\n                                        setSequenceComplete(true);\n                                    }\n                                }[\"WedzatLoginPage.useEffect.timeoutId.completeTimeoutId\"], finalFrameHoldTime);\n                                timeoutIds.push(completeTimeoutId);\n                            }\n                        }\n                    }[\"WedzatLoginPage.useEffect.timeoutId\"], delay);\n                    timeoutIds.push(timeoutId);\n                }\n            }[\"WedzatLoginPage.useEffect\"]);\n            // Cleanup function\n            return ({\n                \"WedzatLoginPage.useEffect\": ()=>{\n                    timeoutIds.forEach({\n                        \"WedzatLoginPage.useEffect\": (id)=>clearTimeout(id)\n                    }[\"WedzatLoginPage.useEffect\"]);\n                }\n            })[\"WedzatLoginPage.useEffect\"];\n        }\n    }[\"WedzatLoginPage.useEffect\"], [\n        logoFrames,\n        sequenceComplete\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WedzatLoginPage.useEffect\": ()=>{\n            if (!sequenceComplete) return; // Only run after frame sequence completes\n            // Define colors to transition between\n            const colors = [\n                {\n                    filter: \"brightness(0) saturate(100%) invert(13%) sepia(77%) saturate(4720%) hue-rotate(352deg) brightness(89%) contrast(91%)\",\n                    opacity: 1\n                },\n                {\n                    filter: \"brightness(0) saturate(100%) invert(18%) sepia(61%) saturate(2848%) hue-rotate(194deg) brightness(94%) contrast(101%)\",\n                    opacity: 1\n                }\n            ];\n            let colorIndex = 0;\n            const colorTransitionTime = 3000; // 3 seconds between color changes\n            // Start color transition interval\n            const intervalId = setInterval({\n                \"WedzatLoginPage.useEffect.intervalId\": ()=>{\n                    colorIndex = (colorIndex + 1) % colors.length;\n                    setLogoStyle(colors[colorIndex]);\n                }\n            }[\"WedzatLoginPage.useEffect.intervalId\"], colorTransitionTime);\n            // Cleanup function\n            return ({\n                \"WedzatLoginPage.useEffect\": ()=>{\n                    clearInterval(intervalId);\n                }\n            })[\"WedzatLoginPage.useEffect\"];\n        }\n    }[\"WedzatLoginPage.useEffect\"], [\n        sequenceComplete\n    ]);\n    const closePopup = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"WedzatLoginPage.useCallback[closePopup]\": ()=>{\n            setLoginPopUp(false);\n            setCurrentView(\"login\");\n            setAuthMethod(\"mobile\");\n        }\n    }[\"WedzatLoginPage.useCallback[closePopup]\"], []);\n    // Removed unused function\n    // Auth method switching\n    const switchAuthMethod = (method)=>{\n        setAuthMethod(method);\n        setCurrentView(method === \"mobile\" ? \"login\" : \"emailLogin\");\n    };\n    // Mobile auth handlers\n    const handleOtpRequest = (mobile)=>{\n        console.log(\"OTP requested for:\", mobile);\n        setMobileNumber(mobile);\n        setCurrentView(\"otp\");\n    };\n    const handleOtpVerify = (otp)=>{\n        console.log(\"OTP verified:\", otp);\n        setCurrentView(\"registration\");\n    };\n    const handleResendOtp = ()=>{\n        console.log(\"Resending OTP to:\", mobileNumber);\n    // Implement resend OTP logic\n    };\n    // Email auth handlers\n    const handleEmailVerify = (emailAddress)=>{\n        console.log(\"Verification requested for email:\", emailAddress);\n        setEmail(emailAddress);\n        setCurrentView(\"emailVerification\");\n    };\n    const handleEmailCodeVerify = (code)=>{\n        console.log(\"Email verification code verified:\", code);\n        setCurrentView(\"registration\");\n    };\n    const handleResendEmailCode = ()=>{\n        console.log(\"Resending verification code to:\", email);\n    // Implement resend email code logic\n    };\n    // Vendor auth handlers\n    const handleVendorLogin = (credentials)=>{\n        console.log(\"Vendor login:\", credentials);\n        // Implement vendor login logic\n        closePopup();\n    };\n    const handleVendorSignup = (data)=>{\n        console.log(\"Vendor signup:\", data);\n        // Implement vendor signup logic\n        closePopup();\n    };\n    const handleForgotPassword = ()=>{\n        console.log(\"Forgot password\");\n    // Implement forgot password logic\n    };\n    // Navigation handlers\n    const navigateToVendorLogin = ()=>{\n        setCurrentView(\"vendorLogin\");\n    };\n    const navigateToVendorSignup = ()=>{\n        setCurrentView(\"vendorSignup\");\n    };\n    const handleSocialSignin = async (provider)=>{\n        if (!isSignUpLoaded || !isSignInLoaded) {\n            console.error(\"Sign-in is not loaded yet\");\n            return;\n        }\n        try {\n            // Map your app's provider names to Clerk's OAuthStrategy type\n            const strategyMap = {\n                Google: \"oauth_google\",\n                Apple: \"oauth_apple\",\n                Facebook: \"oauth_facebook\",\n                Gmail: \"oauth_google\"\n            };\n            const strategy = strategyMap[provider];\n            console.log(\"Starting \".concat(provider, \" authentication...\"));\n            // For Google OAuth, we need to handle both sign-in and sign-up in one flow\n            // We'll add a special parameter to the URL to help our callback page identify new accounts\n            const redirectUrl = \"/auth/callback?source=social&provider=\" + provider.toLowerCase();\n            console.log(\"Authenticating with \".concat(provider, \" using redirect URL: \").concat(redirectUrl));\n            // Use a single authentication call with the custom redirect URL\n            await (signIn === null || signIn === void 0 ? void 0 : signIn.authenticateWithRedirect({\n                strategy,\n                redirectUrl: redirectUrl,\n                redirectUrlComplete: redirectUrl\n            }));\n        } catch (error) {\n            console.error(\"Error during \".concat(provider, \" authentication:\"), error);\n        }\n    };\n    // Updated Social Login handler using Clerk\n    const handleSocialLogin = async (provider)=>{\n        if (!isSignUpLoaded || !isSignInLoaded) {\n            console.error(\"Sign-up is not loaded yet\");\n            return;\n        }\n        try {\n            // Map your app's provider names to Clerk's OAuthStrategy type\n            const strategyMap = {\n                Google: \"oauth_google\",\n                Apple: \"oauth_apple\",\n                Facebook: \"oauth_facebook\",\n                Gmail: \"oauth_google\"\n            };\n            const strategy = strategyMap[provider];\n            console.log(\"Starting \".concat(provider, \" authentication...\"));\n            // For Google OAuth, we need to handle both sign-in and sign-up in one flow\n            // We'll add a special parameter to the URL to help our callback page identify new accounts\n            const redirectUrl = \"/auth/callback?source=social&provider=\" + provider.toLowerCase();\n            console.log(\"Authenticating with \".concat(provider, \" using redirect URL: \").concat(redirectUrl));\n            // Use a single authentication call with the custom redirect URL\n            await (signUp === null || signUp === void 0 ? void 0 : signUp.authenticateWithRedirect({\n                strategy,\n                redirectUrl: redirectUrl,\n                redirectUrlComplete: redirectUrl\n            }));\n        } catch (error) {\n            console.error(\"Error during \".concat(provider, \" authentication:\"), error);\n        }\n    };\n    // Define handleClerkAuth with useCallback\n    const handleClerkAuth = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"WedzatLoginPage.useCallback[handleClerkAuth]\": async ()=>{\n            // Check if we already have a valid token\n            const existingToken = localStorage.getItem(\"wedzat_token\") || localStorage.getItem(\"jwt_token\") || localStorage.getItem(\"token\");\n            if (existingToken) {\n                console.log(\"Valid token exists, skipping auth call\");\n                router.push(currentView === \"vendorSignup\" ? \"/vendor/dashboard\" : \"/home\");\n                return;\n            }\n            if (!isAuthLoaded || !isSignedIn || !user) {\n                console.error(\"Auth not loaded or user not signed in\");\n                return null;\n            }\n            console.log(\"Starting Clerk authentication process\");\n            try {\n                var _user_primaryEmailAddress;\n                // Get the token from Clerk\n                let token;\n                // Try to get token without specifying a template first\n                try {\n                    token = await window.Clerk.session.getToken();\n                    console.log(\"Got token without template\");\n                } catch (tokenError) {\n                    console.error(\"Error getting token without template:\", tokenError);\n                    // If that fails, try with the default template\n                    try {\n                        token = await window.Clerk.session.getToken({\n                            template: \"default\"\n                        });\n                        console.log(\"Got token with default template\");\n                    } catch (defaultError) {\n                        console.error(\"Error getting token with default template:\", defaultError);\n                        throw new Error(\"Could not retrieve authentication token\");\n                    }\n                }\n                if (!token) {\n                    throw new Error(\"No token received from Clerk\");\n                }\n                console.log(\"Token retrieved successfully: Token exists\");\n                // Extract user info to send along with the token\n                const userEmail = (user === null || user === void 0 ? void 0 : (_user_primaryEmailAddress = user.primaryEmailAddress) === null || _user_primaryEmailAddress === void 0 ? void 0 : _user_primaryEmailAddress.emailAddress) || \"\";\n                const userName = (user === null || user === void 0 ? void 0 : user.fullName) || \"\";\n                const userId = (user === null || user === void 0 ? void 0 : user.id) || \"\";\n                console.log(\"Sending user info to backend:\", {\n                    email: userEmail,\n                    name: userName,\n                    id: userId\n                });\n                // Call your backend API to authenticate with Clerk token\n                console.log(\"Attempting to connect to backend...\");\n                const apiUrl = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n                console.log(\"Using API URL: \".concat(apiUrl));\n                const response = await axios__WEBPACK_IMPORTED_MODULE_16__[\"default\"].post(\"\".concat(apiUrl, \"/auth_clerk\"), {\n                    clerk_token: token,\n                    user_type: currentView === \"vendorSignup\" ? \"vendor\" : \"normal\",\n                    user_email: userEmail,\n                    user_name: userName,\n                    user_id: userId\n                }, {\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Backend response:\", response);\n                // Handle response from backend\n                console.log(\"Backend response data:\", response.data);\n                // Check if response contains a token directly\n                if (response.data && response.data.token) {\n                    console.log(\"Found token in direct response\");\n                    // Store the backend JWT token\n                    localStorage.setItem(\"wedzat_token\", response.data.token);\n                    // Close popup\n                    closePopup();\n                    // Redirect based on user type\n                    console.log(\"Redirecting to \".concat(currentView === \"vendorSignup\" ? \"/vendor/dashboard\" : \"/home\"));\n                    // Use router for navigation instead of window.location\n                    if (currentView === \"vendorSignup\") {\n                        router.push(\"/vendor/dashboard\");\n                    } else {\n                        router.push(\"/home\");\n                    }\n                    // Force navigation if router doesn't work\n                    setTimeout({\n                        \"WedzatLoginPage.useCallback[handleClerkAuth]\": ()=>{\n                            if (currentView === \"vendorSignup\") {\n                                window.location.href = \"/vendor/dashboard\";\n                            } else {\n                                window.location.href = \"/home\";\n                            }\n                        }\n                    }[\"WedzatLoginPage.useCallback[handleClerkAuth]\"], 500);\n                    return; // Exit early after successful authentication\n                }\n                // Check for nested response structure (old format)\n                if (response.data && typeof response.data === \"object\" && response.data.body) {\n                    try {\n                        // Try to parse the body if it's a string\n                        const parsedBody = typeof response.data.body === \"string\" ? JSON.parse(response.data.body) : response.data.body;\n                        console.log(\"Parsed body:\", parsedBody);\n                        // Check if the nested response contains an error\n                        if (response.data.statusCode >= 400 || parsedBody && parsedBody.error) {\n                            throw new Error(parsedBody && parsedBody.error || \"Backend authentication failed\");\n                        }\n                        // Check for token in the parsed body\n                        if (parsedBody && parsedBody.token) {\n                            // Store the backend JWT token\n                            localStorage.setItem(\"wedzat_token\", parsedBody.token);\n                            // Close popup\n                            closePopup();\n                            // Redirect based on user type\n                            console.log(\"Redirecting to \".concat(currentView === \"vendorSignup\" ? \"/vendor/dashboard\" : \"/home\"));\n                            // Use router for navigation instead of window.location\n                            if (currentView === \"vendorSignup\") {\n                                router.push(\"/vendor/dashboard\");\n                            } else {\n                                router.push(\"/home\");\n                            }\n                            // Force navigation if router doesn't work\n                            setTimeout({\n                                \"WedzatLoginPage.useCallback[handleClerkAuth]\": ()=>{\n                                    if (currentView === \"vendorSignup\") {\n                                        window.location.href = \"/vendor/dashboard\";\n                                    } else {\n                                        window.location.href = \"/home\";\n                                    }\n                                }\n                            }[\"WedzatLoginPage.useCallback[handleClerkAuth]\"], 500);\n                            return; // Exit early after successful authentication\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing response body:\", parseError);\n                        throw new Error(\"Invalid response format from backend\");\n                    }\n                }\n                // Check if there's a database error in the response\n                if (response.data && response.data.body && typeof response.data.body === \"string\" && (response.data.body.includes(\"violates not-null constraint\") || response.data.body.includes(\"violates check constraint\"))) {\n                    console.error(\"Database constraint violation error. Using fallback approach...\");\n                    // Instead of retrying, let's use the Clerk token directly as a fallback\n                    console.log(\"Using Clerk token as fallback due to database constraints\");\n                    localStorage.setItem(\"wedzat_token\", token);\n                    closePopup();\n                    // Redirect based on user type\n                    console.log(\"Redirecting to \".concat(currentView === \"vendorSignup\" ? \"/vendor/dashboard\" : \"/home\"));\n                    // Use router for navigation instead of window.location\n                    if (currentView === \"vendorSignup\") {\n                        router.push(\"/vendor/dashboard\");\n                    } else {\n                        router.push(\"/home\");\n                    }\n                    // Force navigation if router doesn't work\n                    setTimeout({\n                        \"WedzatLoginPage.useCallback[handleClerkAuth]\": ()=>{\n                            if (currentView === \"vendorSignup\") {\n                                window.location.href = \"/vendor/dashboard\";\n                            } else {\n                                window.location.href = \"/home\";\n                            }\n                        }\n                    }[\"WedzatLoginPage.useCallback[handleClerkAuth]\"], 500);\n                    return;\n                }\n                // If we get here, we didn't find a token in the response\n                console.log(\"No token found in response, using fallback\");\n                // FALLBACK: Use the Clerk token directly if backend response is invalid\n                console.log(\"Using Clerk token as fallback\");\n                localStorage.setItem(\"wedzat_token\", token);\n                closePopup();\n                // Redirect based on user type\n                console.log(\"Redirecting to \".concat(currentView === \"vendorSignup\" ? \"/vendor/dashboard\" : \"/home\"));\n                // Use router for navigation instead of window.location\n                if (currentView === \"vendorSignup\") {\n                    router.push(\"/vendor/dashboard\");\n                } else {\n                    router.push(\"/home\");\n                }\n                // Force navigation if router doesn't work\n                setTimeout({\n                    \"WedzatLoginPage.useCallback[handleClerkAuth]\": ()=>{\n                        if (currentView === \"vendorSignup\") {\n                            window.location.href = \"/vendor/dashboard\";\n                        } else {\n                            window.location.href = \"/home\";\n                        }\n                    }\n                }[\"WedzatLoginPage.useCallback[handleClerkAuth]\"], 500);\n                return;\n            } catch (error) {\n                console.error(\"Backend error:\", error);\n                return null;\n            }\n        }\n    }[\"WedzatLoginPage.useCallback[handleClerkAuth]\"], [\n        isAuthLoaded,\n        isSignedIn,\n        user,\n        closePopup,\n        currentView,\n        router\n    ]);\n    // Auth state conditional rendering\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WedzatLoginPage.useEffect\": ()=>{\n            if (isAuthLoaded && isSignedIn && user) {\n                // User is signed in, close popup and/or redirect\n                closePopup();\n                // Try to authenticate with backend\n                handleClerkAuth();\n            }\n        }\n    }[\"WedzatLoginPage.useEffect\"], [\n        isAuthLoaded,\n        isSignedIn,\n        user,\n        closePopup,\n        handleClerkAuth\n    ]);\n    // Shared handlers\n    const handleLoginClick = ()=>{\n        console.log(\"Login clicked\");\n        // Navigate to the user login view\n        setCurrentView(\"userLogin\");\n    };\n    const handleUserLogin = (credentials)=>{\n        console.log(\"User login:\", credentials);\n        // Implement user login logic - already handled in the UserLogin component\n        closePopup();\n    };\n    const handleVendorSignupClick = ()=>{\n        console.log(\"Vendor signup clicked\");\n        navigateToVendorSignup();\n    };\n    const handleRegistrationSubmit = (data)=>{\n        console.log(\"Registration submitted:\", data);\n        // Implement registration submission\n        setLoginPopUp(false);\n    // Show success message or redirect\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WedzatLoginPage.useEffect\": ()=>{\n            const checkAuthStatus = {\n                \"WedzatLoginPage.useEffect.checkAuthStatus\": async ()=>{\n                    // Check for token in localStorage\n                    const token = localStorage.getItem(\"token\");\n                    if (token) {\n                        try {\n                            // Optional: Verify token is valid with a backend call\n                            // const isValid = await authService.verifyToken(token);\n                            // if (isValid) {\n                            //   router.replace('/home');\n                            // }\n                            // For now, just redirect if token exists\n                            router.replace(\"/home\");\n                        } catch (error) {\n                            console.error(\"Error verifying auth token:\", error);\n                            // If token verification fails, clear it\n                            localStorage.removeItem(\"token\");\n                        }\n                    }\n                }\n            }[\"WedzatLoginPage.useEffect.checkAuthStatus\"];\n            checkAuthStatus();\n        }\n    }[\"WedzatLoginPage.useEffect\"], [\n        router\n    ]);\n    // handleClerkAuth is now defined above with useCallback\n    // Determine if we're on a verification or registration screen\n    const isVerificationOrRegistration = currentView === \"otp\" || currentView === \"emailVerification\" || currentView === \"registration\";\n    // Determine if we're on a vendor screen\n    const isVendorScreen = currentView === \"vendorLogin\" || currentView === \"vendorSignup\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isMobile ? \"flex-col items-center justify-center h-screen overflow-hidden py-1\" : \"flex-col lg:flex-row items-center justify-center min-h-screen\", \" \").concat(loginPopUp ? \"bg-black text-white transition-colors duration-300\" : \"bg-white text-black\", \" \").concat(isMobile ? \"px-2\" : \"p-8\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-center \".concat(isMobile ? \"mb-2\" : \"mr-16 -ml-8\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isMobile ? \"mb-2\" : \"mb-0\", \" flex justify-center\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative \".concat(isMobile ? \"w-[120px] h-[95px] mr-0\" : \"w-[130px] h-[100px] mr-10\"),\n                            children: !sequenceComplete ? // Show frame sequence until complete\n                            logoFrames.map((frame, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 transition-opacity duration-500\",\n                                    style: {\n                                        opacity: currentFrame === index ? 1 : 0,\n                                        zIndex: index\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: frame,\n                                        alt: \"WEDZAT Logo Frame \".concat(index + 1),\n                                        width: isMobile ? 120 : 130,\n                                        height: isMobile ? 95 : 100,\n                                        priority: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 17\n                                }, undefined)) : // After sequence complete, show final frame with color transitions\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"transition-all duration-500 ease-in-out\",\n                                style: logoStyle,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: logoFrames[logoFrames.length - 1],\n                                    alt: \"WEDZAT Logo\",\n                                    width: isMobile ? 120 : 130,\n                                    height: isMobile ? 95 : 100,\n                                    priority: true,\n                                    className: \"object-contain\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                            lineNumber: 652,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/pics/unnamed.png\",\n                            alt: \"WEDZAT Text\",\n                            width: isMobile ? 180 : 200,\n                            height: isMobile ? 180 : 200,\n                            priority: true,\n                            className: \"object-contain \".concat(isMobile ? \"mr-0 mb-1 mt-1\" : \"mr-10 mb-8 mt-2\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"\".concat(isMobile ? \"text-4xl\" : \"text-6xl md:text-7xl\", \" leading-tight tracking-[-0.03em] text-center font-bold\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-jakarta font-medium bg-gradient-to-r from-[#CCAB75] to-[#CCAB75] bg-clip-text text-transparent\",\n                                        children: [\n                                            \"Your\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-serif font-normal italic bg-gradient-to-r from-[#CCAB75] to-[#000000] bg-clip-text text-transparent\",\n                                        children: \"wedding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center \".concat(isMobile ? \"mt-1 mb-1\" : \"mt-1 mb-6\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex -space-x-4 \".concat(isMobile ? \"mr-3\" : \"mr-6\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(isMobile ? \"w-10 h-10\" : \"w-12 h-12 md:w-20 md:h-20\", \" relative shadow-lg rounded-full\"),\n                                                style: {\n                                                    boxShadow: \"0 0 10px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/pics/1stim.jfif\",\n                                                    alt: \"Wedding photo\",\n                                                    fill: true,\n                                                    className: \"object-cover rounded-full border-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(isMobile ? \"w-10 h-10\" : \"w-12 h-12 md:w-20 md:h-20\", \" relative shadow-lg rounded-full\"),\n                                                style: {\n                                                    boxShadow: \"0 0 10px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/pics/2ndim.jfif\",\n                                                    alt: \"Wedding photo\",\n                                                    fill: true,\n                                                    className: \"object-cover rounded-full border-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(isMobile ? \"w-10 h-10\" : \"w-12 h-12 md:w-20 md:h-20\", \" relative shadow-lg rounded-full\"),\n                                                style: {\n                                                    boxShadow: \"0 0 10px rgba(0, 0, 0, 0.3)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/pics/jpeg3.jfif\",\n                                                    alt: \"Wedding photo\",\n                                                    fill: true,\n                                                    className: \"object-cover rounded-full border-2 border-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"\".concat(isMobile ? \"text-4xl\" : \"text-6xl md:text-7xl\", \" leading-tight tracking-[-0.03em] font-jakarta font-medium text-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-[#CCAB76] to-[#66563B] bg-clip-text text-transparent\",\n                                            children: \"platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                lineNumber: 647,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full \".concat(isMobile ? \"max-w-sm px-4 flex-shrink-0\" : \"max-w-md md:ml-12\", \" gap-3\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(isMobile ? \"text-center mb-2\" : \"text-center md:text-left mb-12\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"\".concat(isMobile ? \"text-2xl\" : \"text-4xl\", \" font-bold mb-2\"),\n                                children: [\n                                    \"Welcome to \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-700\",\n                                        children: \"Wedzat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 24\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"\".concat(isMobile ? \"text-base\" : \"text-lg\", \" text-gray-700 font-medium\"),\n                                children: \"Choose your Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-\".concat(isMobile ? \"2\" : \"6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setLoginPopUp(true);\n                                    setCurrentView(\"userLogin\"); // Open signup page first\n                                },\n                                className: \"w-full flex items-center \".concat(isMobile ? \"px-4 py-3\" : \"px-6 py-4\", \" rounded-full transition duration-300 border \").concat(loginPopUp ? \"bg-black text-white border-white\" : \"bg-white text-black border-gray-300 hover:bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: isMobile ? 18 : 22,\n                                        className: \"mr-4 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium flex-grow text-center \".concat(isMobile ? \"text-base\" : \"text-lg\"),\n                                        children: \"User Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center justify-center \".concat(isMobile ? \"my-4\" : \"my-8\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow border-t border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-shrink mx-4 text-gray-500 font-medium \".concat(isMobile ? \"text-base\" : \"text-lg\"),\n                                        children: \"OR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow border-t border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 829,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setLoginPopUp(true);\n                                    setCurrentView(\"vendorLogin\");\n                                },\n                                className: \"w-full flex items-center \".concat(isMobile ? \"px-4 py-3\" : \"px-6 py-4\", \" rounded-full transition duration-300 border \").concat(loginPopUp ? \"bg-black text-white border-white\" : \"bg-white text-black border-gray-300 hover:bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: isMobile ? 18 : 22,\n                                        className: \"mr-4 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium flex-grow text-center \".concat(isMobile ? \"text-base\" : \"text-lg\"),\n                                        children: \"Business Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                        lineNumber: 806,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                lineNumber: 788,\n                columnNumber: 7\n            }, undefined),\n            loginPopUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 backdrop-brightness-30 flex items-center justify-center z-50 \".concat(isMobile ? \"p-2\" : \"p-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative rounded-lg w-full pt-2 mx-auto overflow-hidden text-gray-900 \".concat(isMobile ? \"max-w-sm\" : \"max-w-lg\"),\n                    style: {\n                        background: \"linear-gradient(to bottom, #FAE6C4, #FFFFFF)\",\n                        maxHeight: \"auto\",\n                        boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closePopup,\n                            className: \"absolute top-4 right-4 flex items-center justify-center \".concat(isMobile ? \"w-6 h-6\" : \"w-8 h-8\", \" text-[#646B7D] hover:text-[#B62E2E] focus:outline-none focus:ring-2 focus:ring-[#B62E2E] focus:ring-opacity-50 rounded-full transition-all duration-200 transform hover:scale-110 active:scale-95 z-10 bg-transparent hover:bg-[#f8f1e4] group\"),\n                            \"aria-label\": \"Close registration form\",\n                            type: \"button\",\n                            tabIndex: 0,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 888,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    size: isMobile ? 18 : 24,\n                                    strokeWidth: 2.5,\n                                    className: \"transition-transform duration-200 group-hover:rotate-90\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 13\n                        }, undefined),\n                        !isVerificationOrRegistration && !isVendorScreen && currentView !== \"userLogin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center \".concat(isMobile ? \"mb-4\" : \"mb-6\", \" border-b border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>switchAuthMethod(\"mobile\"),\n                                    className: \"flex items-center \".concat(isMobile ? \"px-4 py-2\" : \"px-6 py-3\", \" \").concat(authMethod === \"mobile\" ? \"text-red-700 border-b-2 border-red-700 font-medium\" : \"text-gray-500 hover:text-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            size: isMobile ? 16 : 18,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: isMobile ? \"text-sm\" : \"\",\n                                            children: \"Mobile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>switchAuthMethod(\"email\"),\n                                    className: \"flex items-center \".concat(isMobile ? \"px-4 py-2\" : \"px-6 py-3\", \" \").concat(authMethod === \"email\" ? \"text-red-700 border-b-2 border-red-700 font-medium\" : \"text-gray-500 hover:text-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Mail_Phone_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            size: isMobile ? 16 : 18,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: isMobile ? \"text-sm\" : \"\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxHeight: \"calc(93vh - 0px)\",\n                                scrollbarWidth: \"none\" /* Firefox */ ,\n                                msOverflowStyle: \"none\" /* IE and Edge */ \n                            },\n                            className: \"jsx-a591263bceba4208\" + \" \" + \"scale-95 origin-top \".concat(isMobile ? \"px-3 py-2\" : \"px-4 py-2\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    id: \"a591263bceba4208\",\n                                    children: \".scrollbar-hide.jsx-a591263bceba4208::-webkit-scrollbar{display:none}body.jsx-a591263bceba4208{text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}input.jsx-a591263bceba4208,select.jsx-a591263bceba4208,textarea.jsx-a591263bceba4208,button.jsx-a591263bceba4208{font-size:1rem!important;letter-spacing:.01em!important}label.jsx-a591263bceba4208{font-weight:500!important;margin-bottom:.25rem!important}button.jsx-a591263bceba4208,a.jsx-a591263bceba4208{-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}\"\n                                }, void 0, false, void 0, undefined),\n                                currentView === \"userLogin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_user_login__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    onBack: closePopup,\n                                    onLogin: handleUserLogin,\n                                    onForgotPassword: handleForgotPassword,\n                                    onSignupClick: ()=>setCurrentView(\"login\"),\n                                    onSocialSignin: handleSocialSignin\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"login\" && (isSignedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-a591263bceba4208\" + \" \" + \"text-center mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-a591263bceba4208\" + \" \" + \"mb-4\",\n                                            children: [\n                                                \"You are logged in as\",\n                                                \" \",\n                                                (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : (_user_emailAddresses_ = user.emailAddresses[0]) === null || _user_emailAddresses_ === void 0 ? void 0 : _user_emailAddresses_.emailAddress)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.SignOutButton, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"jsx-a591263bceba4208\" + \" \" + \"w-full bg-red-700 text-white py-3 rounded-md mb-4 hover:bg-red-800 transition duration-200\",\n                                                children: \"Sign Out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_login__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    onOtpRequest: handleOtpRequest,\n                                    onSocialLogin: handleSocialLogin,\n                                    onLogin: handleLoginClick,\n                                    onVendorSignup: handleVendorSignupClick\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 19\n                                }, undefined)),\n                                currentView === \"emailLogin\" && !isSignedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_email_login__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    onEmailVerify: handleEmailVerify,\n                                    onSocialLogin: handleSocialLogin,\n                                    onLogin: handleLoginClick,\n                                    onVendorSignup: handleVendorSignupClick\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"otp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_verify_otp_verify_otp__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    mobileNumber: mobileNumber,\n                                    onVerify: handleOtpVerify,\n                                    onClose: ()=>setCurrentView(\"login\"),\n                                    onResendOtp: handleResendOtp,\n                                    onLoginClick: handleLoginClick,\n                                    onVendorSignupClick: handleVendorSignupClick\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1026,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"emailVerification\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_verify_otp_email_verification__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    email: email,\n                                    onVerify: handleEmailCodeVerify,\n                                    onClose: ()=>setCurrentView(\"emailLogin\"),\n                                    onResendCode: handleResendEmailCode,\n                                    onLoginClick: handleLoginClick,\n                                    onVendorSignupClick: handleVendorSignupClick\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1038,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"registration\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_registration_registration__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    onComplete: handleRegistrationSubmit,\n                                    onBack: ()=>setCurrentView(email ? \"emailLogin\" : \"login\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"vendorLogin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_login_vendor_login__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onLogin: handleVendorLogin,\n                                    onForgotPassword: handleForgotPassword,\n                                    onSignupClick: navigateToVendorSignup,\n                                    onBack: closePopup\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 17\n                                }, undefined),\n                                currentView === \"vendorSignup\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_registration_vendor_signup__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onSignup: handleVendorSignup,\n                                    onLoginClick: navigateToVendorLogin,\n                                    onBack: closePopup\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                            lineNumber: 933,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                    lineNumber: 869,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n                lineNumber: 866,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\final\\\\WEDZAT_\\\\frontend\\\\components\\\\WedzatLoginpage.tsx\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WedzatLoginPage, \"nLUxG8WiJLGu4pBySH+dhHS6CBE=\", false, function() {\n    return [\n        _hooks_useIsMobile__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useSignUp,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useSignIn,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_14__.useUser,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_15__.usePromisifiedAuth\n    ];\n});\n_c = WedzatLoginPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WedzatLoginPage);\nvar _c;\n$RefreshReg$(_c, \"WedzatLoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/WedzatLoginpage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useIsMobile.ts":
/*!******************************!*\
  !*** ./hooks/useIsMobile.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useIsMobile)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MOBILE_BREAKPOINT = 640;\nfunction useIsMobile() {\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)( true ? window.innerWidth < MOBILE_BREAKPOINT : 0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useIsMobile.useEffect\": ()=>{\n            function handleResize() {\n                setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n            }\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"useIsMobile.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"useIsMobile.useEffect\"];\n        }\n    }[\"useIsMobile.useEffect\"], []);\n    return isMobile;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZUlzTW9iaWxlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUU1QyxNQUFNRSxvQkFBb0I7QUFFWCxTQUFTQztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR0osK0NBQVFBLENBQ3RDLEtBQTZCLEdBQUdLLE9BQU9DLFVBQVUsR0FBR0wsb0JBQW9CLENBQUs7SUFHL0VGLGdEQUFTQTtpQ0FBQztZQUNSLFNBQVNRO2dCQUNQSCxZQUFZQyxPQUFPQyxVQUFVLEdBQUdMO1lBQ2xDO1lBQ0FJLE9BQU9HLGdCQUFnQixDQUFDLFVBQVVEO1lBQ2xDO3lDQUFPLElBQU1GLE9BQU9JLG1CQUFtQixDQUFDLFVBQVVGOztRQUNwRDtnQ0FBRyxFQUFFO0lBRUwsT0FBT0o7QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzaXZhc1xcT25lRHJpdmVcXERlc2t0b3BcXGZpbmFsXFxXRURaQVRfXFxmcm9udGVuZFxcaG9va3NcXHVzZUlzTW9iaWxlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5jb25zdCBNT0JJTEVfQlJFQUtQT0lOVCA9IDY0MDtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUlzTW9iaWxlKCkge1xyXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoXHJcbiAgICB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IHdpbmRvdy5pbm5lcldpZHRoIDwgTU9CSUxFX0JSRUFLUE9JTlQgOiBmYWxzZVxyXG4gICk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmdW5jdGlvbiBoYW5kbGVSZXNpemUoKSB7XHJcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgTU9CSUxFX0JSRUFLUE9JTlQpO1xyXG4gICAgfVxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVJlc2l6ZSk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVJlc2l6ZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gaXNNb2JpbGU7XHJcbn0gIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTU9CSUxFX0JSRUFLUE9JTlQiLCJ1c2VJc01vYmlsZSIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiaGFuZGxlUmVzaXplIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useIsMobile.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./services/api.ts":
/*!*************************!*\
  !*** ./services/api.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   flashesService: () => (/* binding */ flashesService),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   uploadService: () => (/* binding */ uploadService),\n/* harmony export */   userService: () => (/* binding */ userService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _utils_s3MultipartUpload__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/s3MultipartUpload */ \"(app-pages-browser)/./utils/s3MultipartUpload.ts\");\n// services/api.ts\n\n\n// Use environment variable or fallback to localhost for development\nconst BASE_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\n// Log the API URL being used\n// console.log(`API Service using base URL: ${BASE_URL}`);\n// For development, use a CORS proxy if needed\nconst API_BASE_URL = BASE_URL;\nif (true) {\n    // Uncomment the line below to use a CORS proxy in development if needed\n    // API_BASE_URL = `https://cors-anywhere.herokuapp.com/${BASE_URL}`;\n    // Log the API URL for debugging\n    console.log('Development mode detected, using API URL:', API_BASE_URL);\n}\n// Log the API URL being used\nconsole.log(\"API Service using base URL: \".concat(API_BASE_URL));\nconst TOKEN_KEY = 'token'; // Keep using your existing token key\nconst JWT_TOKEN_KEY = 'jwt_token'; // Alternative key for compatibility\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    },\n    timeout: 60000,\n    withCredentials: false // Helps with CORS issues\n});\n// Helper to get token from storage (checking both keys)\nconst getToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(TOKEN_KEY);\n};\n// Helper to save token to storage (using both keys for compatibility)\nconst saveToken = (token)=>{\n    if (false) {}\n    console.log('Saving token to localStorage:', token.substring(0, 15) + '...');\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(JWT_TOKEN_KEY, token); // For compatibility with other components\n};\n// Request interceptor to add auth token to all requests\napi.interceptors.request.use((config)=>{\n    const token = getToken();\n    if (token && config.headers) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Authentication services\nconst authService = {\n    // Register a new user\n    signup: async (signupData)=>{\n        try {\n            console.log('Attempting signup with data:', {\n                ...signupData,\n                password: signupData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(BASE_URL, \"/signup\"), signupData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Signup response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in signup response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Signup error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during signup\"\n            };\n        }\n    },\n    // Register a new vendor\n    registerVendor: async (vendorData)=>{\n        try {\n            console.log('Attempting vendor registration with data:', {\n                ...vendorData,\n                password: vendorData.password ? '********' : undefined\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(BASE_URL, \"/register-vendor\"), vendorData, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log('Vendor registration response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in vendor registration response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Vendor registration error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred during vendor registration\"\n            };\n        }\n    },\n    // Get business types\n    getBusinessTypes: async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(BASE_URL, \"/business-types\"));\n            return response.data.business_types;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get business types error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching business types\"\n            };\n        }\n    },\n    // Get vendor profile\n    getVendorProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(BASE_URL, \"/vendor-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data.profile;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Get vendor profile error:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: \"An error occurred while fetching vendor profile\"\n            };\n        }\n    },\n    // Login with email/mobile and password\n    // In services/api.ts - login function\n    login: async (credentials)=>{\n        try {\n            console.log('Attempting login with:', {\n                email: credentials.email,\n                mobile_number: credentials.mobile_number,\n                password: '********'\n            });\n            const response = await api.post('/login', credentials);\n            console.log('Login response received:', {\n                success: true,\n                hasToken: !!response.data.token,\n                tokenPreview: response.data.token ? \"\".concat(response.data.token.substring(0, 10), \"...\") : 'none'\n            });\n            // Save token to localStorage with explicit console logs\n            if (response.data.token) {\n                console.log('Saving token to localStorage...');\n                localStorage.setItem('token', response.data.token);\n                // Verify token was saved\n                const savedToken = localStorage.getItem('token');\n                console.log(\"Token verification: \".concat(savedToken ? 'Successfully saved' : 'Failed to save'));\n            } else {\n                console.warn('No token received in login response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Login error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Invalid credentials'\n            };\n        }\n    },\n    // Authenticate with Clerk\n    clerkAuth: async (clerkData)=>{\n        try {\n            console.log('Attempting Clerk authentication');\n            const response = await api.post('/clerk_auth', clerkData);\n            console.log('Clerk auth response received:', {\n                success: true,\n                hasToken: !!response.data.token\n            });\n            // Save token to localStorage\n            if (response.data.token) {\n                saveToken(response.data.token);\n            } else {\n                console.warn('No token received in Clerk auth response');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Clerk authentication error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Clerk authentication failed'\n            };\n        }\n    },\n    // Check if user profile is complete\n    checkProfile: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Checking user profile');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(BASE_URL, \"/check-profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Profile check error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to check profile'\n            };\n        }\n    },\n    // Get the current token\n    getToken: ()=>{\n        return getToken();\n    },\n    // Check if the user is authenticated\n    isAuthenticated: ()=>{\n        return !!getToken();\n    },\n    // Logout - clear token from localStorage\n    logout: ()=>{\n        console.log('Logging out and removing tokens');\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n};\n// User services\nconst userService = {\n    // Get user details - uses GET method with explicit token in header\n    getUserDetails: async ()=>{\n        try {\n            const token = getToken();\n            if (!token) {\n                throw {\n                    error: 'No authentication token found'\n                };\n            }\n            console.log('Fetching user details');\n            // Set the correct Authorization format (Bearer + token)\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(BASE_URL, \"/user-details\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error fetching user details:\", error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to fetch user details'\n            };\n        }\n    },\n    // Update user details\n    updateUser: async (userData)=>{\n        try {\n            console.log('Updating user details');\n            const response = await api.put('/update-user', userData);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Update user error:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to update user'\n            };\n        }\n    }\n};\n// Helper to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    const token = getToken();\n    return !!token; // Return true if token exists, false otherwise\n};\n// Upload services\nconst uploadService = {\n    /**\r\n   * Verify user's face using captured image\r\n   * @param faceImage Base64 encoded image data (without data URL prefix)\r\n   */ verifyFace: async (faceImage)=>{\n        try {\n            console.log('Sending face verification request');\n            const token = getToken();\n            // Create the request payload\n            const payload = {\n                face_image: faceImage\n            };\n            const payloadSize = JSON.stringify(payload).length;\n            console.log('Request payload size:', payloadSize);\n            // Check if payload is too large\n            if (payloadSize > 1000000) {\n                console.warn('Warning: Payload is very large, which may cause issues with the API');\n            }\n            // No mock implementation - always use the real API\n            console.log('Using real face verification API');\n            // Make the real API call\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_BASE_URL, \"/verify-face\"), payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000,\n                withCredentials: false\n            });\n            console.log('Face verification response:', response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Error verifying face:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Face verification failed'\n            };\n        }\n    },\n    /**\r\n   * Get pre-signed URLs for uploading media to S3\r\n   */ getPresignedUrl: async (request)=>{\n        try {\n            console.log('Getting presigned URL with request:', request);\n            const token = getToken();\n            if (!token) {\n                console.warn('No authentication token found when getting presigned URL');\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_BASE_URL, \"/get-upload-url\"), request, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 30000 // 30 seconds timeout\n            });\n            console.log('Presigned URL response:', response.data);\n            // Validate the new multipart response\n            if (!response.data.media_id || !response.data.multipart || !response.data.part_urls || !response.data.upload_id || !response.data.main_key) {\n                throw new Error('Invalid response from get-upload-url API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error getting presigned URL:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to get upload URL'\n            };\n        }\n    },\n    /**\r\n   * Complete the upload process by notifying the backend\r\n   */ completeUpload: async (request)=>{\n        try {\n            console.log('Completing upload with request:', JSON.stringify(request, null, 2));\n            // Validate the request\n            if (!request.media_id) {\n                console.error('Missing media_id in completeUpload request');\n                throw new Error('Missing media_id in request');\n            }\n            const token = getToken();\n            let payload = {\n                ...request\n            };\n            if (request.media_type === 'video' && Array.isArray(request.vendor_details)) {\n                const vendorArr = request.vendor_details;\n                const vendorObj = {};\n                const keys = [\n                    'venue',\n                    'photographer',\n                    'makeup_artist',\n                    'decoration',\n                    'caterer'\n                ];\n                keys.forEach((k, i)=>{\n                    if (vendorArr[i]) {\n                        vendorObj[\"\".concat(k, \"_name\")] = vendorArr[i].name || '';\n                        vendorObj[\"\".concat(k, \"_contact\")] = vendorArr[i].phone || '';\n                    }\n                });\n                for(let i = 5; i < vendorArr.length; i++){\n                    vendorObj[\"additional_vendor_\".concat(i - 4, \"_name\")] = vendorArr[i].name || '';\n                    vendorObj[\"additional_vendor_\".concat(i - 4, \"_contact\")] = vendorArr[i].phone || '';\n                }\n                payload = {\n                    ...payload,\n                    vendor_details: vendorObj\n                };\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_BASE_URL, \"/complete-upload\"), payload, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': token ? \"Bearer \".concat(token) : ''\n                },\n                timeout: 60000\n            });\n            if (!response.data.message) {\n                throw new Error('Invalid response from complete-upload API');\n            }\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Error completing upload:', error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                throw {\n                    error: 'Authentication failed. Please log in again.'\n                };\n            }\n            throw ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data) || {\n                error: 'Failed to complete upload'\n            };\n        }\n    },\n    /**\r\n   * Upload a file to a presigned URL with optimized performance\r\n   */ uploadToPresignedUrl: async (url, file, onProgress)=>{\n        try {\n            console.log('Starting simple direct upload for file:', file.name);\n            console.log('File type:', file.type);\n            console.log('File size:', (file.size / (1024 * 1024)).toFixed(2) + ' MB');\n            console.log('Upload URL:', url);\n            // Report initial progress\n            if (onProgress) onProgress(10);\n            // Start timing the upload\n            const startTime = Date.now();\n            let lastProgressUpdate = 0;\n            // Use simple direct upload for all files\n            console.log('Using simple direct upload');\n            // Create simple headers for direct upload\n            const headers = {\n                'Content-Type': file.type,\n                'Content-Length': file.size.toString()\n            };\n            // Perform simple direct upload to the presigned URL\n            await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].put(url, file, {\n                headers,\n                timeout: 3600000,\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                onUploadProgress: (progressEvent)=>{\n                    if (onProgress && progressEvent.total) {\n                        // Calculate raw percentage\n                        const rawPercent = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                        // Update UI in 5% increments for smoother progress\n                        if (rawPercent >= lastProgressUpdate + 5 || rawPercent === 100) {\n                            lastProgressUpdate = rawPercent;\n                            // Map to 10-95% range for UI\n                            const percentCompleted = 10 + Math.floor(rawPercent * 85 / 100);\n                            onProgress(percentCompleted);\n                            // Calculate and log upload speed\n                            const elapsedSeconds = (Date.now() - startTime) / 1000;\n                            if (elapsedSeconds > 0) {\n                                const speedMBps = (progressEvent.loaded / elapsedSeconds / (1024 * 1024)).toFixed(2);\n                                console.log(\"Upload progress: \".concat(percentCompleted, \"% at \").concat(speedMBps, \"MB/s\"));\n                            }\n                        }\n                    }\n                }\n            });\n            // Calculate final stats\n            const endTime = Date.now();\n            const elapsedSeconds = (endTime - startTime) / 1000;\n            const uploadSpeed = (file.size / elapsedSeconds / (1024 * 1024)).toFixed(2);\n            console.log(\"Upload completed in \".concat(elapsedSeconds.toFixed(2), \"s at \").concat(uploadSpeed, \"MB/s\"));\n            console.log('Response status: 200 (success)');\n            // Report completion\n            if (onProgress) onProgress(100);\n            console.log('File uploaded successfully');\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            // Provide more detailed error information\n            let errorMessage = 'Failed to upload file';\n            if (error.message) {\n                if (error.message.includes('Network Error') || error.message.includes('CORS')) {\n                    errorMessage = 'Network error or CORS issue. Please try again or contact support.';\n                } else {\n                    errorMessage = \"Upload error: \".concat(error.message);\n                }\n            }\n            if (error.response) {\n                console.error('Response status:', error.response.status);\n                console.error('Response headers:', error.response.headers);\n                console.error('Response data:', error.response.data);\n                if (error.response.status === 403) {\n                    errorMessage = 'Permission denied. The upload URL may have expired.';\n                }\n            }\n            throw {\n                error: errorMessage\n            };\n        }\n    },\n    /**\r\n   * Handle the complete upload process\r\n   */ handleUpload: async (file, mediaType, category, description, tags, details, duration, thumbnail, onProgress)=>{\n        try {\n            console.log('API SERVICE - handleUpload called with params:', {\n                fileName: file === null || file === void 0 ? void 0 : file.name,\n                fileSize: Math.round(file.size / (1024 * 1024) * 100) / 100 + ' MB',\n                mediaType,\n                category,\n                description,\n                tagsCount: tags === null || tags === void 0 ? void 0 : tags.length,\n                detailsCount: details ? Object.keys(details).length : 0,\n                videoCategory: (details === null || details === void 0 ? void 0 : details.video_category) || 'Not set',\n                duration,\n                hasThumbnail: !!thumbnail\n            });\n            // Log the video_category from details\n            if (mediaType === 'video') {\n                console.log('API SERVICE - Video category from details:', details === null || details === void 0 ? void 0 : details.video_category);\n                console.log('API SERVICE - All detail fields:', details ? JSON.stringify(details) : 'No details');\n            }\n            if (!file) {\n                throw new Error('No file provided');\n            }\n            // Log the file size and selected category without overriding the user's selection\n            if (mediaType === 'video') {\n                const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[category] || 250;\n                console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB, Selected category: \").concat(category));\n                console.log(\"API SERVICE - Size limit for \".concat(category, \": \").concat(sizeLimit, \" MB\"));\n            // Warning is now handled in the presignedUrlRequest section\n            }\n            console.log('API SERVICE - Starting upload process for:', file.name);\n            // Update progress to 10%\n            if (onProgress) onProgress(10);\n            // Step 1: Get presigned URLs\n            console.log('API SERVICE - Creating presigned URL request with category:', category);\n            // Ensure we're using the correct backend category names\n            // Frontend: flashes, glimpses, movies, moments\n            // Backend: flash, glimpse, movie, story\n            let backendCategory = mediaType === 'photo' ? category === 'story' ? 'story' : 'post' // For photos: either 'story' or 'post'\n             : category; // For videos: keep the original category\n            // Double-check that we have a valid category\n            const validCategories = [\n                'story',\n                'flash',\n                'glimpse',\n                'movie',\n                'post'\n            ];\n            if (!validCategories.includes(backendCategory)) {\n                console.log(\"API SERVICE - WARNING: Invalid category '\".concat(backendCategory, \"'. Using 'flash' as fallback.\"));\n                console.log(\"API SERVICE - Valid categories are: \".concat(validCategories.join(', ')));\n                console.log(\"API SERVICE - Category type: \".concat(typeof backendCategory));\n                console.log(\"API SERVICE - Category value: '\".concat(backendCategory, \"'\"));\n                // Use 'flash' as the default for videos instead of 'glimpse'\n                backendCategory = 'flash';\n            }\n            console.log(\"API SERVICE - Original category from context: \".concat(category));\n            console.log(\"API SERVICE - Using backend category: \".concat(backendCategory));\n            // Log the file size to help with debugging\n            const fileSizeMB = Math.round(file.size / (1024 * 1024) * 100) / 100;\n            console.log(\"API SERVICE - File size: \".concat(fileSizeMB, \" MB\"));\n            // Log the category limits\n            const categoryLimits = {\n                'story': 50,\n                'flash': 100,\n                'glimpse': 250,\n                'movie': 2000\n            };\n            const sizeLimit = categoryLimits[backendCategory] || 250;\n            console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB\"));\n            // Log a warning if the file size exceeds the limit\n            if (fileSizeMB > sizeLimit) {\n                console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n            }\n            // When constructing presignedUrlRequest for photo, set media_subtype to null\n            const presignedUrlRequest = {\n                media_type: mediaType,\n                media_subtype: mediaType === 'photo' ? null : backendCategory,\n                filename: file.name,\n                content_type: file.type,\n                file_size: file.size\n            };\n            // Log the presigned URL request\n            console.log(\"API SERVICE - Sending presigned URL request with media_subtype: \".concat(backendCategory));\n            // Add video_category for videos\n            if (mediaType === 'video') {\n                // Get the video_category from details - no default value\n                console.log('API SERVICE - Details object:', details);\n                console.log('API SERVICE - Details keys:', details ? Object.keys(details) : 'No details');\n                const videoCategory = details === null || details === void 0 ? void 0 : details.video_category;\n                console.log('API SERVICE - Video category from details:', videoCategory);\n                // Make sure we're using a valid video_category\n                const allowedCategories = [\n                    'my_wedding',\n                    'wedding_vlog'\n                ];\n                // If no video_category is provided, use a default one\n                if (!videoCategory) {\n                    console.error(\"API SERVICE - Missing video_category. Using default 'my_wedding'.\");\n                    // Use 'my_wedding' as the default video_category\n                    presignedUrlRequest.video_category = 'my_wedding';\n                    console.log('API SERVICE - Using default video_category: my_wedding');\n                } else {\n                    // Map the UI category to the backend category if needed\n                    let backendVideoCategory = videoCategory;\n                    // If the category is in UI format, map it to backend format\n                    if (videoCategory === 'my_wedding_videos') {\n                        backendVideoCategory = 'my_wedding';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    } else if (videoCategory === 'wedding_vlog_videos') {\n                        backendVideoCategory = 'wedding_vlog';\n                        console.log('API SERVICE - Mapped UI category to backend category:', backendVideoCategory);\n                    }\n                    // If the video_category is not allowed, throw an error\n                    if (!allowedCategories.includes(backendVideoCategory)) {\n                        console.error(\"API SERVICE - Invalid video_category: \".concat(backendVideoCategory, \". Must be one of \").concat(JSON.stringify(allowedCategories)));\n                        throw new Error(\"Invalid video category. Must be one of \".concat(JSON.stringify(allowedCategories)));\n                    }\n                    // Use the provided video_category\n                    presignedUrlRequest.video_category = backendVideoCategory;\n                    console.log('API SERVICE - Using video_category:', backendVideoCategory);\n                    console.log(\"API SERVICE - Final video category: \".concat(backendVideoCategory));\n                    console.log(\"API SERVICE - Complete presigned URL request:\", JSON.stringify(presignedUrlRequest, null, 2));\n                }\n                // Log the category and file size limits\n                console.log(\"API SERVICE - Original category from UI: \".concat(category));\n                console.log(\"API SERVICE - Using backend media subtype: \".concat(backendCategory));\n                // Log size limits based on category without overriding the user's selection\n                const categoryLimits = {\n                    'story': 50,\n                    'flash': 100,\n                    'glimpse': 250,\n                    'movie': 2000\n                };\n                const sizeLimit = categoryLimits[backendCategory] || 250;\n                console.log(\"API SERVICE - Size limit for \".concat(backendCategory, \": \").concat(sizeLimit, \" MB (file size: \").concat(fileSizeMB, \" MB)\"));\n                // Log a warning if the file size exceeds the limit for the selected category\n                if (fileSizeMB > sizeLimit) {\n                    console.log(\"API SERVICE - WARNING: File size (\".concat(fileSizeMB, \" MB) exceeds the limit for \").concat(backendCategory, \" (\").concat(sizeLimit, \" MB).\"));\n                    console.log(\"API SERVICE - The upload may fail. Consider selecting a category with a higher size limit.\");\n                }\n                // Add duration if available\n                if (duration) {\n                    console.log(\"Video duration: \".concat(duration, \" seconds\"));\n                // You could add this to the request if the API supports it\n                // presignedUrlRequest.duration = duration;\n                }\n            }\n            const presignedUrlResponse = await uploadService.getPresignedUrl(presignedUrlRequest);\n            // Update progress to 20%\n            if (onProgress) onProgress(20);\n            // Step 2: Upload the file using multipart upload (20-70% of progress)\n            const parts = await (0,_utils_s3MultipartUpload__WEBPACK_IMPORTED_MODULE_0__.multipartUpload)(file, presignedUrlResponse.part_urls, (progress)=>{\n                // Map the upload progress from 0-100 to 20-70 in our overall progress\n                if (onProgress) onProgress(20 + progress.percentage * 0.5);\n            });\n            // Complete multipart upload\n            await uploadService.completeMultipartUpload({\n                media_id: presignedUrlResponse.media_id,\n                upload_id: presignedUrlResponse.upload_id,\n                parts: parts\n            });\n            // Step 3: Upload thumbnail if available (70-90% of progress)\n            if (thumbnail && presignedUrlResponse.thumbnail_url) {\n                console.log('Uploading thumbnail:', thumbnail.name);\n                // Update progress to 70%\n                if (onProgress) onProgress(70);\n                await uploadService.uploadToPresignedUrl(presignedUrlResponse.thumbnail_url, thumbnail, (uploadProgress)=>{\n                    // Map the upload progress from 0-100 to 70-90 in our overall progress\n                    if (onProgress) onProgress(70 + uploadProgress * 0.2);\n                });\n            }\n            // Update progress to 90%\n            if (onProgress) onProgress(90);\n            // Step 4: Complete the upload (90-100% of progress)\n            try {\n                // When constructing completeRequest for photo, do not include title\n                const completeRequest = {\n                    media_id: presignedUrlResponse.media_id,\n                    media_type: mediaType,\n                    media_subtype: mediaType === 'photo' ? null : backendCategory,\n                    description: description || '',\n                    tags: tags || []\n                };\n                console.log(\"API SERVICE - Setting media_subtype in completeRequest: \".concat(backendCategory));\n                // Log the description being sent\n                console.log('Sending description:', description || '');\n                // Add duration for videos if available\n                if (mediaType === 'video' && duration) {\n                    completeRequest.duration = duration;\n                }\n                // Extract personal details from the details object\n                const personalDetails = {\n                    caption: (details === null || details === void 0 ? void 0 : details.caption) || '',\n                    life_partner: (details === null || details === void 0 ? void 0 : details.personal_life_partner) || (details === null || details === void 0 ? void 0 : details.lifePartner) || '',\n                    wedding_style: (details === null || details === void 0 ? void 0 : details.personal_wedding_style) || description || '',\n                    place: (details === null || details === void 0 ? void 0 : details.personal_place) || (details === null || details === void 0 ? void 0 : details.location) || ''\n                };\n                // Extract vendor details from the details object\n                const vendorDetails = {};\n                // Process vendor details from the details object\n                if (details) {\n                    console.log('API SERVICE - Processing vendor details from details object');\n                    console.log('API SERVICE - Raw details object:', JSON.stringify(details, null, 2));\n                    // Count vendor fields in the details object\n                    let vendorFieldCount = 0;\n                    Object.keys(details).forEach((key)=>{\n                        if (key.startsWith('vendor_')) {\n                            vendorFieldCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Found \".concat(vendorFieldCount, \" vendor-related fields in details object\"));\n                    // Keep track of which vendor types we've already processed\n                    const processedVendors = new Set();\n                    Object.entries(details).forEach((param)=>{\n                        let [key, value] = param;\n                        if (key.startsWith('vendor_') && value) {\n                            const parts = key.split('_');\n                            if (parts.length >= 3) {\n                                const vendorType = parts[1];\n                                const fieldType = parts.slice(2).join('_');\n                                // Normalize vendor type to avoid duplicates\n                                const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                console.log(\"API SERVICE - Processing vendor field: \".concat(key, \" = \").concat(value), {\n                                    vendorType,\n                                    fieldType,\n                                    normalizedType,\n                                    alreadyProcessed: processedVendors.has(normalizedType)\n                                });\n                                // Skip if we've already processed this vendor type\n                                if (processedVendors.has(normalizedType)) {\n                                    console.log(\"API SERVICE - Skipping \".concat(key, \" as \").concat(normalizedType, \" is already processed\"));\n                                    return;\n                                }\n                                if (fieldType === 'name') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_name = \").concat(value));\n                                    // Also set the contact if available\n                                    const contactKey = \"vendor_\".concat(vendorType, \"_contact\");\n                                    if (details[contactKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details[contactKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_contact = \").concat(details[contactKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                } else if (fieldType === 'contact') {\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = value;\n                                    console.log(\"API SERVICE - Set \".concat(normalizedType, \"_contact = \").concat(value));\n                                    // Also set the name if available\n                                    const nameKey = \"vendor_\".concat(vendorType, \"_name\");\n                                    if (details[nameKey]) {\n                                        vendorDetails[\"\".concat(normalizedType, \"_name\")] = details[nameKey];\n                                        console.log(\"API SERVICE - Also set \".concat(normalizedType, \"_name = \").concat(details[nameKey]));\n                                        processedVendors.add(normalizedType);\n                                        console.log(\"API SERVICE - Marked \".concat(normalizedType, \" as processed (has both name and contact)\"));\n                                    }\n                                }\n                            }\n                        }\n                    });\n                    // Log the processed vendor details\n                    // console.log('API SERVICE - Processed vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // console.log(`API SERVICE - Processed ${processedVendors.size} complete vendor types: ${Array.from(processedVendors).join(', ')}`);\n                    // Final check to ensure we have both name and contact for each vendor\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    Object.keys(vendorDetails).forEach((key)=>{\n                        if (key.endsWith('_name')) {\n                            vendorNames.add(key.replace('_name', ''));\n                        } else if (key.endsWith('_contact')) {\n                            vendorContacts.add(key.replace('_contact', ''));\n                        }\n                    });\n                    // Count complete pairs\n                    vendorNames.forEach((name)=>{\n                        if (vendorContacts.has(name)) {\n                            completeVendorCount++;\n                        } else {\n                            // If we have a name but no contact, add a default contact\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(name, \" has name but no contact, adding default contact\"));\n                            vendorDetails[\"\".concat(name, \"_contact\")] = '0000000000';\n                            completeVendorCount++;\n                        }\n                    });\n                    // Check for contacts without names\n                    vendorContacts.forEach((contact)=>{\n                        if (!vendorNames.has(contact)) {\n                            // If we have a contact but no name, add a default name\n                            console.log(\"API SERVICE - WARNING: Vendor \".concat(contact, \" has contact but no name, adding default name\"));\n                            if (typeof contact === 'string') {\n                                vendorDetails[\"\".concat(contact, \"_name\")] = \"Vendor \".concat(contact.charAt(0).toUpperCase() + contact.slice(1));\n                            }\n                            completeVendorCount++;\n                        }\n                    });\n                    console.log(\"API SERVICE - Final check: Found \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    console.log('API SERVICE - Final vendor details:', JSON.stringify(vendorDetails, null, 2));\n                }\n                // Ensure we have the required vendor fields for wedding videos\n                if (mediaType === 'video' && (details === null || details === void 0 ? void 0 : details.video_category) && [\n                    'my_wedding',\n                    'wedding_vlog'\n                ].includes(details.video_category)) {\n                    // Try to get vendor details from localStorage if they're missing from the details object\n                    const initialVendorKeys = Object.keys(vendorDetails);\n                    const vendorCount = initialVendorKeys.filter((key)=>key.endsWith('_name')).length;\n                    console.log(\"API SERVICE - Initial vendor count: \".concat(vendorCount, \" name fields found\"));\n                    console.log('API SERVICE - Initial vendor details:', JSON.stringify(vendorDetails, null, 2));\n                    // Always check localStorage for vendor details to ensure we have the most complete set\n                    console.log('API SERVICE - Checking localStorage for vendor details');\n                    try {\n                        const storedVendorDetails = localStorage.getItem('wedzat_vendor_details');\n                        if (storedVendorDetails) {\n                            const parsedVendorDetails = JSON.parse(storedVendorDetails);\n                            console.log('API SERVICE - Retrieved vendor details from localStorage:', storedVendorDetails);\n                            // Track how many complete vendor details we've added\n                            let completeVendorCount = 0;\n                            // Process vendor details from localStorage\n                            Object.entries(parsedVendorDetails).forEach((param)=>{\n                                let [vendorType, details] = param;\n                                if (details && details.name && details.mobileNumber) {\n                                    // Add to vendorDetails\n                                    const normalizedType = vendorType === 'makeupArtist' ? 'makeup_artist' : vendorType === 'decorations' ? 'decoration' : vendorType;\n                                    vendorDetails[\"\".concat(normalizedType, \"_name\")] = details.name;\n                                    vendorDetails[\"\".concat(normalizedType, \"_contact\")] = details.mobileNumber;\n                                    console.log(\"API SERVICE - Added vendor \".concat(normalizedType, \" with name: \").concat(details.name, \" and contact: \").concat(details.mobileNumber));\n                                    completeVendorCount++;\n                                    // Also add the original type if it's different\n                                    if (normalizedType !== vendorType) {\n                                        vendorDetails[\"\".concat(vendorType, \"_name\")] = details.name;\n                                        vendorDetails[\"\".concat(vendorType, \"_contact\")] = details.mobileNumber;\n                                        console.log(\"API SERVICE - Also added original vendor \".concat(vendorType));\n                                    }\n                                }\n                            });\n                            console.log(\"API SERVICE - Added \".concat(completeVendorCount, \" complete vendor details from localStorage\"));\n                            console.log('API SERVICE - Updated vendor details:', JSON.stringify(vendorDetails, null, 2));\n                            // Force update the state with these vendor details\n                            try {\n                                // This is a direct state update to ensure the vendor details are available for validation\n                                if (window && window.dispatchEvent) {\n                                    const vendorUpdateEvent = new CustomEvent('vendor-details-update', {\n                                        detail: parsedVendorDetails\n                                    });\n                                    window.dispatchEvent(vendorUpdateEvent);\n                                    console.log('API SERVICE - Dispatched vendor-details-update event');\n                                }\n                            } catch (eventError) {\n                                console.error('API SERVICE - Failed to dispatch vendor-details-update event:', eventError);\n                            }\n                        } else {\n                            console.log('API SERVICE - No vendor details found in localStorage');\n                        }\n                    } catch (error) {\n                        console.error('API SERVICE - Failed to retrieve vendor details from localStorage:', error);\n                    }\n                    // Make sure we have at least 4 vendor details with both name and contact\n                    // Define required vendor types for fallback if needed\n                    const requiredVendorTypes = [\n                        'venue',\n                        'photographer',\n                        'makeup_artist',\n                        'decoration',\n                        'caterer'\n                    ];\n                    // Count all vendor details, including additional ones\n                    let validVendorCount = 0;\n                    // Count all vendor details where both name and contact are provided\n                    console.log('API SERVICE - Checking vendor details for validation:', JSON.stringify(vendorDetails, null, 2));\n                    const allVendorKeys = Object.keys(vendorDetails);\n                    console.log('API SERVICE - Vendor keys:', allVendorKeys.join(', '));\n                    // Keep track of which vendors we've already counted to avoid duplicates\n                    const countedVendors = new Set();\n                    // First pass: Check for standard vendor types\n                    for(let i = 0; i < allVendorKeys.length; i++){\n                        const key = allVendorKeys[i];\n                        if (key.endsWith('_name')) {\n                            const baseKey = key.replace('_name', '');\n                            const contactKey = \"\".concat(baseKey, \"_contact\");\n                            // Normalize the key to handle both frontend and backend naming\n                            const normalizedKey = baseKey === 'makeupArtist' ? 'makeup_artist' : baseKey;\n                            // Skip if we've already counted this vendor\n                            if (countedVendors.has(normalizedKey)) {\n                                continue;\n                            }\n                            console.log(\"Checking vendor \".concat(baseKey, \":\"), {\n                                name: vendorDetails[key],\n                                contact: vendorDetails[contactKey]\n                            });\n                            if (vendorDetails[key] && vendorDetails[contactKey]) {\n                                validVendorCount++;\n                                countedVendors.add(normalizedKey);\n                                console.log(\"Valid vendor found: \".concat(baseKey, \" with name: \").concat(vendorDetails[key], \" and contact: \").concat(vendorDetails[contactKey]));\n                            }\n                        }\n                    }\n                    console.log(\"Total valid vendor count after first pass: \".concat(validVendorCount));\n                    // Second pass: Check for additional vendors with different naming patterns\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additional\".concat(i, \"_name\");\n                        const contactKey = \"additional\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additional\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additional\".concat(i));\n                            console.log(\"Valid additional vendor found: additional\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    // Third pass: Check for additionalVendor pattern (used in some parts of the code)\n                    for(let i = 1; i <= 10; i++){\n                        const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                        const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                        // Skip if we've already counted this vendor\n                        if (countedVendors.has(\"additionalVendor\".concat(i))) {\n                            continue;\n                        }\n                        if (vendorDetails[nameKey] && vendorDetails[contactKey]) {\n                            validVendorCount++;\n                            countedVendors.add(\"additionalVendor\".concat(i));\n                            console.log(\"Valid additionalVendor found: additionalVendor\".concat(i, \" with name: \").concat(vendorDetails[nameKey], \" and contact: \").concat(vendorDetails[contactKey]));\n                        }\n                    }\n                    console.log(\"Total valid vendor count after all passes: \".concat(validVendorCount));\n                    // Map additional vendors to the predefined vendor types if needed\n                    // This ensures the backend will count them correctly\n                    if (validVendorCount < 4) {\n                        console.log('Need to map additional vendors to predefined types');\n                        // First, collect all additional vendors from all patterns\n                        const additionalVendors = [];\n                        // Collect all additional vendors with 'additional' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additional\".concat(i, \"_name\");\n                            const contactKey = \"additional\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additional\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additional\".concat(i)\n                                });\n                                countedVendors.add(\"additional\".concat(i));\n                                console.log(\"Found additional vendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        // Collect all additional vendors with 'additionalVendor' prefix\n                        for(let i = 1; i <= 10; i++){\n                            const nameKey = \"additionalVendor\".concat(i, \"_name\");\n                            const contactKey = \"additionalVendor\".concat(i, \"_contact\");\n                            if (vendorDetails[nameKey] && vendorDetails[contactKey] && !countedVendors.has(\"additionalVendor\".concat(i))) {\n                                additionalVendors.push({\n                                    name: vendorDetails[nameKey],\n                                    contact: vendorDetails[contactKey],\n                                    key: \"additionalVendor\".concat(i)\n                                });\n                                countedVendors.add(\"additionalVendor\".concat(i));\n                                console.log(\"Found additionalVendor \".concat(i, \": \").concat(vendorDetails[nameKey]));\n                            }\n                        }\n                        console.log(\"Found \".concat(additionalVendors.length, \" additional vendors to map\"));\n                        // Map additional vendors to empty predefined vendor slots\n                        let additionalIndex = 0;\n                        for (const type of requiredVendorTypes){\n                            // Check if this type is already filled\n                            const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                            // Skip if we've already counted this vendor type\n                            if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                console.log(\"Skipping \".concat(type, \" as it's already counted\"));\n                                continue;\n                            }\n                            if (additionalIndex < additionalVendors.length) {\n                                // Map this additional vendor to this predefined type\n                                vendorDetails[\"\".concat(type, \"_name\")] = additionalVendors[additionalIndex].name;\n                                vendorDetails[\"\".concat(type, \"_contact\")] = additionalVendors[additionalIndex].contact;\n                                console.log(\"Mapped additional vendor \".concat(additionalVendors[additionalIndex].key, \" to \").concat(type, \": \").concat(additionalVendors[additionalIndex].name));\n                                additionalIndex++;\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after mapping, no need to continue\");\n                                    break;\n                                }\n                            }\n                        }\n                        // If we still don't have enough, add placeholders\n                        if (validVendorCount < 4) {\n                            console.log('Still need more vendors, adding placeholders');\n                            for (const type of requiredVendorTypes){\n                                // Check if this type is already filled\n                                const normalizedType = type === 'makeup_artist' ? 'makeupArtist' : type === 'decoration' ? 'decorations' : type;\n                                // Skip if we've already counted this vendor type\n                                if (countedVendors.has(type) || countedVendors.has(normalizedType)) {\n                                    console.log(\"Skipping \".concat(type, \" for placeholder as it's already counted\"));\n                                    continue;\n                                }\n                                // Add placeholder for this vendor type\n                                vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                                validVendorCount++;\n                                countedVendors.add(type);\n                                console.log(\"Added placeholder for \".concat(type, \": \").concat(vendorDetails[\"\".concat(type, \"_name\")]));\n                                if (validVendorCount >= 4) {\n                                    console.log(\"Reached 4 valid vendors after adding placeholders\");\n                                    break;\n                                }\n                            }\n                            // Final check - if we still don't have 4, force add the remaining required types\n                            if (validVendorCount < 4) {\n                                console.log('CRITICAL: Still don\\'t have 4 vendors, forcing placeholders for all required types');\n                                for (const type of requiredVendorTypes){\n                                    if (!vendorDetails[\"\".concat(type, \"_name\")] || !vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                        vendorDetails[\"\".concat(type, \"_name\")] = \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                                        vendorDetails[\"\".concat(type, \"_contact\")] = '0000000000';\n                                        validVendorCount++;\n                                        console.log(\"Force added placeholder for \".concat(type));\n                                        if (validVendorCount >= 4) break;\n                                    }\n                                }\n                            }\n                        }\n                        // Final log of vendor count\n                        console.log(\"Final valid vendor count: \".concat(validVendorCount));\n                    }\n                }\n                // Add the details to the request\n                completeRequest.personal_details = personalDetails;\n                completeRequest.vendor_details = vendorDetails;\n                // Final check before sending - ensure we have at least 4 complete vendor details\n                if (mediaType === 'video') {\n                    // Count complete vendor details (with both name and contact)\n                    const vendorNames = new Set();\n                    const vendorContacts = new Set();\n                    let completeVendorCount = 0;\n                    if (vendorDetails) {\n                        Object.keys(vendorDetails).forEach((key)=>{\n                            if (key.endsWith('_name')) {\n                                vendorNames.add(key.replace('_name', ''));\n                            } else if (key.endsWith('_contact')) {\n                                vendorContacts.add(key.replace('_contact', ''));\n                            }\n                        });\n                        // Count complete pairs\n                        vendorNames.forEach((name)=>{\n                            if (vendorContacts.has(name)) {\n                                completeVendorCount++;\n                            }\n                        });\n                    }\n                    console.log(\"API SERVICE - FINAL CHECK: Found \".concat(completeVendorCount, \" complete vendor pairs before sending request\"));\n                    // If we don't have enough vendors, add placeholders to reach 4\n                    if (completeVendorCount < 4) {\n                        console.log(\"API SERVICE - FINAL CHECK: Adding placeholders to reach 4 vendors\");\n                        const requiredVendorTypes = [\n                            'venue',\n                            'photographer',\n                            'makeup_artist',\n                            'decoration',\n                            'caterer'\n                        ];\n                        for (const type of requiredVendorTypes){\n                            // Skip if this vendor is already complete\n                            if (vendorDetails[\"\".concat(type, \"_name\")] && vendorDetails[\"\".concat(type, \"_contact\")]) {\n                                continue;\n                            }\n                            // Add placeholder for this vendor\n                            vendorDetails[\"\".concat(type, \"_name\")] = vendorDetails[\"\".concat(type, \"_name\")] || \"\".concat(type.charAt(0).toUpperCase() + type.slice(1));\n                            vendorDetails[\"\".concat(type, \"_contact\")] = vendorDetails[\"\".concat(type, \"_contact\")] || '0000000000';\n                            completeVendorCount++;\n                            console.log(\"API SERVICE - FINAL CHECK: Added placeholder for \".concat(type));\n                            if (completeVendorCount >= 4) {\n                                break;\n                            }\n                        }\n                        // Update the request with the new vendor details\n                        completeRequest.vendor_details = vendorDetails;\n                        console.log(\"API SERVICE - FINAL CHECK: Now have \".concat(completeVendorCount, \" complete vendor pairs\"));\n                    }\n                }\n                console.log('Sending complete upload request:', JSON.stringify(completeRequest, null, 2));\n                // Complete the upload\n                const completeResponse = await uploadService.completeUpload(completeRequest);\n                // Log the complete response\n                console.log('Complete upload response:', JSON.stringify(completeResponse, null, 2));\n                // Update progress to 100%\n                if (onProgress) onProgress(100);\n                console.log('Upload process completed successfully');\n                return completeResponse;\n            } catch (completeError) {\n                var _completeError_response_data, _completeError_response;\n                console.error('Error in complete upload step:', completeError);\n                // Handle other errors\n                const errorMessage = ((_completeError_response = completeError.response) === null || _completeError_response === void 0 ? void 0 : (_completeError_response_data = _completeError_response.data) === null || _completeError_response_data === void 0 ? void 0 : _completeError_response_data.error) || completeError.message || 'Failed to complete the upload process';\n                console.error('Throwing error:', errorMessage);\n                throw {\n                    error: errorMessage\n                };\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Error in upload process:', error);\n            // If error is already formatted correctly, just pass it through\n            if (error.error) {\n                throw error;\n            }\n            // Otherwise, format the error\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Upload process failed'\n            };\n        }\n    },\n    /**\r\n   * Complete the multipart upload by notifying the backend with part ETags\r\n   */ completeMultipartUpload: async (param)=>{\n        let { media_id, upload_id, parts } = param;\n        try {\n            const token = getToken();\n            if (!token) throw {\n                error: 'No authentication token found'\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_BASE_URL, \"/complete-multipart-upload\"), {\n                media_id,\n                upload_id,\n                parts\n            }, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(token)\n                },\n                timeout: 60000\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            console.error('Error completing multipart upload:', error);\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to complete multipart upload'\n            };\n        }\n    }\n};\n// Like/Unlike API for flashes (shorts page)\nconst API_URL = \"https://uny4cdswka.execute-api.ap-south-1.amazonaws.com/test/hub\" || 0;\nconst flashesService = {\n    like: async (mediaId)=>{\n        try {\n            const token =  true ? localStorage.getItem('token') : 0;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_URL, \"/like\"), {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to like flash'\n            };\n        }\n    },\n    unlike: async (mediaId)=>{\n        try {\n            const token =  true ? localStorage.getItem('token') : 0;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_URL, \"/unlike\"), {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to unlike flash'\n            };\n        }\n    },\n    view: async (mediaId)=>{\n        try {\n            const token =  true ? localStorage.getItem('token') : 0;\n            if (!token) throw new Error('No authentication token found');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_URL, \"/view\"), {\n                content_id: mediaId,\n                content_type: 'flash'\n            }, {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            return response.data;\n        } catch (error) {\n            var _error_response;\n            throw ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || {\n                error: 'Failed to register view'\n            };\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    authService,\n    userService,\n    uploadService,\n    isAuthenticated,\n    flashesService\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./services/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./utils/s3MultipartUpload.ts":
/*!************************************!*\
  !*** ./utils/s3MultipartUpload.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multipartUpload: () => (/* binding */ multipartUpload)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\n// Constants for multipart upload\nconst CHUNK_SIZE = 120 * 1024 * 1024; // 120MB chunks (minimum size as requested)\nconst MAX_CONCURRENT_UPLOADS = 6; // Increased number of concurrent chunk uploads for better performance\n/**\r\n * Handles multipart upload of large files directly to S3 using provided part URLs\r\n */ async function multipartUpload(file, partUrls, onProgress) {\n    const totalParts = partUrls.length;\n    let uploadedBytes = 0;\n    let startTime = Date.now();\n    let uploadSpeed = 0;\n    const partSize = Math.ceil(file.size / totalParts);\n    const uploadedParts = [];\n    for(let i = 0; i < totalParts; i++){\n        const { part_number, url } = partUrls[i];\n        const start = (part_number - 1) * partSize;\n        const end = i === totalParts - 1 ? file.size : start + partSize;\n        const chunk = file.slice(start, end);\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, chunk, {\n                headers: {\n                    'Content-Type': 'application/octet-stream',\n                    'Content-Length': chunk.size.toString()\n                },\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                timeout: 300000,\n                onUploadProgress: (progressEvent)=>{\n                    if (progressEvent.loaded && onProgress) {\n                        uploadedBytes += progressEvent.loaded;\n                        const elapsedSeconds = (Date.now() - startTime) / 1000;\n                        uploadSpeed = elapsedSeconds > 0 ? uploadedBytes / elapsedSeconds : 0;\n                        onProgress({\n                            totalBytes: file.size,\n                            uploadedBytes,\n                            percentage: Math.round(uploadedBytes / file.size * 100),\n                            speed: uploadSpeed\n                        });\n                    }\n                }\n            });\n            const eTag = response.headers['etag'] || response.headers['ETag'];\n            uploadedParts.push({\n                PartNumber: part_number,\n                ETag: (eTag === null || eTag === void 0 ? void 0 : eTag.replaceAll('\"', '')) || ''\n            });\n        } catch (error) {\n            console.error(\"Error uploading part \".concat(part_number, \":\"), error.message);\n            throw error;\n        }\n    }\n    return uploadedParts;\n}\n/**\r\n * Uploads a single chunk to S3\r\n */ async function uploadChunk(chunk, baseUrl, queryParams, chunkNumber, totalChunks, onComplete) {\n    // Add chunk information to query parameters\n    const chunkParams = {\n        ...queryParams,\n        partNumber: (chunkNumber + 1).toString(),\n        uploadId: queryParams.uploadId || 'direct-upload'\n    };\n    // Build the URL with query parameters\n    const queryString = Object.entries(chunkParams).map((param)=>{\n        let [key, value] = param;\n        return \"\".concat(encodeURIComponent(key), \"=\").concat(encodeURIComponent(value));\n    }).join('&');\n    const uploadUrl = \"\".concat(baseUrl, \"?\").concat(queryString);\n    try {\n        // Upload the chunk\n        console.log(\"Uploading chunk \".concat(chunkNumber + 1, \"/\").concat(totalChunks, \" (\").concat((chunk.size / (1024 * 1024)).toFixed(2), \"MB)\"));\n        try {\n            // First try direct upload\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(uploadUrl, chunk, {\n                headers: {\n                    'Content-Type': 'application/octet-stream',\n                    'Content-Length': chunk.size.toString(),\n                    // Add CORS headers\n                    'Origin': window.location.origin,\n                    'Access-Control-Request-Method': 'PUT'\n                },\n                maxBodyLength: Infinity,\n                maxContentLength: Infinity,\n                timeout: 300000\n            });\n            console.log(\"Chunk \".concat(chunkNumber + 1, \"/\").concat(totalChunks, \" uploaded successfully via direct upload\"));\n            onComplete();\n        } catch (directError) {\n            // Check if this is a CORS error\n            const isCorsError = directError.message.includes('CORS') || directError.message.includes('Network Error') || directError.message.includes('Failed to fetch') || directError.message.includes('cross-origin');\n            if (isCorsError) {\n                console.log(\"CORS error detected for chunk \".concat(chunkNumber + 1, \". Falling back to proxy upload.\"));\n                // Use the proxy API instead\n                const proxyUrl = \"/api/upload-proxy?url=\".concat(encodeURIComponent(uploadUrl));\n                // Create a FormData object to send the chunk\n                const formData = new FormData();\n                formData.append('file', chunk);\n                // Send the chunk to our proxy API\n                await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(proxyUrl, formData, {\n                    headers: {\n                        'Content-Type': 'multipart/form-data'\n                    },\n                    maxBodyLength: Infinity,\n                    maxContentLength: Infinity,\n                    timeout: 600000\n                });\n                console.log(\"Chunk \".concat(chunkNumber + 1, \"/\").concat(totalChunks, \" uploaded successfully via proxy\"));\n                onComplete();\n            } else {\n                // Not a CORS error, rethrow\n                throw directError;\n            }\n        }\n    } catch (error) {\n        console.error(\"Error uploading chunk \".concat(chunkNumber + 1, \"/\").concat(totalChunks, \":\"), error.message);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./utils/s3MultipartUpload.ts\n"));

/***/ })

});