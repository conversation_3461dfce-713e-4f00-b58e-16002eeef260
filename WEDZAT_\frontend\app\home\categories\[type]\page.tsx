"use client";
import { useParams, useRouter } from 'next/navigation';
import { businessSubtypesMap } from '../../../../config/constants';

export default function CategoryTypePage() {
  const params = useParams();
  const router = useRouter();
  const type = params && typeof params === 'object' && 'type' in params && params.type ? String(params.type) : '';
  const subtypes = businessSubtypesMap[type] || [];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">{type} Subtypes</h1>
      <div className="flex gap-2 mb-6 flex-wrap">
        {subtypes.map(sub => (
          <button
            key={sub.subtype_id}
            className="p-2 border rounded bg-white hover:bg-red-100"
            onClick={() => router.push(`/home/<USER>/${encodeURIComponent(type)}/${encodeURIComponent(sub.name.replace(/\s+/g, '').toLowerCase())}`)}
          >
            {sub.name}
          </button>
        ))}
      </div>
    </div>
  );
} 